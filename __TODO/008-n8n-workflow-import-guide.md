# n8n Workflow Import Instructions

## Prerequisites

Before importing the workflows, ensure you have:
1. n8n instance running (local or cloud)
2. Admin access to create workflows
3. Required integrations set up

## Step 1: Set Up Credentials

You need to create the following credentials in n8n:

### 1. Slack OAuth2
1. Go to Settings → Credentials → Add Credential
2. Select "Slack OAuth2"
3. Follow the OAuth2 flow to authenticate
4. Required scopes:
   - `app_mentions:read` (for trigger)
   - `chat:write` (for sending messages)
   - `channels:read` (for channel info)

### 2. Google Calendar OAuth2
1. Go to Settings → Credentials → Add Credential
2. Select "Google Calendar OAuth2"
3. Authenticate with your Google account
4. Grant calendar read permissions

### 3. Gmail OAuth2
1. Go to Settings → Credentials → Add Credential
2. Select "Gmail OAuth2"
3. Authenticate with your Google account
4. Grant email read permissions

### 4. OpenAI API
1. Go to Settings → Credentials → Add Credential
2. Select "OpenAI"
3. Enter your OpenAI API key
4. Save the credential

## Step 2: Import Workflows

### Method 1: Via n8n UI
1. Go to Workflows page in n8n
2. Click "Add Workflow" → "Import from File"
3. Select one of these files:
   - `pa-helper-validated.json` (Recommended - fully validated)
   - `pa-helper-daily-dashboard.json` (Original version)
   - `pa-helper-scheduled.json` (Scheduled version)
4. Click "Import"

### Method 2: Via Copy/Paste
1. Open the workflow JSON file
2. Copy all contents
3. In n8n, click "Add Workflow"
4. Press Ctrl/Cmd+V to paste
5. Click "Save"

## Step 3: Configure the Workflow

### For Slack Trigger Version:
1. Open the imported workflow
2. Click on "Slack Trigger" node
3. Select your Slack credential
4. Choose the event type (app_mention recommended)
5. Save the workflow

### For Scheduled Version:
1. Open the imported workflow
2. Click on "Schedule Trigger" node
3. Set your preferred time (default: 7 AM)
4. Configure timezone if needed

## Step 4: Map Credentials

After import, you'll need to reconnect credentials:
1. Click on each node with a warning icon
2. Select the appropriate credential from dropdown
3. Test the connection
4. Save the node

## Step 5: Test the Workflow

### Testing Slack Trigger:
1. Click "Execute Workflow" button
2. In Slack, mention your app: `@YourApp dashboard`
3. Check execution results in n8n

### Testing Scheduled Workflow:
1. Click on Schedule node
2. Use "Execute Node" to test immediately
3. Check the output

## Step 6: Activate the Workflow

1. Toggle the "Active" switch in the top bar
2. Workflow is now live!

## Troubleshooting

### Common Issues:

**Slack not responding:**
- Ensure your app is added to the channel
- Check Slack app permissions
- Verify webhook URL in Slack app settings

**No calendar events:**
- Check date format in expressions
- Verify calendar permissions
- Test with a calendar that has events

**OpenAI errors:**
- Verify API key is valid
- Check API rate limits
- Ensure you have credits

**Gmail not fetching:**
- Check search query syntax
- Verify OAuth permissions
- Test with simpler query first

## Workflow Files Overview

- **pa-helper-validated.json**: Latest version with full error handling
- **pa-helper-daily-dashboard.json**: Slack command triggered
- **pa-helper-scheduled.json**: Time-based trigger
- **pa-helper-mcp-generated.json**: Raw MCP generated version

## Next Steps

1. Customize the AI prompt for your needs
2. Add more data sources (Jira, GitHub, etc.)
3. Create different triggers for different reports
4. Set up error notifications
5. Add data persistence for tracking

---
Created: 2025-08-03
For: n8n PA Helper Implementation