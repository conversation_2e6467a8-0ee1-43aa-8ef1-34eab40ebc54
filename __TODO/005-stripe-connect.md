<img src="https://r2cdn.perplexity.ai/pplx-full-logo-primary-dark%402x.png" class="logo" width="120"/>

# put it in a .md file for me thanks

Absolutely! Here's your NestJS + Stripe Connect Express onboarding guide formatted for a Markdown (.md) file. You can copy and save it as, for example, `stripe-connect-express-nestjs.md`.

# Stripe Connect Express Onboarding with NestJS

This guide walks you through integrating **Stripe Connect Express** onboarding for referrals using a NestJS backend. With this, your users can create Stripe Express accounts and receive payouts directly from your app.

## 1. Install Dependencies

Install the Stripe Node SDK, types, and dotenv for environment variables:

```bash
npm install stripe @types/stripe
npm install dotenv
```

Add your secret Stripe key to a `.env` file:

```
STRIPE_SECRET_KEY=sk_test_xxx
```


## 2. Set Up a Stripe Module

Create a dedicated Stripe module for organization:

```bash
nest g module stripe
nest g service stripe
nest g controller stripe
```

Edit `stripe.module.ts`:

```typescript
import { Module } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { StripeService } from './stripe.service';
import { StripeController } from './stripe.controller';

@Module({
  imports: [ConfigModule.forRoot()],
  providers: [
    StripeService,
    {
      provide: 'STRIPE_API_KEY',
      useFactory: (configService: ConfigService) =>
        configService.get<string>('STRIPE_SECRET_KEY'),
      inject: [ConfigService],
    },
  ],
  controllers: [StripeController],
  exports: [StripeService],
})
export class StripeModule {}
```


## 3. Implement the Stripe Service

Edit `stripe.service.ts` to include methods for account creation and onboarding links:

```typescript
import { Injectable, Inject } from '@nestjs/common';
import Stripe from 'stripe';

@Injectable()
export class StripeService {
  private stripe: Stripe;

  constructor(@Inject('STRIPE_API_KEY') private apiKey: string) {
    this.stripe = new Stripe(this.apiKey, { apiVersion: '2024-12-18.acacia' });
  }

  async createExpressAccount(email: string) {
    return await this.stripe.accounts.create({
      type: 'express',
      email,
      capabilities: {
        transfers: { requested: true },
      },
    });
  }

  async createAccountLink(accountId: string, refreshUrl: string, returnUrl: string) {
    return await this.stripe.accountLinks.create({
      account: accountId,
      refresh_url: refreshUrl,
      return_url: returnUrl,
      type: 'account_onboarding',
    });
  }
}
```


## 4. Expose Endpoints in StripeController

In `stripe.controller.ts`, allow the frontend to trigger onboarding flows:

```typescript
import { Controller, Post, Body } from '@nestjs/common';
import { StripeService } from './stripe.service';

@Controller('stripe')
export class StripeController {
  constructor(private readonly stripeService: StripeService) {}

  @Post('create-express-account')
  async createExpressAccount(@Body('email') email: string) {
    const account = await this.stripeService.createExpressAccount(email);
    return account;
  }

  @Post('create-account-link')
  async createAccountLink(
    @Body('accountId') accountId: string,
    @Body('refreshUrl') refreshUrl: string,
    @Body('returnUrl') returnUrl: string
  ) {
    const link = await this.stripeService.createAccountLink(accountId, refreshUrl, returnUrl);
    return link;
  }
}
```

**Flow:**

- Call `POST /stripe/create-express-account` with the referral's email to create a Stripe Express account.
- Call `POST /stripe/create-account-link` with the returned `account.id` plus your refresh and return URLs to get an onboarding link.
- Send your referral to the onboarding link to finish setup.


## 5. Webhook Handling (Recommended)

- Create endpoints and services to receive Stripe webhooks (for account onboarding status, payouts, etc).
- Stripe will notify your app about events through these webhooks.


## 6. Security \& Compliance

- Store the returned Stripe Account IDs and link them to your users.
- Ensure only authenticated/authorized users can trigger onboarding for themselves.
- Handle errors, KYC requirements, and compliance status in your logic before allowing payouts.


## Summary

This pattern lets you securely and efficiently onboard your referrals to Stripe Express—no handling of sensitive bank information yourself, and full compliance via Stripe! The main API flows:

1. **Create Express Account** → 2. **Generate Onboarding Link** → 3. **Redirect Referral**

Happy coding!

**References:**

- [Stripe Connect Express Accounts Documentation]
- [NestJS Official Docs]

*(Replace placeholders in square brackets with actual URLs, if needed.)*

