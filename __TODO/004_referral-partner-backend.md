# Referral Partner Backend Implementation TODO

## Summary of Work Completed

### Frontend Pages Created
1. **Referral Partner Dashboard** (`/src/pages/referral-partner/index.tsx`)
   - Overview metrics and earnings summary
   - Recent referrals list
   - Quick stats display

2. **Open Job Board** (`/src/pages/referral-partner/jobs.tsx`)
   - Browse available jobs that accept referrals
   - Copy referral links in format: `https://app.kaleidotalent.com/open-jobs/{jobId}?referral={referralCode}`
   - Search and filter functionality
   - Shows commission rates and high-value bounties

3. **Earnings Page** (`/src/pages/referral-partner/earnings.tsx`)
   - Total, pending, and paid earnings display
   - Referral history with status tracking
   - Monthly earnings trend chart
   - Export functionality

### Frontend Services Created
- **Referral API Service** (`/src/app/referral-partner/services/referralApi.ts`)
  - Service layer for all referral-related API calls
  - Includes partner, referral, and earnings endpoints

### Navigation Updates
- Added 'partner' group to navigation configuration
- Updated Sidebar component to include 'partner' in groupOrder
- Added three navigation items for referral partners:
  - Partner Dashboard
  - Open Job Board
  - Earnings

### Utility Created
- **getUserDisplayName** (`/src/utils/getUserDisplayName.ts`)
  - Consistent user name display across the application
  - Handles various Auth0 user object structures

## Backend Work Required

### 1. Create Referral Module Structure
Create a new module in the backend specifically for referral partners:

```
kaleido-backend/src/modules/referral/
├── referral.module.ts
├── referral.controller.ts
├── referral.service.ts
├── dto/
│   ├── get-referral-jobs.dto.ts
│   ├── create-referral.dto.ts
│   └── update-referral.dto.ts
└── entities/
    ├── referral-partner.entity.ts (if not exists)
    └── referral.entity.ts (if not exists)
```

### 2. Referral Controller Endpoints

Create `referral.controller.ts` with the following endpoints:

```typescript
@Controller('referral')
@UseGuards(Auth0Guard)
export class ReferralController {
  
  // Get jobs available for referral
  @Get('jobs/available')
  @Roles(UserRole.REFERRAL_PARTNER)
  async getAvailableJobs(
    @Query() query: GetReferralJobsDto,
    @CurrentUser() user: UserPayload
  ): Promise<JobsResponse> {
    // Return only published jobs that accept referrals
    // Include referral settings and commission details
  }

  // Get partner information
  @Get('partner/profile')
  @Roles(UserRole.REFERRAL_PARTNER)
  async getPartnerProfile(
    @CurrentUser() user: UserPayload
  ): Promise<ReferralPartner> {
    // Return partner profile based on user's clientId
  }

  // Get earnings summary
  @Get('partner/earnings')
  @Roles(UserRole.REFERRAL_PARTNER)
  async getEarnings(
    @CurrentUser() user: UserPayload
  ): Promise<ReferralEarnings> {
    // Return earnings summary with total, pending, paid
  }

  // Get referral history
  @Get('referrals')
  @Roles(UserRole.REFERRAL_PARTNER)
  async getReferrals(
    @Query() filters: GetReferralsDto,
    @CurrentUser() user: UserPayload
  ): Promise<Referral[]> {
    // Return paginated referral history
  }

  // Track new referral
  @Post('track')
  @Public() // Public endpoint for tracking referral clicks
  async trackReferral(
    @Body() data: TrackReferralDto
  ): Promise<{ success: boolean }> {
    // Track referral click/application
  }

  // Request payment
  @Post('partner/request-payment')
  @Roles(UserRole.REFERRAL_PARTNER)
  async requestPayment(
    @Body() data: RequestPaymentDto,
    @CurrentUser() user: UserPayload
  ): Promise<PaymentRequest> {
    // Create payment request for pending earnings
  }
}
```

### 3. Referral Service Implementation

Create `referral.service.ts` with business logic:

```typescript
@Injectable()
export class ReferralService {
  constructor(
    @InjectRepository(Job)
    private jobRepository: Repository<Job>,
    @InjectRepository(ReferralPartner)
    private partnerRepository: Repository<ReferralPartner>,
    @InjectRepository(Referral)
    private referralRepository: Repository<Referral>,
  ) {}

  async getAvailableJobs(filters: GetReferralJobsDto): Promise<Job[]> {
    // Query jobs where:
    // - isPublished = true
    // - referralSettings.acceptsReferrals = true
    // - status !== 'CLOSED'
    // Include pagination and sorting
  }

  async getPartnerByClientId(clientId: string): Promise<ReferralPartner> {
    // Find partner by Auth0 clientId
    // Include dashboard metrics
  }

  async getEarnings(partnerId: string): Promise<ReferralEarnings> {
    // Calculate total, pending, and paid earnings
    // Group by month for trend data
  }

  async getReferrals(partnerId: string, filters: any): Promise<Referral[]> {
    // Get referrals with candidate and job details
    // Support filtering by status, date range
  }

  async trackReferral(data: TrackReferralDto): Promise<void> {
    // Validate referral code
    // Create or update referral record
    // Track source, medium, campaign
  }

  async calculateCommission(referral: Referral): Promise<number> {
    // Calculate commission based on job's referral settings
    // Handle percentage vs fixed bounty
  }
}
```

### 4. DTOs Required

#### GetReferralJobsDto
```typescript
export class GetReferralJobsDto {
  @IsOptional()
  @Type(() => Number)
  @IsInt()
  limit?: number = 20;

  @IsOptional()
  @Type(() => Number)
  @IsInt()
  offset?: number = 0;

  @IsOptional()
  @IsString()
  sort?: string = 'createdAt';

  @IsOptional()
  @IsIn(['ASC', 'DESC'])
  order?: 'ASC' | 'DESC' = 'DESC';

  @IsOptional()
  @IsString()
  search?: string;

  @IsOptional()
  @IsString()
  department?: string;

  @IsOptional()
  @IsString()
  location?: string;
}
```

#### TrackReferralDto
```typescript
export class TrackReferralDto {
  @IsString()
  @IsNotEmpty()
  referralCode: string;

  @IsString()
  @IsNotEmpty()
  jobId: string;

  @IsOptional()
  @IsString()
  candidateId?: string;

  @IsOptional()
  @IsString()
  source?: string;

  @IsOptional()
  @IsString()
  medium?: string;

  @IsOptional()
  @IsString()
  campaign?: string;
}
```

### 5. Update Frontend API Calls

Update the frontend to use the new endpoints:

1. **Jobs Page** (`/src/pages/referral-partner/jobs.tsx`)
   - Change from `/jobs/referral/available` to `/referral/jobs/available`

2. **Referral API Service** (`/src/app/referral-partner/services/referralApi.ts`)
   - Update all endpoints to use `/referral/*` prefix
   - Example: `/referral/partner/profile`, `/referral/partner/earnings`, `/referral/referrals`

### 6. Authentication & Authorization

1. Ensure `@Roles(UserRole.REFERRAL_PARTNER)` decorator is used on all partner-specific endpoints
2. Implement proper validation to ensure partners can only access their own data
3. Add rate limiting for public tracking endpoint

### 7. Database Considerations

1. Ensure indexes on:
   - `referral_partners.clientId` for fast lookups
   - `referrals.referralPartnerId` for partner queries
   - `referrals.status` for filtering
   - `jobs.isPublished` and `jobs.referralSettings` for job queries

2. Consider adding views or materialized views for:
   - Monthly earnings aggregation
   - Partner dashboard metrics

### 8. Testing Requirements

1. Unit tests for ReferralService methods
2. Integration tests for controller endpoints
3. E2E tests for complete referral flow:
   - Partner views jobs
   - Copies referral link
   - Candidate applies via link
   - Referral is tracked
   - Commission is calculated

## Next Steps

1. Create the referral module structure in the backend
2. Implement the controller with all required endpoints
3. Implement the service with business logic
4. Create DTOs for request/response validation
5. Update frontend to use new endpoints
6. Add comprehensive tests
7. Document API endpoints in Swagger

## Environment Variables Needed

```bash
# Referral system configuration
REFERRAL_LINK_BASE_URL=https://app.kaleidotalent.com
REFERRAL_COOKIE_DURATION=30 # days
REFERRAL_DEFAULT_COMMISSION_PERCENT=10
```

## Security Considerations

1. Validate referral codes to prevent SQL injection
2. Rate limit public tracking endpoint to prevent abuse
3. Ensure partners can only access their own data
4. Implement CORS properly for public tracking endpoint
5. Log all payment requests for audit trail

---

This document outlines the complete backend implementation needed for the referral partner system. The frontend is ready and waiting for these endpoints to be implemented.