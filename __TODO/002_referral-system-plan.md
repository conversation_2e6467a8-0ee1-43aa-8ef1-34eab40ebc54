# Affiliate/Referral System Implementation Plan

## Overview
This document outlines the implementation plan for an affiliate/referral system that allows referral partners to earn bounties by referring candidates to jobs. The system will integrate with existing entities while maintaining clean separation of concerns.

## Core Entities

### 1. Referral Partner Entity
```typescript
@Entity('referral_partners')
export class ReferralPartner extends BaseEntity {
  @Column({ unique: true })
  referralCode!: string; // Unique code like 'REF-ABC123'
  
  @Column({ unique: true })
  clientId!: string; // Links to UserRoleEntity
  
  @Column({ nullable: true })
  companyId?: string; // Links to Company if partner is company-specific
  
  @Column()
  partnerName!: string;
  
  @Column({ nullable: true })
  contactEmail!: string;
  
  @Column({ nullable: true })
  contactPhone?: string;
  
  @Column({ type: 'decimal', precision: 10, scale: 2, default: 0 })
  totalEarnings!: number;
  
  @Column({ type: 'decimal', precision: 10, scale: 2, default: 0 })
  pendingEarnings!: number;
  
  @Column({ type: 'decimal', precision: 10, scale: 2, default: 0 })
  paidEarnings!: number;
  
  @Column({ default: true })
  isActive!: boolean;
  
  @Column({ type: 'jsonb', nullable: true })
  settings?: {
    defaultBountyPercentage?: number;
    customBountyRules?: any[];
    paymentPreferences?: {
      method: string;
      details: any;
    };
  };
  
  @Column({ type: 'jsonb', nullable: true })
  dashboardMetrics?: {
    totalReferrals: number;
    successfulPlacements: number;
    conversionRate: number;
    averageBounty: number;
  };
}
```

### 2. Referral Entity (Tracks Individual Referrals)
```typescript
@Entity('referrals')
export class Referral extends BaseEntity {
  @Column()
  referralPartnerId!: string;
  
  @Column()
  candidateId!: string;
  
  @Column()
  jobId!: string;
  
  @Column({ nullable: true })
  companyId?: string;
  
  @Column()
  referralCode!: string; // The code used for this referral
  
  @Column({
    type: 'enum',
    enum: ReferralStatus,
    default: ReferralStatus.PENDING
  })
  status!: ReferralStatus;
  
  @Column({ type: 'decimal', precision: 10, scale: 2, nullable: true })
  bountyAmount?: number;
  
  @Column({ type: 'jsonb', nullable: true })
  bountyCalculation?: {
    baseSalary?: number;
    percentage?: number;
    fixedAmount?: number;
    calculationType: 'PERCENTAGE' | 'FIXED' | 'TIERED';
  };
  
  @Column({ type: 'timestamp', nullable: true })
  candidateAppliedAt?: Date;
  
  @Column({ type: 'timestamp', nullable: true })
  candidateHiredAt?: Date;
  
  @Column({ type: 'timestamp', nullable: true })
  bountyPaidAt?: Date;
  
  @Column({ type: 'jsonb', nullable: true })
  trackingData?: {
    source: string;
    medium: string;
    campaign?: string;
    clickedAt: Date;
    ipAddress?: string;
    userAgent?: string;
  };
}

enum ReferralStatus {
  PENDING = 'PENDING',
  CANDIDATE_APPLIED = 'CANDIDATE_APPLIED',
  CANDIDATE_INTERVIEWED = 'CANDIDATE_INTERVIEWED',
  CANDIDATE_HIRED = 'CANDIDATE_HIRED',
  BOUNTY_APPROVED = 'BOUNTY_APPROVED',
  BOUNTY_PAID = 'BOUNTY_PAID',
  EXPIRED = 'EXPIRED',
  CANCELLED = 'CANCELLED'
}
```

### 3. Bounty Configuration Entity
```typescript
@Entity('bounty_configurations')
export class BountyConfiguration extends BaseEntity {
  @Column({ nullable: true })
  companyId?: string; // Company-specific config
  
  @Column({ nullable: true })
  jobId?: string; // Job-specific config
  
  @Column({ nullable: true })
  referralPartnerId?: string; // Partner-specific config
  
  @Column({
    type: 'enum',
    enum: BountyType,
    default: BountyType.PERCENTAGE
  })
  bountyType!: BountyType;
  
  @Column({ type: 'decimal', precision: 5, scale: 2, nullable: true })
  percentageValue?: number; // e.g., 10.00 for 10%
  
  @Column({ type: 'decimal', precision: 10, scale: 2, nullable: true })
  fixedAmount?: number;
  
  @Column({ type: 'jsonb', nullable: true })
  tieredStructure?: {
    tiers: Array<{
      minHires: number;
      maxHires: number;
      value: number; // percentage or fixed based on bountyType
    }>;
  };
  
  @Column({ default: true })
  isActive!: boolean;
  
  @Column({ type: 'int', nullable: true })
  priority?: number; // For config precedence
}

enum BountyType {
  PERCENTAGE = 'PERCENTAGE',
  FIXED = 'FIXED',
  TIERED = 'TIERED'
}
```

## Entity Modifications

### 1. Candidate Entity Updates
```typescript
// Add to Candidate entity
@Column({ nullable: true })
referralCode?: string;

@Column({ nullable: true })
referralId?: string;

@ManyToOne(() => Referral)
@JoinColumn({ name: 'referralId' })
referral?: Referral;
```

### 2. Job Entity Updates
```typescript
// Add to Job entity
@Column({ type: 'jsonb', nullable: true })
referralSettings?: {
  acceptsReferrals: boolean;
  bountyConfiguration?: {
    type: 'PERCENTAGE' | 'FIXED';
    value: number;
  };
  referralPartnerIds?: string[]; // Specific partners allowed
};
```

### 3. Company Entity Updates
```typescript
// Add to Company entity
@Column({ type: 'jsonb', nullable: true })
referralProgram?: {
  isEnabled: boolean;
  defaultBountyType: 'PERCENTAGE' | 'FIXED';
  defaultBountyValue: number;
  approvedPartners?: string[];
  terms?: string;
};
```

## System Flow

### 1. Referral Partner Onboarding
```mermaid
graph TD
    A[New Referral Partner] --> B[Create User with REFERRAL role]
    B --> C[Create ReferralPartner entity]
    C --> D[Generate unique referral code]
    D --> E[Set up bounty configuration]
    E --> F[Provide dashboard access]
```

### 2. Candidate Referral Flow
```mermaid
graph TD
    A[Referral link shared] --> B[Candidate clicks link]
    B --> C[System captures referral code]
    C --> D[Candidate applies to job]
    D --> E[Create Referral entity]
    E --> F[Link Candidate to Referral]
    F --> G[Track application progress]
    G --> H{Candidate hired?}
    H -->|Yes| I[Calculate bounty]
    H -->|No| J[Update referral status]
    I --> K[Approve bounty]
    K --> L[Process payment]
```

### 3. Bounty Calculation Logic
```typescript
// Pseudo-code for bounty calculation
function calculateBounty(referral: Referral): number {
  // Priority order: Job > Company > Partner > System default
  const config = getBountyConfiguration(
    referral.jobId,
    referral.companyId,
    referral.referralPartnerId
  );
  
  if (config.bountyType === 'PERCENTAGE') {
    const salary = getJobSalary(referral.jobId);
    return salary * (config.percentageValue / 100);
  } else if (config.bountyType === 'FIXED') {
    return config.fixedAmount;
  } else if (config.bountyType === 'TIERED') {
    const partnerHires = getPartnerHiresCount(referral.referralPartnerId);
    const tier = config.tieredStructure.tiers.find(
      t => partnerHires >= t.minHires && partnerHires <= t.maxHires
    );
    return calculateTierValue(tier, referral);
  }
}
```

## Dashboard Features

### Referral Partner Dashboard
1. **Overview Section**
   - Total referrals
   - Successful placements
   - Earnings (pending/paid/total)
   - Conversion rate

2. **Referrals Management**
   - List of all referrals with status
   - Filter by job, company, status
   - Track candidate progress
   - View bounty calculations

3. **Earnings & Payments**
   - Payment history
   - Pending payments
   - Payment methods
   - Tax documents

4. **Tools & Resources**
   - Referral link generator
   - Marketing materials
   - Job listings eligible for referral
   - Performance analytics

## API Endpoints

### Referral Partner Endpoints
```typescript
// Partner management
POST   /api/referral-partners
GET    /api/referral-partners/:id
PUT    /api/referral-partners/:id
GET    /api/referral-partners/:id/dashboard

// Referral tracking
POST   /api/referrals/track
GET    /api/referrals
GET    /api/referrals/:id
PUT    /api/referrals/:id/status

// Earnings
GET    /api/referral-partners/:id/earnings
GET    /api/referral-partners/:id/payments
POST   /api/referral-partners/:id/request-payment

// Reports
GET    /api/referral-partners/:id/reports/summary
GET    /api/referral-partners/:id/reports/detailed
```

### Public Endpoints
```typescript
// For tracking referral clicks
GET    /api/r/:referralCode
POST   /api/referrals/capture
```

## Security Considerations

1. **Access Control**
   - Referral partners can only see their own data
   - Cannot access candidate PII beyond basic status
   - Cannot modify bounty configurations

2. **Referral Code Security**
   - Codes should be cryptographically secure
   - Rate limiting on referral link usage
   - IP-based fraud detection

3. **Payment Security**
   - Two-factor approval for bounty payments
   - Audit trail for all transactions
   - Integration with secure payment providers

## Implementation Phases

### Phase 1: Core Infrastructure (Week 1-2)
- Create database entities
- Set up basic CRUD operations
- Implement referral tracking

### Phase 2: Integration (Week 3-4)
- Modify existing entities
- Create referral capture flow
- Implement bounty calculation

### Phase 3: Dashboard (Week 5-6)
- Build referral partner dashboard
- Create reporting features
- Implement analytics

### Phase 4: Payment System (Week 7-8)
- Integrate payment processing
- Build approval workflow
- Create payment reports

### Phase 5: Testing & Launch (Week 9-10)
- Comprehensive testing
- Security audit
- Soft launch with selected partners

## Metrics to Track

1. **Performance Metrics**
   - Referral conversion rate
   - Time to hire for referred candidates
   - Average bounty per placement
   - Partner retention rate

2. **Financial Metrics**
   - Total bounties paid
   - ROI on referral program
   - Cost per hire via referrals
   - Payment processing costs

3. **Quality Metrics**
   - Referred candidate quality scores
   - Retention rate of referred hires
   - Partner satisfaction scores

## Future Enhancements

1. **Advanced Features**
   - Multi-tier referral programs
   - Automated marketing campaigns
   - AI-powered matching for referrals
   - Mobile app for partners

2. **Integrations**
   - CRM integration
   - Marketing automation tools
   - Advanced analytics platforms
   - Accounting software integration

3. **Gamification**
   - Leaderboards
   - Achievement badges
   - Bonus incentives
   - Partner tiers with benefits