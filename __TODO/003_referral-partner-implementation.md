# Referral Partner System Implementation Documentation

## Overview
This document outlines the implementation status of the referral partner system for Kaleido Talent, including what has been completed and what remains to be implemented.

## What Has Been Completed

### 1. Backend Infrastructure (✅ Complete)

#### Entities Created
- **ReferralPartner Entity** (`kaleido-backend/src/modules/referral/entities/referral-partner.entity.ts`)
  - Stores partner information with unique referral codes
  - Tracks earnings (pending, paid, total)
  - Dashboard analytics fields
  - Settings for notifications and tracking

- **Referral Entity** (`kaleido-backend/src/modules/referral/entities/referral.entity.ts`)
  - Tracks individual referrals with status lifecycle
  - Links partners, candidates, and jobs
  - Status flow: PENDING → CANDIDATE_APPLIED → CANDIDATE_HIRED → BOUNTY_APPROVED → BOUNTY_PAID

- **BountyConfiguration Entity** (`kaleido-backend/src/modules/referral/entities/bounty-configuration.entity.ts`)
  - Configurable bounty amounts by job type/seniority
  - Company-specific bounty overrides
  - Active/inactive status management

#### Services Implemented
- **ReferralPartnerService**: Partner CRUD operations, unique code generation
- **ReferralService**: Referral tracking, status updates, analytics
- **BountyService**: Bounty calculations, approval workflow

#### Controllers Created
- **ReferralPartnerController**: Authenticated endpoints for partner management
- **ReferralController**: Authenticated endpoints for referral operations
- **PublicReferralController**: Public endpoints for tracking referral clicks

#### Database Updates
- Updated Candidate entity with referral tracking fields
- Updated Job entity with referral bounty settings
- Updated Company entity with bounty preferences
- Successfully ran migration to create all tables and relationships

### 2. Technical Fixes Applied
- Replaced `nanoid` with Node.js `crypto.randomBytes()` for CommonJS compatibility
- Fixed TypeScript type errors with Express Request/Response
- Resolved TypeORM entity registration issues
- Fixed database migration foreign key type mismatches

## What Needs to Be Implemented

### 1. Frontend - Referral Partner Onboarding Page

#### Route: `/referral-partner/onboarding`

**Components Needed:**
```typescript
// Main onboarding page component
- ReferralPartnerOnboarding.tsx
  - Hero section with gradient background (similar to accept-invitation.tsx)
  - Benefits section highlighting earning potential
  - How it works step-by-step guide
  - Call-to-action for registration

// Sub-components
- OnboardingHero.tsx (gradient background with compelling copy)
- BenefitsGrid.tsx (showcase partner benefits)
- HowItWorksSteps.tsx (visual step-by-step process)
- PartnerTestimonials.tsx (social proof)
```

**Key Features:**
- Responsive design with gradient backgrounds
- Clear value proposition for potential partners
- Seamless Auth0 integration for signup
- Auto-redirect to dashboard after registration

### 2. Auth0 Integration for Referral Partners

**Authentication Flow:**
1. User clicks "Become a Partner" on landing page
2. Redirect to Auth0 with `role=referral_partner` parameter
3. After successful auth, create ReferralPartner record
4. Assign `REFERRAL_PARTNER` role in backend
5. Redirect to partner dashboard

**Implementation Steps:**
```typescript
// In LandingPage.tsx or new ReferralLanding.tsx
const handlePartnerSignup = () => {
  localStorage.setItem('pendingUserRole', UserRole.REFERRAL_PARTNER);
  const params = new URLSearchParams({
    returnTo: '/referral-partner/dashboard',
    role: 'referral_partner',
    screen_hint: 'signup'
  });
  window.location.href = `/api/auth/login?${params.toString()}`;
};
```

**Backend Updates Needed:**
- Add `REFERRAL_PARTNER` to UserRole enum
- Update JWT strategy to handle referral partner role assignment
- Create middleware to protect partner routes

### 3. Referral Partner Dashboard

#### Route: `/referral-partner/dashboard`

**Dashboard Sections:**
1. **Overview Cards** (similar to EmployerDashboard.tsx)
   - Total Referrals
   - Successful Hires
   - Pending Earnings
   - Total Earned

2. **Referral Link Management**
   - Display unique referral code
   - Copy link functionality
   - QR code generation
   - Custom link creation for specific jobs

3. **Referrals Table**
   - List all referrals with status
   - Candidate name/details
   - Job information
   - Status tracking
   - Earnings per referral

4. **Earnings & Payouts**
   - Earnings history
   - Pending payouts
   - Payment method settings
   - Tax documentation

5. **Performance Analytics**
   - Conversion funnel
   - Click-through rates
   - Top performing jobs
   - Monthly trends

**Components Structure:**
```typescript
// Main dashboard
- ReferralPartnerDashboard.tsx
  - StatsOverview.tsx
  - ReferralLinkManager.tsx
  - ReferralsTable.tsx
  - EarningsSection.tsx
  - AnalyticsCharts.tsx

// Supporting components
- CopyLinkButton.tsx
- QRCodeGenerator.tsx
- ReferralStatusBadge.tsx
- EarningsChart.tsx
- PayoutRequestModal.tsx
```

### 4. Navigation Updates

**Update `navigation.ts`:**
```typescript
{
  label: 'Partner Dashboard',
  href: '/referral-partner/dashboard',
  icon: Users,
  roles: [UserRole.REFERRAL_PARTNER],
  gradient: themeGradients.matchRank,
  description: 'Manage your referrals and track earnings',
  group: 'partner',
},
{
  label: 'My Referrals',
  href: '/referral-partner/referrals',
  icon: Link,
  roles: [UserRole.REFERRAL_PARTNER],
  gradient: themeGradients.notifications,
  description: 'View all your referral submissions',
  group: 'partner',
},
{
  label: 'Earnings',
  href: '/referral-partner/earnings',
  icon: DollarSign,
  roles: [UserRole.REFERRAL_PARTNER],
  gradient: themeGradients.cultureFit,
  description: 'Track your earnings and request payouts',
  group: 'partner',
},
```

### 5. Website Footer Update

**Add to website footer:**
- "Referral Program" or "Become a Partner" link
- Link to `/referral-partner/onboarding`
- Styled consistently with other footer links

### 6. Email Notifications

**Email templates needed:**
1. Welcome email for new partners
2. Referral submitted confirmation
3. Candidate hired notification
4. Bounty approved notification
5. Payment processed confirmation

### 7. Admin Features

**Admin dashboard additions:**
- View all referral partners
- Approve/reject partner applications
- Manage bounty configurations
- Process payout requests
- View referral analytics

## Technical Considerations

### Security
- Validate referral codes on both frontend and backend
- Rate limit referral tracking endpoints
- Secure partner dashboard with proper authentication
- Implement CSRF protection for referral links

### Performance
- Cache referral partner data
- Optimize referral tracking queries
- Implement pagination for large referral lists
- Use indexes on referral_code fields

### Tracking & Analytics
- Implement click tracking with IP/user agent logging
- Cookie-based attribution for referral sources
- Conversion funnel analytics
- A/B testing for referral landing pages

## Implementation Priority

1. **Phase 1 - Core Functionality**
   - [ ] Add REFERRAL_PARTNER role to backend
   - [ ] Create onboarding landing page
   - [ ] Implement Auth0 flow for partners
   - [ ] Build basic partner dashboard

2. **Phase 2 - Enhanced Features**
   - [ ] Add referral link management
   - [ ] Implement earnings tracking
   - [ ] Create referrals table with filtering
   - [ ] Add email notifications

3. **Phase 3 - Advanced Features**
   - [ ] Analytics and reporting
   - [ ] Payout processing
   - [ ] Admin management tools
   - [ ] Mobile app support

## Next Steps

1. **Immediate Actions:**
   - Add `REFERRAL_PARTNER` to UserRole enum in backend
   - Create the onboarding page with gradient design
   - Update navigation.ts with partner routes
   - Implement Auth0 integration for partner signup

2. **Follow-up Tasks:**
   - Build out the partner dashboard components
   - Create API endpoints for dashboard data
   - Implement referral link tracking
   - Set up email notification system

3. **Testing Requirements:**
   - Test referral link tracking across browsers
   - Verify Auth0 role assignment
   - Test dashboard data accuracy
   - Validate bounty calculations

## Notes

- The backend infrastructure is fully implemented and tested
- Database schema supports all required features
- Focus should be on frontend implementation and Auth0 integration
- Consider using existing components (like EmployerDashboard) as templates
- Maintain consistent styling with gradient backgrounds throughout