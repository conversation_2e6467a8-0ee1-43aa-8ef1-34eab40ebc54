<build-idea>
You're creating an n8n agent that reads your daily calendar events and identifies important unread emails, providing a comprehensive daily overview. This agent can be triggered via Slack, scheduled to run automatically, or called on-demand.

The agent uses OpenAI as the LLM and has access to the following tools:
• Google Calendar tool (to READ today's events from all calendars)
• Gmail tool (to READ unread emails and identify important ones)
• Vectal MCP tool (to generate a formatted daily report)
• Slack tool (to send the formatted dashboard)

Workflow structure for Slack-triggered version:
Slack Trigger (slash command like /today or /dashboard)
→ Parallel execution:
→ Google Calendar: Get all events for today (from all connected calendars)
→ Gmail: Get unread emails + filter for importance (using AI to detect urgency/importance)
→ Merge results
→ Agent (OpenAI) to analyze and format:

- Prioritize meetings by importance
- Identify scheduling conflicts
- Highlight urgent emails requiring response
- Extract key action items
  → Vectal MCP Tool (generate beautiful formatted report)
  → Slack Send Message with rich blocks showing:
- Today's schedule with meeting details
- Important unread emails with preview
- Suggested actions/priorities

Alternative Trigger Options:

1. **Scheduled Trigger**: Run every morning at 7 AM
2. **Webhook Trigger**: Called by your system tray app
3. **HTTP Request**: For browser extension integration

Output format includes:

- Time-blocked calendar view
- Meeting titles, attendees, and duration
- Color-coded priority levels
- Email sender, subject, and brief preview
- Quick action buttons (<PERSON>in meeting, Reply to email)
  </build-idea>
