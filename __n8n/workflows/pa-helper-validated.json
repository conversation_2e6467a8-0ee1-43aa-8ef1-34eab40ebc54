{"name": "PA Helper - Daily Dashboard (Validated)", "nodes": [{"parameters": {"event": "app_mention"}, "id": "slack-trigger-001", "name": "<PERSON><PERSON><PERSON>", "type": "n8n-nodes-base.slackTrigger", "typeVersion": 1, "position": [250, 300], "credentials": {"slackApi": {"id": "slack-oauth2", "name": "Slack OAuth2"}}, "onError": "continueRegularOutput"}, {"parameters": {"resource": "event", "operation": "getAll", "calendar": "primary", "timeMin": "={{ $now.minus({days: 0}).startOf('day').toISO() }}", "timeMax": "={{ $now.minus({days: 0}).endOf('day').toISO() }}", "options": {"singleEvents": true, "orderBy": "startTime"}}, "id": "gcal-001", "name": "Get Today's Calendar Events", "type": "n8n-nodes-base.googleCalendar", "typeVersion": 1, "position": [550, 200], "credentials": {"googleCalendarOAuth2Api": {"id": "google-calendar", "name": "Google Calendar"}}, "onError": "continueRegularOutput"}, {"parameters": {"resource": "message", "operation": "getAll", "q": "is:unread newer_than:1d", "returnAll": false, "limit": 20, "additionalFields": {}}, "id": "gmail-001", "name": "Get Unread Emails", "type": "n8n-nodes-base.gmail", "typeVersion": 2.1, "position": [550, 400], "credentials": {"gmailOAuth2": {"id": "gmail-oauth2", "name": "Gmail OAuth2"}}, "onError": "continueRegularOutput"}, {"parameters": {"jsCode": "// Combine calendar and email data\nconst calendarEvents = $input.first().json.events || [];\nconst emails = $input.last().json.messages || [];\n\n// Format calendar events\nconst formattedEvents = calendarEvents.map(event => {\n  const start = event.start?.dateTime || event.start?.date || 'No time';\n  return {\n    summary: event.summary || 'No title',\n    start: start,\n    attendees: event.attendees?.length || 0\n  };\n});\n\n// Format emails\nconst formattedEmails = emails.map(email => {\n  return {\n    from: email.payload?.headers?.find(h => h.name === 'From')?.value || 'Unknown',\n    subject: email.payload?.headers?.find(h => h.name === 'Subject')?.value || 'No subject',\n    snippet: email.snippet || ''\n  };\n});\n\nreturn {\n  calendarEvents: formattedEvents,\n  emails: formattedEmails,\n  date: new Date().toLocaleDateString()\n};"}, "id": "code-001", "name": "Format Data", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [750, 300], "onError": "continueRegularOutput"}, {"parameters": {"model": {"__rl": true, "value": "gpt-4", "mode": "list"}, "messages": {"values": [{"role": "system", "content": "You are a helpful personal assistant that creates organized daily briefings. Format your response as a clear, structured report suitable for Slack messaging."}, {"role": "user", "content": "={{ 'Please analyze this data and create a daily dashboard:\\n\\nCalendar Events:\\n' + $json.calendarEvents.map(e => '- ' + e.summary + ' at ' + e.start).join('\\n') + '\\n\\nUnread Emails:\\n' + $json.emails.map(e => '- From: ' + e.from + ', Subject: ' + e.subject).join('\\n') + '\\n\\nPlease include: 1) Prioritized schedule, 2) Important emails, 3) Suggested actions, 4) Any conflicts' }}"}]}, "options": {"temperature": 0.7}}, "id": "openai-001", "name": "AI Analysis", "type": "n8n-nodes-base.openAi", "typeVersion": 1, "position": [950, 300], "credentials": {"openAiApi": {"id": "openai-api", "name": "OpenAI API"}}, "onError": "continueRegularOutput"}, {"parameters": {"resource": "message", "operation": "send", "authentication": "oAuth2", "channel": "={{ $('Slack Trigger').item.json.channel }}", "text": "={{ $json.choices[0].message.content }}", "otherOptions": {}}, "id": "slack-send-001", "name": "Send to Slack", "type": "n8n-nodes-base.slack", "typeVersion": 2.2, "position": [1150, 300], "credentials": {"slackOAuth2Api": {"id": "slack-oauth2", "name": "Slack OAuth2"}}, "onError": "continueRegularOutput"}], "connections": {"Slack Trigger": {"main": [[{"node": "Get Today's Calendar Events", "type": "main", "index": 0}, {"node": "Get Unread Emails", "type": "main", "index": 0}]]}, "Get Today's Calendar Events": {"main": [[{"node": "Format Data", "type": "main", "index": 0}]]}, "Get Unread Emails": {"main": [[{"node": "Format Data", "type": "main", "index": 0}]]}, "Format Data": {"main": [[{"node": "AI Analysis", "type": "main", "index": 0}]]}, "AI Analysis": {"main": [[{"node": "Send to Slack", "type": "main", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1", "saveDataSuccessExecution": "all", "saveExecutionProgress": true, "saveManualExecutions": true, "callerPolicy": "workflowsFromSameOwner"}, "versionId": "1.0.0", "meta": {"templateCredsSetupCompleted": false, "instanceId": "pa-helper-validated"}, "tags": []}