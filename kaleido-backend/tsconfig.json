{
  "compilerOptions": {
    "jsx": "react",
    "module": "CommonJS",
    "declaration": true,
    "removeComments": true,
    "emitDecoratorMetadata": true,
    "experimentalDecorators": true,
    "allowSyntheticDefaultImports": true,
    "target": "es2018",
    "lib": ["es2018", "es2017", "es2015", "dom"],
    "sourceMap": true,
    "outDir": "./dist",
    "baseUrl": "./",
    "incremental": true,
    "skipLibCheck": true,
    "strict": true,
    "strictNullChecks": true,
    "noImplicitAny": true,
    "strictBindCallApply": true,
    "forceConsistentCasingInFileNames": true,
    "noFallthroughCasesInSwitch": true,
    "types": ["node", "jest", "react"],
    "useUnknownInCatchVariables": false,
    "typeRoots": ["./node_modules/@types", "./src/types"],
    "esModuleInterop": true,

    "paths": {
      "@/*": ["./src/*"],
      "@modules/*": ["./src/modules/*"],
      "@shared/*": ["./src/shared/*"]
    },
      
    "inlineSources": true,

    // Set `sourceRoot` to  "/" to strip the build path prefix
    // from generated source code references.
    // This improves issue grouping in Sentry.
    "sourceRoot": "/"
  },
  "include": [
    "src/**/*"
  ],
  "exclude": [
    "node_modules",
    "dist",
    "src/migrations",
    "**/*.spec.ts",
    "**/*.test.ts",
    "src/__tests__/**/*",
    "test/**/*"
  ]
} 