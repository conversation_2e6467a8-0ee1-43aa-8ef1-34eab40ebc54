# Migration Workflow Guide

## Overview
This guide explains how migrations work in both development and production environments.

## Current Setup

### Automatic Migration on Startup
The application now automatically handles migrations on startup:

1. **Docker/Production**: The `docker-entrypoint.sh` runs `ensure-migrations.js`
2. **Local Development**: Run `npm run migration:run` before starting

### How ensure-migrations.js Works
1. Checks if the database has migrations table
2. Checks if critical tables/columns exist
3. Applies emergency fixes if needed
4. Runs TypeORM migrations
5. Handles errors gracefully in production

## Development Workflow

### 1. Making Entity Changes
When you modify an entity:

```bash
# 1. Make your entity changes
# 2. Generate a migration
npm run migration:generate -- -n DescriptiveName

# 3. Review the generated migration
# 4. Run the migration
npm run migration:run

# 5. Commit both entity and migration files
git add src/entities/... src/migrations/...
git commit -m "feat: add new field to entity"
```

### 2. Creating New Entities
When adding a new entity:

```bash
# 1. Create your entity file
# 2. Add to src/entities/index.ts
npm run entities:update

# 3. Generate migration
npm run migration:generate -- -n AddNewEntity

# 4. Run migration
npm run migration:run
```

## Production Deployment

### Deployment Behavior
**IMPORTANT**: Deployments will now FAIL if migrations fail. This is by design to ensure database consistency.

### Automatic Process
1. Deploy code with new migrations
2. Container starts and runs `ensure-migrations.js`
3. If migrations fail:
   - Detailed error messages are logged
   - Container exits with error code
   - Deployment is rolled back
4. If migrations succeed:
   - Application starts normally

### When Migrations Fail
The deployment will show detailed error information:
- Full error messages and stack traces
- Database connection details (without passwords)
- Specific troubleshooting steps
- Command that failed

### Pre-deployment Validation
Run this before deploying to catch issues early:

```bash
npm run validate:migrations
```

This will check:
- Migration file structure
- TypeScript compilation
- Database connection
- User permissions

## Best Practices

### 1. Always Generate Migrations
- Never modify the database manually
- Always use TypeORM to generate migrations
- Review generated SQL before committing

### 2. Test Migrations
- Run migrations locally first
- Test rollback (down method)
- Ensure migrations are idempotent

### 3. Migration Naming
Use descriptive names:
- `AddUserRole`
- `UpdateCompanySettings`
- `CreateReferralSystem`

### 4. Keep Migrations Small
- One logical change per migration
- Easier to debug and rollback
- Better for team collaboration

## Troubleshooting

### Migration Fails in Production
1. Check logs: `kubectl logs <pod-name>`
2. Run fix script: `node scripts/fix-production-now.js`
3. Set `FORCE_START_ON_MIGRATION_FAILURE=true` to start anyway

### Missing Columns Error
1. The `ensure-migrations.js` should catch this
2. If not, run: `node scripts/fix-production-now.js`

### Migration Not Generated
1. Ensure entity is exported in `src/entities/index.ts`
2. Run `npm run entities:update`
3. Check for TypeScript errors

## Environment Variables

```bash
# Skip migrations entirely (not recommended)
SKIP_MIGRATIONS=true

# Force start even if migrations fail
FORCE_START_ON_MIGRATION_FAILURE=true

# Database connection
DB_HOST=localhost
DB_PORT=5432
DB_NAME=kaleido
DB_USERNAME=postgres
DB_PASSWORD=password
DB_SSL=require  # for production
```

## Migration Commands

```bash
# Generate new migration
npm run migration:generate -- -n MigrationName

# Run pending migrations
npm run migration:run

# Show migration status
npm run migration:show

# Revert last migration
npm run migration:revert

# Run migrations in production mode
npm run migration:run:prod
```

## Important Files

- `scripts/ensure-migrations.js` - Main migration runner
- `scripts/fix-production-now.js` - Emergency column fixer
- `src/config/migration.config.ts` - Migration configuration
- `scripts/docker-entrypoint.sh` - Docker startup script