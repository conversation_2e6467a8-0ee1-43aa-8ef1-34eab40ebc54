import { MigrationInterface, QueryRunner } from 'typeorm';

export class UpdateReferralIdTypes1756558000000 implements MigrationInterface {
  name = 'UpdateReferralIdTypes1756558000000';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // First, drop the existing foreign key constraints and indexes that depend on these columns
    await queryRunner.query(`DROP INDEX IF EXISTS "IDX_referrals_composite"`);

    // Convert candidateId from varchar to uuid
    await queryRunner.query(
      `ALTER TABLE "referrals" ALTER COLUMN "candidateId" TYPE uuid USING "candidateId"::uuid`,
    );

    // Convert jobId from varchar to uuid
    await queryRunner.query(
      `ALTER TABLE "referrals" ALTER COLUMN "jobId" TYPE uuid USING "jobId"::uuid`,
    );

    // Convert companyId from varchar to uuid (nullable)
    await queryRunner.query(
      `ALTER TABLE "referrals" ALTER COLUMN "companyId" TYPE uuid USING "companyId"::uuid`,
    );

    // Recreate the composite index
    await queryRunner.query(
      `CREATE INDEX "IDX_referrals_composite" ON "referrals" ("referralPartnerId", "candidateId", "jobId")`,
    );

    // Add foreign key constraints for candidate, job, and company
    await queryRunner.query(
      `ALTER TABLE "referrals" ADD CONSTRAINT "FK_referrals_candidateId" FOREIGN KEY ("candidateId") REFERENCES "candidates"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "referrals" ADD CONSTRAINT "FK_referrals_jobId" FOREIGN KEY ("jobId") REFERENCES "jobs"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "referrals" ADD CONSTRAINT "FK_referrals_companyId" FOREIGN KEY ("companyId") REFERENCES "companies"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Drop foreign key constraints
    await queryRunner.query(
      `ALTER TABLE "referrals" DROP CONSTRAINT IF EXISTS "FK_referrals_companyId"`,
    );
    await queryRunner.query(
      `ALTER TABLE "referrals" DROP CONSTRAINT IF EXISTS "FK_referrals_jobId"`,
    );
    await queryRunner.query(
      `ALTER TABLE "referrals" DROP CONSTRAINT IF EXISTS "FK_referrals_candidateId"`,
    );

    // Drop the composite index
    await queryRunner.query(`DROP INDEX IF EXISTS "IDX_referrals_composite"`);

    // Convert back to varchar
    await queryRunner.query(
      `ALTER TABLE "referrals" ALTER COLUMN "companyId" TYPE character varying`,
    );
    await queryRunner.query(`ALTER TABLE "referrals" ALTER COLUMN "jobId" TYPE character varying`);
    await queryRunner.query(
      `ALTER TABLE "referrals" ALTER COLUMN "candidateId" TYPE character varying`,
    );

    // Recreate the composite index
    await queryRunner.query(
      `CREATE INDEX "IDX_referrals_composite" ON "referrals" ("referralPartnerId", "candidateId", "jobId")`,
    );
  }
}
