-- Add composite index for efficient duplicate checking on scouted candidates
-- This index helps prevent duplicate candidates with the same name and Apollo external ID for the same job

-- Index for name + Apollo profile ID combination per job
CREATE INDEX IF NOT EXISTS idx_scouted_candidates_job_name_apollo_id 
ON scouted_candidates(jobId, fullName, (apolloMetadata->>'profileId'))
WHERE isAdded = false;

-- Additional index for profile URL lookups
CREATE INDEX IF NOT EXISTS idx_scouted_candidates_job_profile_url
ON scouted_candidates(jobId, profileUrl)
WHERE isAdded = false;

-- Additional index for LinkedIn URL lookups  
CREATE INDEX IF NOT EXISTS idx_scouted_candidates_job_linkedin_url
ON scouted_candidates(jobId, linkedinUrl)
WHERE isAdded = false;

-- Composite index for Apollo metadata profile ID
CREATE INDEX IF NOT EXISTS idx_scouted_candidates_apollo_profile_id
ON scouted_candidates(jobId, (apolloMetadata->>'profileId'))
WHERE isAdded = false AND apolloMetadata IS NOT NULL;