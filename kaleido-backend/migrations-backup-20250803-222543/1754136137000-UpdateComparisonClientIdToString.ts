import { MigrationInterface, QueryRunner } from 'typeorm';

export class UpdateComparisonClientIdToString1754136137000 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    // First drop the foreign key constraint if it exists
    await queryRunner.query(`
      ALTER TABLE candidate_comparisons 
      DROP CONSTRAINT IF EXISTS fk_comparison_client;
    `);

    // Change clientId from UUID to VARCHAR
    await queryRunner.query(`
      ALTER TABLE candidate_comparisons 
      ALTER COLUMN "clientId" TYPE VARCHAR(255) USING "clientId"::text;
    `);

    // Also update performedBy to VARCHAR since it's also an Auth0 ID
    await queryRunner.query(`
      ALTER TABLE candidate_comparisons 
      ALTER COLUMN "performedBy" TYPE VARCHAR(255) USING "performedBy"::text;
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Revert clientId back to UUID (this might fail if data contains non-UUID values)
    await queryRunner.query(`
      ALTER TABLE candidate_comparisons 
      ALTER COLUMN "clientId" TYPE UUID USING "clientId"::uuid;
    `);

    // Revert performedBy back to UUID
    await queryRunner.query(`
      ALTER TABLE candidate_comparisons 
      ALTER COLUMN "performedBy" TYPE UUID USING "performedBy"::uuid;
    `);
  }
}
