import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddCompanyTeamManagement1754047900537 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    // Create company_members table
    await queryRunner.query(`
      CREATE TYPE "company_member_role_enum" AS ENUM('owner', 'admin', 'member', 'viewer');
      CREATE TYPE "company_member_status_enum" AS ENUM('active', 'invited', 'suspended');
      
      CREATE TABLE "company_members" (
        "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
        "companyId" uuid NOT NULL,
        "clientId" character varying NOT NULL,
        "email" character varying NOT NULL,
        "role" "company_member_role_enum" NOT NULL DEFAULT 'member',
        "permissions" jsonb,
        "status" "company_member_status_enum" NOT NULL DEFAULT 'invited',
        "invitedBy" character varying,
        "invitedAt" TIMESTAMP NOT NULL DEFAULT now(),
        "joinedAt" TIMESTAMP,
        "createdAt" TIMESTAMP NOT NULL DEFAULT now(),
        "updatedAt" TIMESTAMP NOT NULL DEFAULT now(),
        CONSTRAINT "PK_company_members_id" PRIMARY KEY ("id"),
        CONSTRAINT "FK_company_members_company" FOREIGN KEY ("companyId") REFERENCES "companies"("id") ON DELETE CASCADE ON UPDATE NO ACTION
      );
      
      CREATE UNIQUE INDEX "idx_company_members_company_client" ON "company_members" ("companyId", "clientId");
      CREATE INDEX "idx_company_members_client_id" ON "company_members" ("clientId");
      CREATE INDEX "idx_company_members_email" ON "company_members" ("email");
      CREATE INDEX "idx_company_members_status" ON "company_members" ("status");
    `);

    // Create company_invitations table
    await queryRunner.query(`
      CREATE TYPE "invitation_status_enum" AS ENUM('pending', 'accepted', 'expired', 'cancelled');
      
      CREATE TABLE "company_invitations" (
        "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
        "companyId" uuid NOT NULL,
        "email" character varying NOT NULL,
        "invitedByClientId" character varying NOT NULL,
        "invitedByName" character varying,
        "role" "company_member_role_enum" NOT NULL DEFAULT 'member',
        "permissions" jsonb,
        "invitationToken" character varying NOT NULL,
        "status" "invitation_status_enum" NOT NULL DEFAULT 'pending',
        "message" text,
        "createdAt" TIMESTAMP NOT NULL DEFAULT now(),
        "expiresAt" TIMESTAMP NOT NULL,
        "acceptedAt" TIMESTAMP,
        "acceptedByClientId" character varying,
        "updatedAt" TIMESTAMP NOT NULL DEFAULT now(),
        CONSTRAINT "PK_company_invitations_id" PRIMARY KEY ("id"),
        CONSTRAINT "UQ_company_invitations_token" UNIQUE ("invitationToken"),
        CONSTRAINT "FK_company_invitations_company" FOREIGN KEY ("companyId") REFERENCES "companies"("id") ON DELETE CASCADE ON UPDATE NO ACTION
      );
      
      CREATE UNIQUE INDEX "idx_company_invitations_token" ON "company_invitations" ("invitationToken");
      CREATE INDEX "idx_company_invitations_email" ON "company_invitations" ("email");
      CREATE INDEX "idx_company_invitations_status" ON "company_invitations" ("status");
      CREATE INDEX "idx_company_invitations_company" ON "company_invitations" ("companyId");
    `);

    // Add new columns to companies table
    await queryRunner.query(`
      ALTER TABLE "companies" 
      ADD COLUMN "allowedEmailDomains" jsonb DEFAULT '[]',
      ADD COLUMN "autoJoinEnabled" boolean DEFAULT false,
      ADD COLUMN "defaultAutoJoinRole" character varying DEFAULT 'member';
    `);

    // Migrate existing company-user relationships to company_members table
    await queryRunner.query(`
      INSERT INTO "company_members" ("companyId", "clientId", "email", "role", "status", "joinedAt", "permissions")
      SELECT 
        c.id as "companyId",
        c."clientId",
        COALESCE(c."contactEmail", '<EMAIL>') as "email",
        'owner' as "role",
        'active' as "status",
        c."createdAt" as "joinedAt",
        jsonb_build_object(
          'canManageJobs', true,
          'canViewCandidates', true,
          'canManageCandidates', true,
          'canManageTeam', true,
          'canManageBilling', true,
          'canViewAnalytics', true,
          'canManageCompanySettings', true
        ) as "permissions"
      FROM "companies" c
      WHERE c."clientId" IS NOT NULL;
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Remove new columns from companies table
    await queryRunner.query(`
      ALTER TABLE "companies" 
      DROP COLUMN "allowedEmailDomains",
      DROP COLUMN "autoJoinEnabled",
      DROP COLUMN "defaultAutoJoinRole";
    `);

    // Drop tables
    await queryRunner.query(`DROP TABLE "company_invitations"`);
    await queryRunner.query(`DROP TABLE "company_members"`);

    // Drop enums
    await queryRunner.query(`DROP TYPE "invitation_status_enum"`);
    await queryRunner.query(`DROP TYPE "company_member_status_enum"`);
    await queryRunner.query(`DROP TYPE "company_member_role_enum"`);
  }
}
