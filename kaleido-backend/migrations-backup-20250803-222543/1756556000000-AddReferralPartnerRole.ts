import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddReferralPartnerRole1756556000000 implements MigrationInterface {
  name = 'AddReferralPartnerRole1756556000000';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Add REFERRAL_PARTNER to the user_roles_role_enum
    await queryRunner.query(`ALTER TYPE "user_roles_role_enum" ADD VALUE 'referral-partner'`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Note: PostgreSQL doesn't support removing enum values directly
    // This would require recreating the enum type which is complex
    // For production, you'd want a more comprehensive down migration
    console.warn('Removing enum values is not straightforward in PostgreSQL');
  }
}
