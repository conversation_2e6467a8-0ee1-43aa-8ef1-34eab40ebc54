import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddReferralSystem1754155800000 implements MigrationInterface {
  name = 'AddReferralSystem1754155800000';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Create referral_partners table
    await queryRunner.query(`
            CREATE TABLE "referral_partners" (
                "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
                "createdAt" TIMESTAMP NOT NULL DEFAULT now(),
                "updatedAt" TIMESTAMP NOT NULL DEFAULT now(),
                "referralCode" character varying NOT NULL,
                "clientId" character varying NOT NULL,
                "companyId" character varying,
                "partnerName" character varying NOT NULL,
                "contactEmail" character varying NOT NULL,
                "contactPhone" character varying,
                "totalEarnings" numeric(10,2) NOT NULL DEFAULT '0',
                "pendingEarnings" numeric(10,2) NOT NULL DEFAULT '0',
                "paidEarnings" numeric(10,2) NOT NULL DEFAULT '0',
                "isActive" boolean NOT NULL DEFAULT true,
                "settings" jsonb,
                "dashboardMetrics" jsonb,
                CONSTRAINT "UQ_referral_partners_referralCode" UNIQUE ("referralCode"),
                CONSTRAINT "UQ_referral_partners_clientId" UNIQUE ("clientId"),
                CONSTRAINT "PK_referral_partners" PRIMARY KEY ("id")
            )
        `);

    // Create indexes for referral_partners
    await queryRunner.query(
      `CREATE INDEX "IDX_referral_partners_referralCode" ON "referral_partners" ("referralCode")`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_referral_partners_clientId" ON "referral_partners" ("clientId")`,
    );

    // Create referrals table
    await queryRunner.query(`
            CREATE TABLE "referrals" (
                "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
                "createdAt" TIMESTAMP NOT NULL DEFAULT now(),
                "updatedAt" TIMESTAMP NOT NULL DEFAULT now(),
                "referralPartnerId" uuid NOT NULL,
                "candidateId" character varying NOT NULL,
                "jobId" character varying NOT NULL,
                "companyId" character varying,
                "referralCode" character varying NOT NULL,
                "status" character varying NOT NULL DEFAULT 'PENDING',
                "bountyAmount" numeric(10,2),
                "bountyCalculation" jsonb,
                "candidateAppliedAt" TIMESTAMP,
                "candidateHiredAt" TIMESTAMP,
                "bountyPaidAt" TIMESTAMP,
                "trackingData" jsonb,
                CONSTRAINT "PK_referrals" PRIMARY KEY ("id")
            )
        `);

    // Create indexes for referrals
    await queryRunner.query(
      `CREATE INDEX "IDX_referrals_composite" ON "referrals" ("referralPartnerId", "candidateId", "jobId")`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_referrals_referralCode" ON "referrals" ("referralCode")`,
    );
    await queryRunner.query(`CREATE INDEX "IDX_referrals_status" ON "referrals" ("status")`);

    // Create bounty_configurations table
    await queryRunner.query(`
            CREATE TABLE "bounty_configurations" (
                "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
                "createdAt" TIMESTAMP NOT NULL DEFAULT now(),
                "updatedAt" TIMESTAMP NOT NULL DEFAULT now(),
                "companyId" character varying,
                "jobId" character varying,
                "referralPartnerId" character varying,
                "bountyType" character varying NOT NULL DEFAULT 'PERCENTAGE',
                "percentageValue" numeric(5,2),
                "fixedAmount" numeric(10,2),
                "tieredStructure" jsonb,
                "isActive" boolean NOT NULL DEFAULT true,
                "priority" integer,
                CONSTRAINT "PK_bounty_configurations" PRIMARY KEY ("id")
            )
        `);

    // Create index for bounty_configurations
    await queryRunner.query(
      `CREATE INDEX "IDX_bounty_configurations_composite" ON "bounty_configurations" ("companyId", "jobId", "referralPartnerId")`,
    );

    // Add foreign key constraints
    await queryRunner.query(
      `ALTER TABLE "referrals" ADD CONSTRAINT "FK_referrals_referralPartnerId" FOREIGN KEY ("referralPartnerId") REFERENCES "referral_partners"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );

    // Update candidates table
    await queryRunner.query(`ALTER TABLE "candidates" ADD "referralCode" character varying`);
    await queryRunner.query(`ALTER TABLE "candidates" ADD "referralId" uuid`);
    await queryRunner.query(
      `ALTER TABLE "candidates" ADD CONSTRAINT "FK_candidates_referralId" FOREIGN KEY ("referralId") REFERENCES "referrals"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );

    // Update jobs table
    await queryRunner.query(`ALTER TABLE "jobs" ADD "referralSettings" jsonb`);

    // Update companies table
    await queryRunner.query(`ALTER TABLE "companies" ADD "referralProgram" jsonb`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Drop foreign key constraints
    await queryRunner.query(`ALTER TABLE "candidates" DROP CONSTRAINT "FK_candidates_referralId"`);
    await queryRunner.query(
      `ALTER TABLE "referrals" DROP CONSTRAINT "FK_referrals_referralPartnerId"`,
    );

    // Remove columns from existing tables
    await queryRunner.query(`ALTER TABLE "companies" DROP COLUMN "referralProgram"`);
    await queryRunner.query(`ALTER TABLE "jobs" DROP COLUMN "referralSettings"`);
    await queryRunner.query(`ALTER TABLE "candidates" DROP COLUMN "referralId"`);
    await queryRunner.query(`ALTER TABLE "candidates" DROP COLUMN "referralCode"`);

    // Drop indexes
    await queryRunner.query(`DROP INDEX "IDX_bounty_configurations_composite"`);
    await queryRunner.query(`DROP INDEX "IDX_referrals_status"`);
    await queryRunner.query(`DROP INDEX "IDX_referrals_referralCode"`);
    await queryRunner.query(`DROP INDEX "IDX_referrals_composite"`);
    await queryRunner.query(`DROP INDEX "IDX_referral_partners_clientId"`);
    await queryRunner.query(`DROP INDEX "IDX_referral_partners_referralCode"`);

    // Drop tables
    await queryRunner.query(`DROP TABLE "bounty_configurations"`);
    await queryRunner.query(`DROP TABLE "referrals"`);
    await queryRunner.query(`DROP TABLE "referral_partners"`);
  }
}
