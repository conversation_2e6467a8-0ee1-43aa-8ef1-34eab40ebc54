import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddStripeToReferralSystem1756559000000 implements MigrationInterface {
  name = 'AddStripeToReferralSystem1756559000000';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Add Stripe fields to referral_partners table
    await queryRunner.query(
      `ALTER TABLE "referral_partners" ADD "stripeAccountId" character varying`,
    );
    await queryRunner.query(
      `ALTER TABLE "referral_partners" ADD "stripeAccountStatus" character varying`,
    );

    // Add index for stripeAccountId for faster lookups
    await queryRunner.query(
      `CREATE INDEX "IDX_referral_partners_stripeAccountId" ON "referral_partners" ("stripeAccountId")`,
    );

    // Add Stripe payment tracking fields to referrals table
    await queryRunner.query(`ALTER TABLE "referrals" ADD "stripeTransferId" character varying`);
    await queryRunner.query(`ALTER TABLE "referrals" ADD "stripePayoutId" character varying`);

    // Add check constraint for stripeAccountStatus
    await queryRunner.query(
      `ALTER TABLE "referral_partners" ADD CONSTRAINT "CHK_stripeAccountStatus" CHECK ("stripeAccountStatus" IN ('pending', 'active', 'restricted', 'disabled'))`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Remove check constraint
    await queryRunner.query(
      `ALTER TABLE "referral_partners" DROP CONSTRAINT "CHK_stripeAccountStatus"`,
    );

    // Remove columns from referrals table
    await queryRunner.query(`ALTER TABLE "referrals" DROP COLUMN "stripePayoutId"`);
    await queryRunner.query(`ALTER TABLE "referrals" DROP COLUMN "stripeTransferId"`);

    // Remove index
    await queryRunner.query(`DROP INDEX "IDX_referral_partners_stripeAccountId"`);

    // Remove columns from referral_partners table
    await queryRunner.query(`ALTER TABLE "referral_partners" DROP COLUMN "stripeAccountStatus"`);
    await queryRunner.query(`ALTER TABLE "referral_partners" DROP COLUMN "stripeAccountId"`);
  }
}
