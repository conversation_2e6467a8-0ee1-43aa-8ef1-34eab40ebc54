-- Migration to add monthly video JD limits to existing companies
-- This protects against Synthesia cost abuse by implementing Option 2

-- Update existing companies to include videoJdMonthlyLimit and videoJdUsedThisMonth
UPDATE companies 
SET "subscriptionCredits" = jsonb_set(
  jsonb_set(
    COALESCE("subscriptionCredits", '{}'::jsonb),
    '{videoJdMonthlyLimit}',
    CASE 
      WHEN "subscriptionPlan" = 'free' THEN '0'::jsonb
      WHEN "subscriptionPlan" = 'starter' THEN '3'::jsonb  
      WHEN "subscriptionPlan" = 'professional' THEN '10'::jsonb
      WHEN "subscriptionPlan" = 'enterprise' THEN '20'::jsonb
      ELSE '0'::jsonb
    END
  ),
  '{videoJdUsedThisMonth}',
  '0'::jsonb
)
WHERE "subscriptionCredits" IS NOT NULL;

-- Update companies with NULL subscriptionCredits to have default values
UPDATE companies 
SET "subscriptionCredits" = 
CASE 
  WHEN "subscriptionPlan" = 'free' THEN '{
    "totalCredits": 5,
    "usedCredits": 0,
    "remainingCredits": 5,
    "monthlyAllocation": 5,
    "lastResetDate": "2024-01-01T00:00:00.000Z",
    "videoJdMaxDuration": 60,
    "videoJdMonthlyLimit": 0,
    "videoJdUsedThisMonth": 0,
    "atsIntegration": "basic",
    "databaseRetentionMonths": 1
  }'::jsonb
  WHEN "subscriptionPlan" = 'starter' THEN '{
    "totalCredits": 359,
    "usedCredits": 0,
    "remainingCredits": 359,
    "monthlyAllocation": 359,
    "lastResetDate": "2024-01-01T00:00:00.000Z",
    "videoJdMaxDuration": 90,
    "videoJdMonthlyLimit": 3,
    "videoJdUsedThisMonth": 0,
    "atsIntegration": "basic",
    "databaseRetentionMonths": 3
  }'::jsonb
  WHEN "subscriptionPlan" = 'professional' THEN '{
    "totalCredits": 885,
    "usedCredits": 0,
    "remainingCredits": 885,
    "monthlyAllocation": 885,
    "lastResetDate": "2024-01-01T00:00:00.000Z",
    "videoJdMaxDuration": 180,
    "videoJdMonthlyLimit": 10,
    "videoJdUsedThisMonth": 0,
    "atsIntegration": "advanced",
    "databaseRetentionMonths": 12
  }'::jsonb
  WHEN "subscriptionPlan" = 'enterprise' THEN '{
    "totalCredits": 9999999,
    "usedCredits": 0,
    "remainingCredits": 9999999,
    "monthlyAllocation": 9999999,
    "lastResetDate": "2024-01-01T00:00:00.000Z",
    "videoJdMaxDuration": 300,
    "videoJdMonthlyLimit": 20,
    "videoJdUsedThisMonth": 0,
    "atsIntegration": "custom",
    "databaseRetentionMonths": 24
  }'::jsonb
  ELSE '{
    "totalCredits": 5,
    "usedCredits": 0,
    "remainingCredits": 5,
    "monthlyAllocation": 5,
    "lastResetDate": "2024-01-01T00:00:00.000Z",
    "videoJdMaxDuration": 60,
    "videoJdMonthlyLimit": 0,
    "videoJdUsedThisMonth": 0,
    "atsIntegration": "basic",
    "databaseRetentionMonths": 1
  }'::jsonb
END
WHERE "subscriptionCredits" IS NULL;
