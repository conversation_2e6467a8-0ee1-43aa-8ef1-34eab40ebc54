-- SQL script to add MATCH_MODIFIED status to candidate_status_enum
-- This can be run manually in your PostgreSQL database

-- First, check if the enum type exists
DO $$
DECLARE
    enum_exists BOOLEAN;
    enum_values TEXT[];
    value_exists BOOLEAN := FALSE;
BEGIN
    -- Check if the enum type exists
    SELECT EXISTS (
        SELECT 1 FROM pg_type 
        WHERE typname = 'candidate_status_enum'
    ) INTO enum_exists;

    IF enum_exists THEN
        -- Get current enum values
        SELECT array_agg(e.enumlabel) INTO enum_values
        FROM pg_enum e
        JOIN pg_type t ON e.enumtypid = t.oid
        WHERE t.typname = 'candidate_status_enum';
        
        -- Check if MATCH_MODIFIED already exists
        SELECT 'MATCH_MODIFIED' = ANY(enum_values) INTO value_exists;
        
        IF NOT value_exists THEN
            -- Add the new enum value
            EXECUTE 'ALTER TYPE candidate_status_enum ADD VALUE IF NOT EXISTS ''MATCH_MODIFIED''';
            RAISE NOTICE 'Successfully added MATCH_MODIFIED status to candidate_status_enum';
        ELSE
            RAISE NOTICE 'MATCH_MODIFIED status already exists in candidate_status_enum';
        END IF;
    ELSE
        RAISE NOTICE 'candidate_status_enum does not exist. Please check your database schema.';
    END IF;
END $$;

-- Alternative direct approach (if you're sure the enum exists and doesn't have the value yet)
-- ALTER TYPE candidate_status_enum ADD VALUE IF NOT EXISTS 'MATCH_MODIFIED';
