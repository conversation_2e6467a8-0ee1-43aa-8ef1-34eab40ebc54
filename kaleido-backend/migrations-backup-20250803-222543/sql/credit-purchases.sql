-- Migration: Add credit purchases table for one-time credit purchases
-- Date: 2024-12-19
-- Description: Create table to track one-time credit purchases and their status

-- Create enum for credit package types
DO $$ BEGIN
    CREATE TYPE credit_package_enum AS ENUM ('SMALL', 'MEDIUM', 'LARGE', 'EXTRA_LARGE', 'MEGA');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

-- Create enum for credit purchase status
DO $$ BEGIN
    CREATE TYPE credit_purchase_status_enum AS ENUM ('PENDING', 'COMPLETED', 'FAILED', 'REFUNDED');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

-- Create credit_purchases table
CREATE TABLE IF NOT EXISTS credit_purchases (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    "companyId" UUID NOT NULL,
    "packageType" credit_package_enum NOT NULL,
    "amountPaid" DECIMAL(10,2) NOT NULL,
    "baseCredits" INTEGER NOT NULL,
    "bonusCredits" INTEGER NOT NULL DEFAULT 0,
    "totalCredits" INTEGER NOT NULL,
    "paymentMethod" VARCHAR(50) DEFAULT 'stripe',
    "paymentId" VARCHAR(255), -- Stripe session/payment ID
    "stripeSessionId" VARCHAR(255), -- Stripe checkout session ID
    status credit_purchase_status_enum DEFAULT 'PENDING',
    metadata JSONB DEFAULT '{}', -- Additional metadata
    "createdAt" TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    "updatedAt" TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Foreign key constraint
    CONSTRAINT fk_credit_purchases_company 
        FOREIGN KEY ("companyId") 
        REFERENCES companies(id) 
        ON DELETE CASCADE,
    
    -- Indexes for performance
    CONSTRAINT idx_credit_purchases_company_id 
        UNIQUE ("companyId", "createdAt"),
    
    -- Check constraints
    CONSTRAINT chk_amount_paid_positive 
        CHECK ("amountPaid" > 0),
    CONSTRAINT chk_credits_positive 
        CHECK ("baseCredits" > 0 AND "bonusCredits" >= 0 AND "totalCredits" > 0),
    CONSTRAINT chk_total_credits_calculation 
        CHECK ("totalCredits" = "baseCredits" + "bonusCredits")
);

-- Create indexes for better query performance
CREATE INDEX IF NOT EXISTS idx_credit_purchases_company_id ON credit_purchases("companyId");
CREATE INDEX IF NOT EXISTS idx_credit_purchases_status ON credit_purchases(status);
CREATE INDEX IF NOT EXISTS idx_credit_purchases_payment_id ON credit_purchases("paymentId");
CREATE INDEX IF NOT EXISTS idx_credit_purchases_stripe_session_id ON credit_purchases("stripeSessionId");
CREATE INDEX IF NOT EXISTS idx_credit_purchases_created_at ON credit_purchases("createdAt" DESC);

-- Create updated_at trigger
CREATE OR REPLACE FUNCTION update_credit_purchases_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW."updatedAt" = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER trigger_credit_purchases_updated_at
    BEFORE UPDATE ON credit_purchases
    FOR EACH ROW
    EXECUTE FUNCTION update_credit_purchases_updated_at();

-- Add comments for documentation
COMMENT ON TABLE credit_purchases IS 'Tracks one-time credit purchases made by companies';
COMMENT ON COLUMN credit_purchases."companyId" IS 'Reference to the company that made the purchase';
COMMENT ON COLUMN credit_purchases."packageType" IS 'Type of credit package purchased (SMALL, MEDIUM, LARGE, etc.)';
COMMENT ON COLUMN credit_purchases."amountPaid" IS 'Amount paid in USD for the credit package';
COMMENT ON COLUMN credit_purchases."baseCredits" IS 'Base number of credits in the package';
COMMENT ON COLUMN credit_purchases."bonusCredits" IS 'Bonus credits added to the package';
COMMENT ON COLUMN credit_purchases."totalCredits" IS 'Total credits (base + bonus) added to account';
COMMENT ON COLUMN credit_purchases."paymentMethod" IS 'Payment method used (stripe, paypal, etc.)';
COMMENT ON COLUMN credit_purchases."paymentId" IS 'External payment system transaction ID';
COMMENT ON COLUMN credit_purchases."stripeSessionId" IS 'Stripe checkout session ID for verification';
COMMENT ON COLUMN credit_purchases.status IS 'Current status of the purchase (pending, completed, failed, refunded)';
COMMENT ON COLUMN credit_purchases.metadata IS 'Additional metadata about the purchase';
