-- Step 1: Add FREE plan to subscription_plan enum if it doesn't exist (do this FIRST)
ALTER TYPE subscription_plan_enum ADD VALUE IF NOT EXISTS 'free';

-- Step 2: Add the subscriptionCredits column to the companies table (using camelCase to match entity)
ALTER TABLE companies
ADD COLUMN IF NOT EXISTS "subscriptionCredits" jsonb DEFAULT '{
  "totalCredits": 100,
  "usedCredits": 0,
  "remainingCredits": 100,
  "monthlyAllocation": 100,
  "lastResetDate": "2024-01-01T00:00:00.000Z",
  "videoJdMaxDuration": 30,
  "atsIntegration": "basic",
  "databaseRetentionMonths": 3
}'::jsonb;

-- Step 3: Update existing companies to have default credit data based on their current plan
UPDATE companies
SET "subscriptionCredits" = CASE
  WHEN "subscriptionPlan" = 'free' THEN '{
    "totalCredits": 100,
    "usedCredits": 0,
    "remainingCredits": 100,
    "monthlyAllocation": 100,
    "lastResetDate": "2024-01-01T00:00:00.000Z",
    "videoJdMaxDuration": 30,
    "atsIntegration": "basic",
    "databaseRetentionMonths": 3
  }'::jsonb
  WHEN "subscriptionPlan" = 'starter' THEN '{
    "totalCredits": 3000,
    "usedCredits": 0,
    "remainingCredits": 3000,
    "monthlyAllocation": 3000,
    "lastResetDate": "2024-01-01T00:00:00.000Z",
    "videoJdMaxDuration": 90,
    "atsIntegration": "basic",
    "databaseRetentionMonths": 3
  }'::jsonb
  WHEN "subscriptionPlan" = 'professional' THEN '{
    "totalCredits": 5000,
    "usedCredits": 0,
    "remainingCredits": 5000,
    "monthlyAllocation": 5000,
    "lastResetDate": "2024-01-01T00:00:00.000Z",
    "videoJdMaxDuration": 180,
    "atsIntegration": "advanced",
    "databaseRetentionMonths": 12
  }'::jsonb
  WHEN "subscriptionPlan" = 'enterprise' THEN '{
    "totalCredits": 9999999,
    "usedCredits": 0,
    "remainingCredits": 9999999,
    "monthlyAllocation": 9999999,
    "lastResetDate": "2024-01-01T00:00:00.000Z",
    "videoJdMaxDuration": 300,
    "atsIntegration": "custom",
    "databaseRetentionMonths": 24
  }'::jsonb
  ELSE '{
    "totalCredits": 75,
    "usedCredits": 0,
    "remainingCredits": 75,
    "monthlyAllocation": 75,
    "lastResetDate": "2024-01-01T00:00:00.000Z",
    "videoJdMaxDuration": 30,
    "atsIntegration": "basic",
    "databaseRetentionMonths": 3
  }'::jsonb
END
WHERE "subscriptionCredits" IS NULL;