import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddClientIdToReferrals1756557000000 implements MigrationInterface {
  name = 'AddClientIdToReferrals1756557000000';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Add clientId column to referrals table
    await queryRunner.query(`ALTER TABLE "referrals" ADD "clientId" character varying`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Remove clientId column from referrals table
    await queryRunner.query(`ALTER TABLE "referrals" DROP COLUMN "clientId"`);
  }
}
