apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: kaleido-backend-ingress
  annotations:
    kubernetes.io/ingress.class: nginx
    cert-manager.io/cluster-issuer: letsencrypt-prod
    nginx.ingress.kubernetes.io/ssl-redirect: "false"
    nginx.ingress.kubernetes.io/proxy-body-size: "50m"
spec:
  # Commenting out TLS section until certificate is available
  # tls:
  # - hosts:
  #   - api.kaleidotalent.com
  #   secretName: kaleido-backend-tls
  rules:
  - host: api.kaleidotalent.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: kaleido-backend
            port:
              number: 80
