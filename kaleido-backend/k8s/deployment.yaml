apiVersion: apps/v1
kind: Deployment
metadata:
  name: kaleido-backend
  labels:
    app: kaleido-backend
spec:
  replicas: 1
  selector:
    matchLabels:
      app: kaleido-backend
  template:
    metadata:
      labels:
        app: kaleido-backend
    spec:
      containers:
      - name: kaleido-backend
        image: registry.digitalocean.com/kaleido/kaleido-backend:latest
        imagePullPolicy: Always
        ports:
        - containerPort: 8080
        resources:
          limits:
            cpu: "1"
            memory: "2Gi"
          requests:
            cpu: "500m"
            memory: "1Gi"
        env:
        - name: REDIS_HOST
          value: "localhost"
        - name: REDIS_PORT
          value: "6379"
        - name: NODE_ENV
          value: "production"
        envFrom:
        - configMapRef:
            name: kaleido-backend-config
        - secretRef:
            name: kaleido-backend-secrets
        # Readiness probe is commented out to allow deployment to proceed even if health check fails
        # readinessProbe:
        #   httpGet:
        #     path: /health
        #     port: 8080
        #   initialDelaySeconds: 180
        #   periodSeconds: 30
        #   timeoutSeconds: 15
        #   failureThreshold: 30

        # Liveness probe is commented out to allow deployment to proceed even if health check fails
        # livenessProbe:
        #   httpGet:
        #     path: /health
        #     port: 8080
        #   initialDelaySeconds: 300
        #   periodSeconds: 60
        #   timeoutSeconds: 15
        #   failureThreshold: 30

        # Startup probe is commented out to allow deployment to proceed even if health check fails
        # startupProbe:
        #   httpGet:
        #     path: /health
        #     port: 8080
        #   initialDelaySeconds: 300
        #   periodSeconds: 30
        #   timeoutSeconds: 15
        #   failureThreshold: 60
        volumeMounts:
        - name: redis-data
          mountPath: /data/redis
        - name: uploads
          mountPath: /app/uploads
      # Using private registry
      imagePullSecrets:
      - name: registry-kaleido
      volumes:
      - name: redis-data
        emptyDir: {}
      - name: uploads
        emptyDir: {}
