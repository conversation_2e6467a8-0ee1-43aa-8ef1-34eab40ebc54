# Kubernetes Deployment Guide

This directory contains Kubernetes manifests for deploying the Headstart Backend application to a Kubernetes cluster.

## Prerequisites

- Kubernetes cluster (e.g., GKE, EKS, AKS, or self-hosted)
- kubectl configured to connect to your cluster
- Docker registry for storing container images
- Helm (optional, for installing dependencies)

## Configuration

Before deploying, you need to:

1. Update the `configmap.yaml` and `secrets.yaml` with your actual configuration values
2. Update the `deployment.yaml` to use your Docker registry

## Deployment Steps

### 1. Create Namespace (Optional)

```bash
kubectl create namespace headstart
kubectl config set-context --current --namespace=headstart
```

### 2. Apply Secrets and ConfigMap

```bash
kubectl apply -f k8s/configmap.yaml
```

### 3. Deploy the Application

```bash
kubectl apply -f k8s/deployment.yaml
kubectl apply -f k8s/service.yaml
```

### 4. Deploy Ingress

```bash
kubectl apply -f k8s/ingress.yaml
```

## Monitoring the Deployment

Check the status of your deployment:

```bash
kubectl get pods
kubectl get services
kubectl get ingress
```

View logs from the application:

```bash
kubectl logs -f deployment/kaleido-backend
```

## Scaling

To scale the application horizontally:

```bash
kubectl scale deployment/kaleido-backend --replicas=3
```

## Updating the Application

When you have a new version of the application:

1. Build and push a new Docker image
2. Update the deployment:

```bash
kubectl set image deployment/kaleido-backend kaleido-backend=${DOCKER_REGISTRY}/kaleido-backend:new-tag
```

Or apply the updated deployment file:

```bash
kubectl apply -f k8s/deployment.yaml
```

## Troubleshooting

If you encounter issues:

1. Check pod status: `kubectl get pods`
2. Check pod details: `kubectl describe pod <pod-name>`
3. Check logs: `kubectl logs <pod-name>`
4. Check events: `kubectl get events --sort-by='.lastTimestamp'`
