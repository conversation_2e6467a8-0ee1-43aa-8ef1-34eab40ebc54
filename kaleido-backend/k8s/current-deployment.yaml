apiVersion: apps/v1
kind: Deployment
metadata:
  annotations:
    deployment.kubernetes.io/revision: "31"
    kubectl.kubernetes.io/last-applied-configuration: "{\"apiVersion\":\"apps/v1\",\"kind\":\"Deployment\",\"metadata\":{\"annotations\":{},\"labels\":{\"app\":\"kaleido-backend\"},\"name\":\"kaleido-backend\",\"namespace\":\"default\"},\"spec\":{\"replicas\":1,\"selector\":{\"matchLabels\":{\"app\":\"kaleido-backend\"}},\"template\":{\"metadata\":{\"labels\":{\"app\":\"kaleido-backend\"}},\"spec\":{\"containers\":[{\"args\":[\"echo
      \\\"\U0001F680 Starting container services...\\\"\\n\\n# Start Redis\\necho
      \\\"\U0001F504 Starting Redis...\\\"\\nredis-server --daemonize yes\\n\\n# Check
      Redis status\\necho \\\"\U0001F504 Checking Redis status...\\\"\\nredis-cli
      ping\\n\\n# Start the application\\necho \\\"\U0001F504 Starting application...\\\"\\nnode
      dist/main\\n\"],\"command\":[\"/bin/sh\",\"-c\"],\"env\":[{\"name\":\"REDIS_HOST\",\"value\":\"localhost\"},{\"name\":\"REDIS_PORT\",\"value\":\"6379\"}],\"envFrom\":[{\"configMapRef\":{\"name\":\"kaleido-backend-config\"}},{\"secretRef\":{\"name\":\"kaleido-backend-secrets\"}}],\"image\":\"registry.digitalocean.com/kaleido/kaleido-backend:fix1\",\"imagePullPolicy\":\"Always\",\"name\":\"kaleido-backend\",\"ports\":[{\"containerPort\":8080}],\"resources\":{\"limits\":{\"cpu\":\"400m\",\"memory\":\"800Mi\"},\"requests\":{\"cpu\":\"200m\",\"memory\":\"400Mi\"}}}],\"imagePullSecrets\":[{\"name\":\"registry-kaleido\"}]}}}}\n"
  creationTimestamp: "2025-05-03T21:54:43Z"
  generation: 31
  labels:
    app: kaleido-backend
  name: kaleido-backend
  namespace: default
  resourceVersion: "146212"
  uid: 1f258e8b-b280-41ed-ad82-f3ca77bc2de1
spec:
  progressDeadlineSeconds: 600
  replicas: 1
  revisionHistoryLimit: 10
  selector:
    matchLabels:
      app: kaleido-backend
  strategy:
    rollingUpdate:
      maxSurge: 25%
      maxUnavailable: 25%
    type: RollingUpdate
  template:
    metadata:
      annotations:
        kubectl.kubernetes.io/restartedAt: "2025-05-04T07:06:50+01:00"
      creationTimestamp: null
      labels:
        app: kaleido-backend
    spec:
      containers:
      - args:
        - "echo \"\U0001F680 Starting container services...\"\n\n# Start Redis\necho
          \"\U0001F504 Starting Redis...\"\nredis-server --daemonize yes\n\n# Check
          Redis status\necho \"\U0001F504 Checking Redis status...\"\nredis-cli ping\n\n#
          Start the application\necho \"\U0001F504 Starting application...\"\nnode
          dist/main\n"
        command:
        - /bin/sh
        - -c
        env:
        - name: REDIS_HOST
          value: localhost
        - name: REDIS_PORT
          value: "6379"
        envFrom:
        - configMapRef:
            name: kaleido-backend-config
        - secretRef:
            name: kaleido-backend-secrets
        image: registry.digitalocean.com/kaleido/kaleido-backend:fix1
        imagePullPolicy: Always
        name: kaleido-backend
        ports:
        - containerPort: 8080
          protocol: TCP
        resources:
          limits:
            cpu: 400m
            memory: 800Mi
          requests:
            cpu: 200m
            memory: 400Mi
        terminationMessagePath: /dev/termination-log
        terminationMessagePolicy: File
      dnsPolicy: ClusterFirst
      imagePullSecrets:
      - name: registry-kaleido
      restartPolicy: Always
      schedulerName: default-scheduler
      securityContext: {}
      terminationGracePeriodSeconds: 30
status:
  availableReplicas: 1
  conditions:
  - lastTransitionTime: "2025-05-04T07:50:12Z"
    lastUpdateTime: "2025-05-04T07:50:12Z"
    message: Deployment has minimum availability.
    reason: MinimumReplicasAvailable
    status: "True"
    type: Available
  - lastTransitionTime: "2025-05-04T07:25:34Z"
    lastUpdateTime: "2025-05-04T08:17:19Z"
    message: ReplicaSet "kaleido-backend-596685dd65" is progressing.
    reason: ReplicaSetUpdated
    status: "True"
    type: Progressing
  observedGeneration: 31
  readyReplicas: 1
  replicas: 2
  unavailableReplicas: 1
  updatedReplicas: 1
