import * as os from 'os';

// Import with `const Sentry = require("@sentry/nestjs");` if you are using CJS
import * as Sentry from '@sentry/nestjs';

// Import profiling integration conditionally to avoid platform-specific issues
let nodeProfilingIntegration: any = null;
try {
  const profilingModule = require('@sentry/profiling-node');
  nodeProfilingIntegration = profilingModule.nodeProfilingIntegration;
} catch (e: unknown) {
  const errorMessage = e instanceof Error ? e.message : String(e);
  console.warn('Sentry profiling integration could not be loaded:', errorMessage);
  nodeProfilingIntegration = null;
}

// Only initialize Sentry in production or staging environments
const environment = process.env.NODE_ENV || 'development';
const sentryDsn =
  process.env.SENTRY_DSN ||
  'https://<EMAIL>/4509442555314256';
const sentryAuthToken = process.env.SENTRY_AUTH_TOKEN;

if (environment !== 'development' || process.env.FORCE_SENTRY === 'true') {
  console.log(`Initializing Sentry in ${environment} environment`);

  // Collect runtime information for better error context
  const runtimeContext = {
    hostname: os.hostname(),
    platform: os.platform(),
    release: os.release(),
    uptime: os.uptime(),
    nodeVersion: process.version,
    memory: {
      total: os.totalmem(),
      free: os.freemem(),
    },
    cpu: os.cpus().length,
  };

  Sentry.init({
    dsn: sentryDsn,
    environment,
    integrations: nodeProfilingIntegration ? [nodeProfilingIntegration()] : [],
    // Tracing configuration
    tracesSampleRate: environment === 'production' ? 0.2 : 1.0, // Sample 20% in production, 100% elsewhere
    // Transport configuration if auth token provided
    ...(sentryAuthToken
      ? {
          transport: (options) =>
            Sentry.makeNodeTransport({
              ...options,
              headers: {
                ...options.headers,
                Authorization: `Bearer ${sentryAuthToken}`,
              },
            }),
        }
      : {}),
    // Add runtime info to all events
    beforeSend(event) {
      event.contexts = {
        ...event.contexts,
        runtime: runtimeContext,
      };
      return event;
    },
  });

  // Add global tags
  Sentry.setTags({
    'app.version': process.env.npm_package_version || '1.0.0',
    'node.environment': environment,
    'server.type': process.env.SERVER_TYPE || 'api',
  });

  // Start profiler automatically if available
  if (
    nodeProfilingIntegration &&
    Sentry.profiler &&
    typeof Sentry.profiler.startProfiler === 'function'
  ) {
    try {
      Sentry.profiler.startProfiler();
      console.log('✅ Sentry profiler started successfully');
    } catch (e: unknown) {
      const errorMessage = e instanceof Error ? e.message : String(e);
      console.warn('Failed to start Sentry profiler:', errorMessage);
    }
  } else {
    console.log('ℹ️ Sentry profiler not available - skipping profiler initialization');
  }

  // Set up unhandled rejection and exception handlers
  process.on('unhandledRejection', (reason) => {
    Sentry.captureException(reason);
  });

  process.on('uncaughtException', (error) => {
    Sentry.captureException(error);
  });

  console.log('✅ Sentry initialized successfully');
} else {
  console.log('Sentry initialization skipped for development environment');
}
