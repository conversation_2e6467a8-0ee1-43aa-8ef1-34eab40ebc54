// This file exports all entities for TypeORM configuration
// Auto-generated by scripts/update-entity-exports.ts
// Run 'pnpm run entities:update' to regenerate

export { CreditPurchase } from './CreditPurchase.entity';
export { ApprovalStep } from '../modules/approval/entities/approval-step.entity';
export { Approval } from '../modules/approval/entities/approval.entity';
export { Offer } from '../modules/approval/entities/offer.entity';
export { RecruitmentAssessment } from '../modules/assessment/entities/recruitment-assessment.entity';
export { CandidateApplication } from '../modules/candidate/entities/candidate-application.entity';
export { CandidateEvaluation } from '../modules/candidate/entities/candidate-evaluation.entity';
export { CandidateProfile } from '../modules/candidate/entities/candidate-profile.entity';
export { Candidate } from '../modules/candidate/entities/candidate.entity';
export { ScoutedCandidate } from '../modules/candidate/entities/scouted-candidate.entity';
export { CareerInsight } from '../modules/career-insights/entities/career-insight.entity';
export { CompanyInvitation } from '../modules/company/entities/company-invitation.entity';
export { CompanyMember } from '../modules/company/entities/company-member.entity';
export { Company } from '../modules/company/entities/company.entity';
export { CandidateComparison } from '../modules/comparison/entities/candidate-comparison.entity';
export { Contact } from '../modules/contact/entities/contact.entity';
export { EmailHistory } from '../modules/email/entities/email-history.entity';
export { FeatureFlagEntity } from '../modules/feature-flags/entities/feature-flag.entity';
export { Feedback } from '../modules/feedback/entities/feedback.entity';
export { Graduate } from '../modules/graduate/entities/graduate.entity';
export { JobApplication } from '../modules/job-seeker/entities/job-application.entity';
export { JobSeeker } from '../modules/job-seeker/entities/job-seeker.entity';
export { Job } from '../modules/job/entities/job.entity';
export { Notification } from '../modules/notification/entities/notification.entity';
export { BountyConfiguration } from '../modules/referral/entities/bounty-configuration.entity';
export { ReferralPartner } from '../modules/referral/entities/referral-partner.entity';
export { Referral } from '../modules/referral/entities/referral.entity';
export { UserRoleEntity } from '../modules/roles/entities/user-role.entity';
export { CreditUsageHistory } from '../modules/subscription/entities/credit-usage-history.entity';
export { VideoJD } from '../modules/video-jd/entities/video-jd.entity';
export { VideoResponse } from '../modules/video-response/entities/video-response.entity';
export { Waitlist } from '../modules/waitlist/entities/waitlist.entity';
export { BaseEntity } from '../shared/entities/base.entity';