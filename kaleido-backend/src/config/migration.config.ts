// Register TypeScript path aliases
import * as path from 'path';
import { register } from 'tsconfig-paths';

// Register paths for both development and production
const baseUrl = path.join(__dirname, '../..');
register({
  baseUrl,
  paths: {
    '@/*': ['src/*'],
    '@modules/*': ['src/modules/*'],
    '@shared/*': ['src/shared/*'],
  },
});

import { config } from 'dotenv';
import { DataSource } from 'typeorm';
import { getDatabaseConfig, getEntityPaths, getMigrationPaths } from './database.config';
import * as entities from '../entities';

// Load environment variables
config();

// Determine if we're in production (compiled JS) or development (TS)
const isProduction = process.env.NODE_ENV === 'production';

// Create a DataSource configuration for migrations
const AppDataSource = new DataSource({
  ...getDatabaseConfig(),
  // In development, use the entity classes directly
  // In production, use glob patterns to find compiled JS files
  entities: isProduction ? getEntityPaths(__dirname) : Object.values(entities),
  migrations: getMigrationPaths(__dirname),
});

export default AppDataSource;
