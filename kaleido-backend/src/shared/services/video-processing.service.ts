import { Injectable, Logger } from '@nestjs/common';
import axios from 'axios';
import * as ffmpeg from 'fluent-ffmpeg';
import * as fs from 'fs';
import * as os from 'os';
import * as path from 'path';
import { v4 as uuidv4 } from 'uuid';

// Try to import ffmpeg-static and ffprobe-static, but handle if they fail
let ffmpegStatic: string | null = null;
let ffprobeStatic: string | null = null;
(async () => {
  try {
    const ffmpegModule = await import('ffmpeg-static');
    ffmpegStatic = ffmpegModule.default;
  } catch (error) {
    console.warn('ffmpeg-static package not available:', error.message);
  }

  try {
    // @ts-expect-error - ffprobe-static has no type definitions
    const ffprobeModule = await import('ffprobe-static');
    ffprobeStatic = ffprobeModule.path;
  } catch (error) {
    console.warn('ffprobe-static package not available:', error.message);
  }
})();

// Set FFmpeg and FFprobe paths
try {
  // Try to use ffmpeg-static first
  if (ffmpegStatic && typeof ffmpegStatic === 'string') {
    // Verify the binary exists before setting the path
    if (fs.existsSync(ffmpegStatic)) {
      ffmpeg.default.setFfmpegPath(ffmpegStatic);
    } else {
      console.warn('FFmpeg binary not found at ffmpeg-static path:', ffmpegStatic);
    }
  } else {
    // Fallback to system ffmpeg - just let fluent-ffmpeg find it
    // Don't set a path, let fluent-ffmpeg find it automatically
  }

  // Set FFprobe path
  if (ffprobeStatic && typeof ffprobeStatic === 'string') {
    // Verify the binary exists before setting the path
    if (fs.existsSync(ffprobeStatic)) {
      ffmpeg.default.setFfprobePath(ffprobeStatic);
    } else {
      console.warn('FFprobe binary not found at ffprobe-static path:', ffprobeStatic);
    }
  } else {
    // Fallback to system ffprobe - just let fluent-ffmpeg find it
    // Don't set a path, let fluent-ffmpeg find it automatically
  }
} catch (error) {
  console.error('Failed to set FFmpeg/FFprobe paths:', error);
}

export interface PlatformSpecs {
  aspectRatio: string;
  maxDuration: number;
  maxFileSize: number;
  recommendedResolution: string;
  format: string;
  description: string;
}

export interface VideoProcessingOptions {
  platform: string;
  quality: 'low' | 'medium' | 'high' | 'original';
  filename?: string;
}

export interface VideoProcessingResult {
  processedVideoPath: string;
  filename: string;
  fileSize: number;
  duration: number;
  resolution: string;
  uploadedUrl?: string; // Add uploaded URL for VideoJD updates
}

@Injectable()
export class VideoProcessingService {
  private readonly logger = new Logger(VideoProcessingService.name);
  private readonly tempDir = path.join(os.tmpdir(), 'video-processing');

  constructor() {
    // Ensure temp directory exists
    if (!fs.existsSync(this.tempDir)) {
      fs.mkdirSync(this.tempDir, { recursive: true });
    }
  }

  async processVideoForPlatform(
    videoUrl: string,
    platformSpecs: PlatformSpecs,
    options: VideoProcessingOptions,
    onProgress?: (progress: number) => void,
  ): Promise<VideoProcessingResult> {
    const jobId = uuidv4();
    this.logger.log(`Starting video processing job ${jobId} for platform: ${options.platform}`);

    try {
      // Download the original video
      onProgress?.(10);
      const originalVideoPath = await this.downloadVideo(videoUrl, jobId);

      onProgress?.(25);

      // Process the video according to platform specifications
      const processedVideoPath = await this.convertVideo(
        originalVideoPath,
        platformSpecs,
        options,
        jobId,
        onProgress,
      );

      onProgress?.(95);

      // Get file stats
      const stats = fs.statSync(processedVideoPath);
      const videoInfo = await this.getVideoInfo(processedVideoPath);

      onProgress?.(100);

      const result: VideoProcessingResult = {
        processedVideoPath,
        filename:
          options.filename || `video-${options.platform}-${Date.now()}.${platformSpecs.format}`,
        fileSize: stats.size,
        duration: videoInfo.duration,
        resolution: videoInfo.resolution,
      };

      this.logger.log(`Video processing job ${jobId} completed successfully`);
      return result;
    } catch (error) {
      this.logger.error(`Video processing job ${jobId} failed:`, error);
      throw new Error(`Video processing failed: ${error.message}`);
    }
  }

  private async downloadVideo(videoUrl: string, jobId: string): Promise<string> {
    const tempVideoPath = path.join(this.tempDir, `${jobId}-original.webm`);

    try {
      this.logger.log(`Downloading video from: ${videoUrl}`);

      const response = await axios({
        method: 'GET',
        url: videoUrl,
        responseType: 'stream',
      });

      const writer = fs.createWriteStream(tempVideoPath);
      response.data.pipe(writer);

      return new Promise((resolve, reject) => {
        writer.on('finish', () => {
          this.logger.log(`Video downloaded to: ${tempVideoPath}`);
          resolve(tempVideoPath);
        });
        writer.on('error', reject);
      });
    } catch (error) {
      this.logger.error(`Failed to download video: ${error.message}`);
      throw error;
    }
  }

  private async convertVideo(
    inputPath: string,
    platformSpecs: PlatformSpecs,
    options: VideoProcessingOptions,
    jobId: string,
    onProgress?: (progress: number) => void,
  ): Promise<string> {
    const outputPath = path.join(this.tempDir, `${jobId}-processed.${platformSpecs.format}`);

    return new Promise((resolve, reject) => {
      this.logger.log(`Converting video for platform: ${options.platform}`);

      try {
        if (typeof ffmpeg.default !== 'function') {
          throw new Error('FFmpeg is not properly initialized as a function');
        }

        let command = ffmpeg.default(inputPath).output(outputPath).format(platformSpecs.format);

        // Ensure this command instance uses the correct ffmpeg path
        if (ffmpegStatic && typeof ffmpegStatic === 'string' && fs.existsSync(ffmpegStatic)) {
          command.setFfmpegPath(ffmpegStatic);
        }

        // Set video codec and quality based on platform and quality setting
        if (platformSpecs.format === 'mp4') {
          command = command.videoCodec('libx264');

          // Set quality based on options
          switch (options.quality) {
            case 'low':
              command = command.videoBitrate('500k').audioBitrate('64k');
              break;
            case 'medium':
              command = command.videoBitrate('1000k').audioBitrate('128k');
              break;
            case 'high':
              command = command.videoBitrate('2000k').audioBitrate('192k');
              break;
            case 'original':
              // Keep original quality
              break;
          }
        }

        // Set resolution based on platform specs
        const [width, height] = platformSpecs.recommendedResolution.split('x').map(Number);
        command = command.size(`${width}x${height}`);

        // Set aspect ratio and cropping if needed
        if (platformSpecs.aspectRatio !== '16:9') {
          // Apply aspect ratio conversion with smart cropping
          command = command.aspect(platformSpecs.aspectRatio);
        }

        // Limit duration if specified
        if (platformSpecs.maxDuration && platformSpecs.maxDuration < 3600) {
          command = command.duration(platformSpecs.maxDuration);
        }

        // Add progress tracking
        command.on('progress', (progress) => {
          if (onProgress && progress.percent) {
            // Map FFmpeg progress (0-100%) to our range (25-95%)
            const mappedProgress = 25 + progress.percent * 0.7;
            onProgress(Math.min(mappedProgress, 95));
          }
        });

        command.on('end', () => {
          this.logger.log(`Video conversion completed: ${outputPath}`);
          resolve(outputPath);
        });

        command.on('error', (error) => {
          this.logger.error(`Video conversion failed: ${error.message}`);
          reject(error);
        });

        // Start the conversion
        command.run();
      } catch (error) {
        this.logger.error(`Failed to initialize FFmpeg command: ${error.message}`);
        reject(new Error(`FFmpeg initialization failed: ${error.message}`));
      }
    });
  }

  private async getVideoInfo(videoPath: string): Promise<{ duration: number; resolution: string }> {
    return new Promise((resolve, reject) => {
      // Create a new ffmpeg instance and ensure it uses the correct ffprobe path
      const command = ffmpeg.default(videoPath);

      // Set ffprobe path if available
      if (ffprobeStatic && typeof ffprobeStatic === 'string' && fs.existsSync(ffprobeStatic)) {
        command.setFfprobePath(ffprobeStatic);
      }

      command.ffprobe((err, metadata) => {
        if (err) {
          this.logger.error(`FFprobe failed: ${err.message}`);
          reject(err);
          return;
        }

        const videoStream = metadata.streams.find((stream) => stream.codec_type === 'video');
        if (!videoStream) {
          reject(new Error('No video stream found'));
          return;
        }

        resolve({
          duration: metadata.format.duration || 0,
          resolution: `${videoStream.width}x${videoStream.height}`,
        });
      });
    });
  }

  async cleanupTempFiles(jobId: string): Promise<void> {
    try {
      const files = fs.readdirSync(this.tempDir);
      const jobFiles = files.filter((file) => file.startsWith(jobId));

      for (const file of jobFiles) {
        const filePath = path.join(this.tempDir, file);
        fs.unlinkSync(filePath);
        this.logger.log(`Cleaned up temp file: ${filePath}`);
      }
    } catch (error) {
      this.logger.warn(`Failed to cleanup temp files for job ${jobId}:`, error);
    }
  }

  async cleanupOldTempFiles(): Promise<void> {
    try {
      const files = fs.readdirSync(this.tempDir);
      const now = Date.now();
      const maxAge = 24 * 60 * 60 * 1000; // 24 hours

      for (const file of files) {
        const filePath = path.join(this.tempDir, file);
        const stats = fs.statSync(filePath);

        if (now - stats.mtime.getTime() > maxAge) {
          fs.unlinkSync(filePath);
          this.logger.log(`Cleaned up old temp file: ${filePath}`);
        }
      }
    } catch (error) {
      this.logger.warn('Failed to cleanup old temp files:', error);
    }
  }

  /**
   * Process video for platform and upload to storage
   * This method integrates video processing with file upload
   */
  async processAndUploadVideo(
    videoUrl: string,
    platformSpecs: PlatformSpecs,
    options: VideoProcessingOptions,
    uploadService: any, // Digital Ocean Spaces service
    uploadPath: string,
    onProgress?: (progress: number) => void,
  ): Promise<VideoProcessingResult> {
    const result = await this.processVideoForPlatform(
      videoUrl,
      platformSpecs,
      options,
      (progress) => {
        // Map processing progress to 0-80% range
        onProgress?.(progress * 0.8);
      },
    );

    try {
      onProgress?.(85);

      // Upload the processed video
      const processedVideoBuffer = fs.readFileSync(result.processedVideoPath);

      const fileObject = {
        buffer: processedVideoBuffer,
        originalname: result.filename,
        mimetype: `video/${platformSpecs.format}`,
        size: result.fileSize,
      } as Express.Multer.File;

      const uploadedUrl = await uploadService.uploadFile(fileObject, uploadPath);

      onProgress?.(95);

      // Clean up temporary files
      const jobId = result.processedVideoPath.split('-')[0].split('/').pop() || '';
      await this.cleanupTempFiles(jobId);

      onProgress?.(100);

      return {
        ...result,
        uploadedUrl,
      };
    } catch (error) {
      this.logger.error(`Failed to upload processed video: ${error.message}`);
      throw error;
    }
  }
}
