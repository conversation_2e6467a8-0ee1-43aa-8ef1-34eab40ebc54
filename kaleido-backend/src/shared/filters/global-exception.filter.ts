import {
  ArgumentsHost,
  Catch,
  ExceptionFilter,
  HttpException,
  HttpStatus,
  Logger,
} from '@nestjs/common';

import { AppError } from '../types/error.types';

@Catch()
export class GlobalExceptionFilter implements ExceptionFilter {
  private readonly logger = new Logger(GlobalExceptionFilter.name);

  catch(exception: unknown, host: ArgumentsHost) {
    const ctx = host.switchToHttp();
    const response = ctx.getResponse();

    let status = HttpStatus.INTERNAL_SERVER_ERROR;
    let message = 'Internal server error';
    let type = 'INTERNAL_SERVER_ERROR';

    if (exception instanceof HttpException) {
      status = exception.getStatus();
      const exceptionResponse = exception.getResponse();

      if (typeof exceptionResponse === 'string') {
        message = exceptionResponse;
      } else {
        message = (exceptionResponse as any).message || message;
        type = (exceptionResponse as any).type || type;

        // For 402 Payment Required errors (credit-related) and 400 Bad Request (validation errors), preserve all custom fields
        if (
          (status === HttpStatus.PAYMENT_REQUIRED || status === HttpStatus.BAD_REQUEST) &&
          typeof exceptionResponse === 'object'
        ) {
          this.logger.error(`${status} - ${message}`, exception);

          // Return the full custom error response for credit and validation errors
          return response.status(status).send({
            statusCode: status,
            type,
            message,
            timestamp: new Date().toISOString(),
            // Preserve custom fields (like validationResult for profile validation, or credit fields for payment errors)
            ...(exceptionResponse as any),
          });
        }
      }
    } else if (exception instanceof AppError) {
      status = exception.statusCode;
      message = exception.message;
      type = exception.type;
    }

    this.logger.error(`${status} - ${message}`, exception);

    response.status(status).send({
      statusCode: status,
      type,
      message,
      timestamp: new Date().toISOString(),
    });
  }
}
