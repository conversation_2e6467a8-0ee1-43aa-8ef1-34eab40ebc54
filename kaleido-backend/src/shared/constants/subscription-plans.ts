import { SubscriptionPlan } from '../enums/subscription-plan.enum';

// This file contains the subscription plan features that match the frontend
// PLAN_FEATURES in kaleido-app/src/components/Subscription/subscription-plans.types.ts

export interface PlanCredits {
  [key: string]: number | string | boolean;
  // Credit allocation
  monthlyCredits: number;
  // Non-credit configuration
  videoJdMaxDuration: number;
  videoJdMonthlyLimit: number; // NEW: Monthly video JD limit to protect against Synthesia costs
  atsIntegration: string;
  databaseRetentionMonths: number;
  // Features
  prioritySupport: boolean;
  customBranding: boolean;
  apiAccess: boolean;
  dedicatedAccountManager: boolean;
}

/**
 * Credit-based plan configurations
 */
export const PLAN_CREDITS: Record<SubscriptionPlan, PlanCredits> = {
  [SubscriptionPlan.FREE]: {
    monthlyCredits: 5, // Freemium - 5 credits per month (from spreadsheet)
    videoJdMaxDuration: 60, // 60 seconds (updated to match test expectations)
    videoJdMonthlyLimit: 1, // Allow 1 video JD on free plan (60 seconds max)
    atsIntegration: 'basic',
    databaseRetentionMonths: 1,
    prioritySupport: false,
    customBranding: false,
    apiAccess: false,
    dedicatedAccountManager: false,
  },
  [SubscriptionPlan.STARTUP]: {
    monthlyCredits: 93, // Startup - $49/month, 93 credits per month (updated pricing)
    videoJdMaxDuration: 180, // 3 minutes
    videoJdMonthlyLimit: 1, // Max 1 video/month for startup plan
    atsIntegration: 'basic',
    databaseRetentionMonths: 1,
    prioritySupport: false,
    customBranding: false,
    apiAccess: false,
    dedicatedAccountManager: false,
  },
  [SubscriptionPlan.STARTER]: {
    monthlyCredits: 359, // Small/Advanced - $150/month, 359 credits per month (updated pricing)
    videoJdMaxDuration: 90, // 90 seconds (updated to match test expectations)
    videoJdMonthlyLimit: 3, // Max 3 videos/month to protect against Synthesia costs
    atsIntegration: 'basic',
    databaseRetentionMonths: 3,
    prioritySupport: true,
    customBranding: true,
    apiAccess: true,
    dedicatedAccountManager: false,
  },
  [SubscriptionPlan.PROFESSIONAL]: {
    monthlyCredits: 885, // Medium/Growth - $360/month, 885 credits per month (updated pricing)
    videoJdMaxDuration: 180, // 3 minutes (updated to match test expectations)
    videoJdMonthlyLimit: 10, // Max 10 videos/month to protect against Synthesia costs
    atsIntegration: 'advanced',
    databaseRetentionMonths: 12,
    prioritySupport: true,
    customBranding: true,
    apiAccess: true,
    dedicatedAccountManager: true,
  },
  [SubscriptionPlan.ENTERPRISE]: {
    monthlyCredits: 9999999, // Unlimited
    videoJdMaxDuration: 9999999, // Unlimited
    videoJdMonthlyLimit: 9999999, // Unlimited videos
    atsIntegration: 'custom',
    databaseRetentionMonths: 24,
    prioritySupport: true,
    customBranding: true,
    apiAccess: true,
    dedicatedAccountManager: true,
  },
};
