import { Console } from 'console';
import chalk from 'chalk';
import boxen from 'boxen';
// @ts-expect-error - gradient-string has no type definitions
import gradient from 'gradient-string';
import figlet from 'figlet';
import Table from 'cli-table3';

// Create Kaleido gradient based on logo colors
const kaleidoGradient = gradient([
  '#FFE66D', // Yellow
  '#FF6B6B', // Pink/Red
  '#C06FBB', // Purple
  '#4ECDC4', // Teal
  '#45B7D1', // Blue
  '#96CEB4', // Green
  '#FFD93D', // Orange/Yellow
]);

// Custom gradient for different elements
const titleGradient = gradient(['#FF6B6B', '#C06FBB', '#4ECDC4']);
const infoGradient = gradient(['#45B7D1', '#96CEB4']);
const urlGradient = gradient(['#FFE66D', '#FFD93D']);

export class Logger {
  private context: string;
  private console: Console;

  constructor(context: string) {
    this.context = context;
    this.console = new Console(process.stdout, process.stderr);
  }

  private formatMessage(level: string, message: string, meta?: Record<string, any>): string {
    const timestamp = chalk.dim(new Date().toISOString());
    const levelColor = this.getLevelColor(level);
    const formattedLevel = levelColor(level.toUpperCase().padEnd(7));
    const contextMessage = chalk.cyan(this.context);

    let fullMessage = `  ${timestamp}  ${formattedLevel}  ${contextMessage}  ${message}`;

    if (meta) {
      fullMessage += '\n' + chalk.dim(JSON.stringify(meta, null, 2));
    }

    return fullMessage;
  }

  private getLevelColor(level: string): any {
    switch (level.toLowerCase()) {
      case 'error':
        return chalk.red.bold;
      case 'warn':
        return chalk.yellow.bold;
      case 'info':
        return chalk.green.bold;
      case 'debug':
        return chalk.blue.bold;
      case 'verbose':
        return chalk.magenta.bold;
      default:
        return chalk.white.bold;
    }
  }

  error(message: string, meta?: Record<string, any>) {
    const formattedMessage = this.formatMessage('error', message, meta);
    this.console.error(formattedMessage);
  }

  warn(message: string, meta?: Record<string, any>) {
    const formattedMessage = this.formatMessage('warn', message, meta);
    this.console.warn(formattedMessage);
  }

  info(message: string, meta?: Record<string, any>) {
    const formattedMessage = this.formatMessage('info', message, meta);
    this.console.info(formattedMessage);
  }

  debug(message: string, meta?: Record<string, any>) {
    const formattedMessage = this.formatMessage('debug', message, meta);
    this.console.debug(formattedMessage);
  }

  async startupBox(messages: string[]) {
    console.clear();

    // Create Kaleido ASCII art
    const kaleidoArt = figlet.textSync('KALEIDO', {
      font: 'ANSI Shadow',
      horizontalLayout: 'default',
      verticalLayout: 'default',
      width: 80,
      whitespaceBreak: true,
    });

    // Display gradient Kaleido logo
    console.log('\n' + kaleidoGradient(kaleidoArt) + '\n');

    // Create subtitle
    const subtitle = titleGradient('✨ Talent Management Platform ✨');
    console.log(chalk.bold(subtitle.padStart(45)) + '\n');

    // Create a beautiful table for API details
    const table = new Table({
      style: {
        head: [],
        border: [],
      },
      chars: {
        top: '═',
        'top-mid': '╤',
        'top-left': '╔',
        'top-right': '╗',
        bottom: '═',
        'bottom-mid': '╧',
        'bottom-left': '╚',
        'bottom-right': '╝',
        left: '║',
        'left-mid': '╟',
        mid: '─',
        'mid-mid': '┼',
        right: '║',
        'right-mid': '╢',
        middle: '│',
      },
    });

    // Parse messages and add to table
    const apiDetails: [string, string][] = [];
    const otherInfo: string[] = [];

    messages.forEach((msg) => {
      if (msg.includes('•')) {
        const colonIndex = msg.indexOf(':');
        if (colonIndex !== -1) {
          const label = msg.substring(0, colonIndex).trim();
          const value = msg.substring(colonIndex + 1).trim();
          const cleanLabel = label.replace('•', '').trim();

          if (value) {
            // Color URLs differently
            const coloredValue = value.includes('http') ? urlGradient(value) : infoGradient(value);

            apiDetails.push([chalk.bold(infoGradient(cleanLabel)), coloredValue]);
          }
        }
      } else if (
        msg &&
        !msg.includes('API Details') &&
        !msg.includes('Application Successfully Started')
      ) {
        otherInfo.push(msg);
      }
    });

    // Add rows to table
    apiDetails.forEach(([label, value]) => {
      table.push([label, value]);
    });

    // Add other info if any
    if (otherInfo.length > 0) {
      otherInfo.forEach((info) => {
        table.push([{ colSpan: 2, content: chalk.dim(info) }]);
      });
    }

    // Create the final box with success message
    const successBox = boxen(chalk.bold(kaleidoGradient('🚀 Application Successfully Started')), {
      padding: 1,
      margin: 1,
      borderStyle: 'double',
      borderColor: 'cyan',
      textAlignment: 'center',
    });

    console.log(successBox);
    console.log(table.toString());

    // Add a decorative footer
    const footer = kaleidoGradient('═'.repeat(60));
    console.log('\n' + footer + '\n');
  }
}
