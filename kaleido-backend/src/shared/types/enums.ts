import { UserRole } from '../../common/enums/role.enum';

import { CandidateStatus, ContactMethod as Candidate<PERSON><PERSON>actMethod } from './candidate.types';

export enum NotificationType {
  VIDEO_JD_GENERATING = 'VIDEO_JD_GENERATING',
  VIDEO_JD_READY = 'VIDEO_JD_READY',
  VIDEO_INTERVIEW_SUBMITTED = 'VIDEO_INTERVIEW_SUBMITTED',
  VIDEO_INTERVIEW_DONE = 'VIDEO_INTERVIEW_DONE',
  NEW_APPLICATION = 'NEW_APPLICATION',
  APPLICATION_WITHDRAWN = 'APPLICATION_WITHDRAWN',
}

export enum ApplicationStatus {
  PENDING = 'PENDING',
  REVIEWING = 'REVIEWING',
  SHORTLISTED = 'SHORTLISTED',
  REJECTED = 'REJECTED',
  WITHDRAWN = 'WITHDRAWN',
  APPLIED = 'APPLIED',
  HIRED = 'HIRED',
}

export enum ApprovalStatus {
  PENDING = 'PENDING',
  APPROVED = 'APPROVED',
  REJECTED = 'REJECTED',
  CANCELED = 'CANCELED',
}

export enum ApprovalType {
  OFFER = 'OFFER',
  HIRING = 'HIRING',
  SALARY = 'SALARY',
}

export enum ApprovalRole {
  HIRING_MANAGER = 'HIRING_MANAGER',
  DEPARTMENT_HEAD = 'DEPARTMENT_HEAD',
  HR_MANAGER = 'HR_MANAGER',
  FINANCE_APPROVER = 'FINANCE_APPROVER',
  EXECUTIVE_APPROVER = 'EXECUTIVE_APPROVER',
}

export enum RemotePreference {
  REMOTE = 'remote',
  HYBRID = 'hybrid',
  ONSITE = 'onsite',
}

// Re-export from candidate.types.ts
export { CandidateContactMethod as ContactMethod, CandidateStatus, UserRole };

export enum SalaryPeriod {
  HOURLY = 'HOURLY',
  MONTHLY = 'MONTHLY',
  YEARLY = 'YEARLY',
}

export enum ProfileVisibility {
  PUBLIC = 'PUBLIC',
  PRIVATE = 'PRIVATE',
  CONNECTIONS_ONLY = 'CONNECTIONS_ONLY',
}
