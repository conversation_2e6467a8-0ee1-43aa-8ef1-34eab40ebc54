import { Request as ExpressRequest, Response as ExpressResponse } from 'express';
import { Express as ExpressType } from 'express-serve-static-core';
import { Multer as MulterType } from 'multer';

declare global {
  namespace Express {
    interface Request extends ExpressRequest {
      user?: any;
    }
    // Response type is used as-is from ExpressResponse
    type Response = ExpressResponse;
    interface Multer extends MulterType {
      File: MulterFile;
    }
  }
}

interface MulterFile {
  fieldname: string;
  originalname: string;
  encoding: string;
  mimetype: string;
  size: number;
  destination?: string;
  filename?: string;
  path?: string;
  buffer: Buffer;
}
