// Register TypeScript path aliases
import './register-ts-paths';

import { config } from 'dotenv';
import { DataSource } from 'typeorm';

// Import all entities
import { CreditPurchase } from '../entities/CreditPurchase.entity';
import { ApprovalStep } from '../modules/approval/entities/approval-step.entity';
import { Approval } from '../modules/approval/entities/approval.entity';
import { Offer } from '../modules/approval/entities/offer.entity';
import { RecruitmentAssessment } from '../modules/assessment/entities/recruitment-assessment.entity';
import { CandidateApplication } from '../modules/candidate/entities/candidate-application.entity';
import { CandidateEvaluation } from '../modules/candidate/entities/candidate-evaluation.entity';
import { CandidateProfile } from '../modules/candidate/entities/candidate-profile.entity';
import { Candidate } from '../modules/candidate/entities/candidate.entity';
import { ScoutedCandidate } from '../modules/candidate/entities/scouted-candidate.entity';
import { CareerInsight } from '../modules/career-insights/entities/career-insight.entity';
import { Company } from '../modules/company/entities/company.entity';
import { CompanyMember } from '../modules/company/entities/company-member.entity';
import { CompanyInvitation } from '../modules/company/entities/company-invitation.entity';
import { CandidateComparison } from '../modules/comparison/entities/candidate-comparison.entity';
import { Contact } from '../modules/contact/entities/contact.entity';
import { EmailHistory } from '../modules/email/entities/email-history.entity';
import { FeatureFlagEntity } from '../modules/feature-flags/entities/feature-flag.entity';
import { Feedback } from '../modules/feedback/entities/feedback.entity';
import { Graduate } from '../modules/graduate/entities/graduate.entity';
import { JobApplication } from '../modules/job-seeker/entities/job-application.entity';
import { JobSeeker } from '../modules/job-seeker/entities/job-seeker.entity';
import { Job } from '../modules/job/entities/job.entity';
import { Notification } from '../modules/notification/entities/notification.entity';
import { BountyConfiguration } from '../modules/referral/entities/bounty-configuration.entity';
import { ReferralPartner } from '../modules/referral/entities/referral-partner.entity';
import { Referral } from '../modules/referral/entities/referral.entity';
import { UserRoleEntity } from '../modules/roles/entities/user-role.entity';
import { CreditUsageHistory } from '../modules/subscription/entities/credit-usage-history.entity';
import { VideoJD } from '../modules/video-jd/entities/video-jd.entity';
import { VideoResponse } from '../modules/video-response/entities/video-response.entity';
import { Waitlist } from '../modules/waitlist/entities/waitlist.entity';

// Load environment variables
config();

/**
 * Schema Synchronization Script
 *
 * This script synchronizes the database schema with TypeORM entities.
 * It will create missing tables and columns but won't drop existing data.
 *
 * This is safer than the generate-schema script as it preserves existing data.
 */
async function synchronizeSchema() {
  console.log('🔄 Starting database schema synchronization...');
  console.log('📝 This will create missing tables/columns but preserve existing data.');

  // Get database connection info from environment variables
  const dbHost = process.env.DB_HOST || 'localhost';
  const dbPort = parseInt(process.env.DB_PORT || '5432');
  const dbName = process.env.DB_NAME || '';
  const dbUsername = process.env.DB_USERNAME || '';
  const dbPassword = process.env.DB_PASSWORD || '';

  console.log(`📡 Connecting to database: ${dbHost}:${dbPort}/${dbName}`);

  // Create a DataSource with synchronize enabled but no schema dropping
  const AppDataSource = new DataSource({
    type: 'postgres',
    host: dbHost,
    port: dbPort,
    username: dbUsername,
    password: dbPassword,
    database: dbName,
    entities: [
      // Core entities
      Company,
      CompanyMember,
      CompanyInvitation,
      Candidate,
      Job,
      Notification,
      VideoJD,
      VideoResponse,
      JobSeeker,
      JobApplication,
      UserRoleEntity,
      ScoutedCandidate,
      CreditPurchase,

      // Additional entities
      Graduate,
      Approval,
      ApprovalStep,
      Offer,
      CandidateProfile,
      CandidateApplication,
      CandidateEvaluation,
      Feedback,
      CreditUsageHistory,
      Waitlist,
      Contact,
      FeatureFlagEntity,
      
      // Assessment and Career entities
      RecruitmentAssessment,
      CareerInsight,
      
      // Comparison entity
      CandidateComparison,
      
      // Email entity
      EmailHistory,
      
      // Referral entities
      BountyConfiguration,
      ReferralPartner,
      Referral,
    ],
    synchronize: true, // This will create/update the schema
    dropSchema: false, // This preserves existing data
    ssl:
      process.env.DB_SSL === 'require'
        ? {
            rejectUnauthorized: false,
          }
        : false,
  });

  try {
    // Initialize the data source
    console.log('🔌 Initializing database connection...');
    await AppDataSource.initialize();
    console.log('✅ Database connection established.');
    
    // Perform schema synchronization
    console.log('🔄 Running schema synchronization...');
    await AppDataSource.synchronize();

    console.log('✅ Schema synchronization completed successfully!');
    console.log('📋 Missing tables and columns have been created.');
    console.log('💾 Existing data has been preserved.');

    // Close the connection
    await AppDataSource.destroy();
    console.log('🔌 Database connection closed.');

    console.log('');
    console.log('✨ Schema synchronization process completed successfully!');
    console.log('');
    console.log('📝 Next steps:');
    console.log('1. Verify the schema changes are correct');
    console.log('2. Consider running migrations to ensure everything is up to date:');
    console.log('   pnpm migration:run');
  } catch (error) {
    console.error('❌ Error during schema synchronization:', error);
    process.exit(1);
  }
}

// Run the schema synchronization function
synchronizeSchema();
