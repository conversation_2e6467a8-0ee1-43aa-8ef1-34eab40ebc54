import { createConnection } from 'typeorm';
import * as dotenv from 'dotenv';
import * as path from 'path';

// Load production environment variables
dotenv.config({ path: path.resolve(__dirname, '../../.env.production') });

async function verifyComparisonSchema() {
  console.log('🔍 Verifying candidate_comparisons schema...\n');

  // Build connection configuration from individual env vars
  const connection = await createConnection({
    type: 'postgres',
    host: process.env.DB_HOST,
    port: parseInt(process.env.DB_PORT || '5432'),
    username: process.env.DB_USERNAME,
    password: process.env.DB_PASSWORD,
    database: process.env.DB_NAME,
    ssl: process.env.DB_SSL === 'require' ? { rejectUnauthorized: false } : false,
    logging: false, // Disable query logging for cleaner output
  });

  try {
    // Get all columns
    const columns = await connection.query(`
      SELECT 
        column_name, 
        data_type, 
        udt_name,
        is_nullable,
        column_default
      FROM information_schema.columns
      WHERE table_name = 'candidate_comparisons'
      AND table_schema = 'public'
      ORDER BY ordinal_position
    `);

    console.log('📋 Current database schema for candidate_comparisons:');
    console.table(
      columns.map((col: any) => ({
        column: col.column_name,
        type: col.data_type,
        udt: col.udt_name,
        nullable: col.is_nullable,
        default: col.column_default ? col.column_default.substring(0, 30) : null
      }))
    );

    // Check for specific problematic columns
    const hasComparisonData = columns.some((col: any) => col.column_name === 'comparisonData');
    const hasCandidateIds = columns.some((col: any) => col.column_name === 'candidateIds');

    console.log('\n🔍 Column Analysis:');
    console.log(`- comparisonData column exists: ${hasComparisonData ? '❌ YES (This is the problem!)' : '✅ NO'}`);
    console.log(`- candidateIds column exists: ${hasCandidateIds ? '✅ YES' : '❌ NO'}`);

    if (hasComparisonData) {
      const comparisonDataCol = columns.find((col: any) => col.column_name === 'comparisonData');
      console.log(`\n⚠️  Found legacy comparisonData column:`);
      console.log(`  - Type: ${comparisonDataCol.data_type}`);
      console.log(`  - Nullable: ${comparisonDataCol.is_nullable}`);
      console.log('\nThis column is NOT in the entity definition and is causing the insert to fail!');
    }

    // Check row count
    const rowCount = await connection.query(
      'SELECT COUNT(*) as count FROM candidate_comparisons'
    );
    console.log(`\n📊 Table has ${rowCount[0].count} rows`);

    // List expected columns based on entity
    console.log('\n📝 Expected columns based on entity definition:');
    const expectedColumns = [
      'id', 'clientId', 'jobId', 'candidateIds', 'comparisonTitle',
      'userPrompt', 'comparisonCriteria', 'comparisonResults', 'visualData',
      'status', 'performedBy', 'expiresAt', 'comparisonType', 'metadata',
      'createdAt', 'updatedAt'
    ];
    
    expectedColumns.forEach(col => {
      const exists = columns.some((dbCol: any) => dbCol.column_name === col);
      console.log(`  - ${col}: ${exists ? '✅' : '❌'}`);
    });

    // Find columns in DB but not in entity
    console.log('\n⚠️  Columns in database but NOT in entity:');
    columns.forEach((col: any) => {
      if (!expectedColumns.includes(col.column_name)) {
        console.log(`  - ${col.column_name} (${col.data_type})`);
      }
    });

  } catch (error) {
    console.error('❌ Error verifying schema:', error);
    throw error;
  } finally {
    await connection.close();
  }
}

// Run the verification
verifyComparisonSchema()
  .then(() => {
    console.log('\n✅ Schema verification completed!');
    process.exit(0);
  })
  .catch((error) => {
    console.error('\n❌ Schema verification failed:', error);
    process.exit(1);
  });