import { createConnection } from 'typeorm';
import * as dotenv from 'dotenv';
import * as path from 'path';

// Load local/development environment variables
dotenv.config({ path: path.resolve(__dirname, '../../.env') });

async function recreateCandidateComparisonsTableLocal() {
  console.log('🔧 Recreating candidate_comparisons table in LOCAL database...\n');

  // Build connection configuration from individual env vars
  const connection = await createConnection({
    type: 'postgres',
    host: process.env.DB_HOST || 'localhost',
    port: parseInt(process.env.DB_PORT || '5432'),
    username: process.env.DB_USERNAME || 'postgres',
    password: process.env.DB_PASSWORD,
    database: process.env.DB_NAME || 'kaleido-talent-db',
    ssl: process.env.DB_SSL === 'true',
    logging: true,
  });

  try {
    // Ensure UUID extension is enabled
    console.log('🔌 Ensuring UUID extension is enabled...');
    await connection.query(`CREATE EXTENSION IF NOT EXISTS "uuid-ossp"`);
    console.log('✅ UUID extension ready\n');

    // First, check if there's any data we need to worry about
    const rowCount = await connection.query(
      `SELECT COUNT(*) as count FROM candidate_comparisons`
    ).catch(() => [{ count: 0 }]);
    
    console.log(`📊 Current table has ${rowCount[0].count} rows\n`);

    if (parseInt(rowCount[0].count) > 0) {
      console.log('⚠️  WARNING: Table contains data! Backing up...\n');
      
      // Create a backup table
      await connection.query(`
        CREATE TABLE candidate_comparisons_backup AS 
        SELECT * FROM candidate_comparisons
      `);
      console.log('✅ Backup created as candidate_comparisons_backup\n');
    }

    // Drop the existing table
    console.log('🗑️  Dropping existing table...');
    await connection.query(`DROP TABLE IF EXISTS candidate_comparisons CASCADE`);
    console.log('✅ Table dropped\n');

    // Create the table with the correct schema matching our entity
    console.log('🏗️  Creating new table with proper schema...');
    await connection.query(`
      CREATE TABLE candidate_comparisons (
        "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
        "clientId" character varying NOT NULL,
        "jobId" uuid NOT NULL,
        "candidateIds" jsonb NOT NULL,
        "comparisonTitle" character varying(500) NOT NULL,
        "userPrompt" text,
        "comparisonCriteria" jsonb NOT NULL,
        "comparisonResults" jsonb,
        "visualData" jsonb,
        "status" character varying NOT NULL DEFAULT 'pending',
        "performedBy" character varying,
        "expiresAt" timestamp without time zone,
        "comparisonType" character varying,
        "metadata" jsonb,
        "createdAt" timestamp without time zone NOT NULL DEFAULT now(),
        "updatedAt" timestamp without time zone NOT NULL DEFAULT now(),
        CONSTRAINT "PK_candidate_comparisons" PRIMARY KEY ("id")
      )
    `);
    console.log('✅ Table created\n');

    // Create indexes
    console.log('📇 Creating indexes...');
    
    const indexes = [
      {
        name: 'idx_comparison_client_job',
        query: `CREATE INDEX "idx_comparison_client_job" ON candidate_comparisons ("clientId", "jobId")`
      },
      {
        name: 'idx_comparison_status',
        query: `CREATE INDEX "idx_comparison_status" ON candidate_comparisons ("status")`
      },
      {
        name: 'idx_comparison_created',
        query: `CREATE INDEX "idx_comparison_created" ON candidate_comparisons ("createdAt")`
      },
      {
        name: 'idx_comparison_client_id',
        query: `CREATE INDEX "idx_comparison_client_id" ON candidate_comparisons ("clientId")`
      },
      {
        name: 'idx_comparison_job_id',
        query: `CREATE INDEX "idx_comparison_job_id" ON candidate_comparisons ("jobId")`
      }
    ];

    for (const index of indexes) {
      await connection.query(index.query);
      console.log(`✅ Created index: ${index.name}`);
    }

    // Add foreign key constraint to jobs table (if jobs table exists)
    console.log('\n🔗 Adding foreign key constraints...');
    try {
      await connection.query(`
        ALTER TABLE candidate_comparisons 
        ADD CONSTRAINT "FK_candidate_comparisons_job" 
        FOREIGN KEY ("jobId") REFERENCES jobs("id") 
        ON DELETE CASCADE ON UPDATE NO ACTION
      `);
      console.log('✅ Foreign key constraint added');
    } catch (error: any) {
      if (error.code === '42P01') {
        console.log('⚠️  Skipping foreign key constraint - jobs table not found in this database');
      } else {
        throw error;
      }
    }

    // Verify the final schema
    console.log('\n📋 Final schema verification:');
    const finalSchema = await connection.query(`
      SELECT 
        column_name, 
        data_type, 
        udt_name,
        is_nullable,
        column_default
      FROM information_schema.columns
      WHERE table_name = 'candidate_comparisons'
      AND table_schema = 'public'
      ORDER BY ordinal_position
    `);

    console.table(
      finalSchema.map((col: any) => ({
        column: col.column_name,
        type: col.data_type,
        nullable: col.is_nullable,
        default: col.column_default ? col.column_default.substring(0, 30) + '...' : null
      }))
    );

    // Check if we have a backup to restore
    if (parseInt(rowCount[0].count) > 0) {
      console.log('\n💾 Data restoration options:');
      console.log('The old data is saved in candidate_comparisons_backup table');
      console.log('You can manually migrate relevant data if needed');
    }

  } catch (error) {
    console.error('❌ Error recreating table:', error);
    throw error;
  } finally {
    await connection.close();
  }
}

// Add confirmation prompt
async function main() {
  console.log('⚠️  WARNING: This script will DROP and RECREATE the candidate_comparisons table in LOCAL database!');
  console.log('All existing data will be backed up to candidate_comparisons_backup');
  console.log('\nEnvironment:', process.env.NODE_ENV || 'development');
  console.log('Database:', process.env.DB_NAME || 'kaleido-talent-db');
  console.log('Host:', process.env.DB_HOST || 'localhost');
  
  console.log('\nProceeding in 3 seconds... (Ctrl+C to cancel)\n');
  
  await new Promise(resolve => setTimeout(resolve, 3000));
  
  await recreateCandidateComparisonsTableLocal();
}

// Run the script
main()
  .then(() => {
    console.log('\n✅ Local table recreation completed successfully!');
    process.exit(0);
  })
  .catch((error) => {
    console.error('\n❌ Local table recreation failed:', error);
    process.exit(1);
  });