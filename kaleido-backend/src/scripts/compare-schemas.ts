import { createConnection, Connection } from 'typeorm';
import * as dotenv from 'dotenv';
import * as path from 'path';

// Load both environment files
const localEnv = dotenv.parse(require('fs').readFileSync(path.resolve(__dirname, '../../.env')));
const prodEnv = dotenv.parse(require('fs').readFileSync(path.resolve(__dirname, '../../.env.production.local')));

async function compareSchemas() {
  console.log('🔍 Comparing local and production schemas for candidate_comparisons...\n');

  let localConnection: Connection | null = null;
  let prodConnection: Connection | null = null;

  try {
    // Connect to local database
    localConnection = await createConnection({
      name: 'local',
      type: 'postgres',
      host: localEnv.DB_HOST || 'localhost',
      port: parseInt(localEnv.DB_PORT || '5432'),
      username: localEnv.DB_USERNAME || 'postgres',
      password: localEnv.DB_PASSWORD,
      database: localEnv.DB_NAME || 'kaleido-talent-db',
      ssl: localEnv.DB_SSL === 'true',
      logging: false,
    });

    // Connect to production database
    prodConnection = await createConnection({
      name: 'production',
      type: 'postgres',
      host: prodEnv.DB_HOST,
      port: parseInt(prodEnv.DB_PORT || '5432'),
      username: prodEnv.DB_USERNAME,
      password: prodEnv.DB_PASSWORD,
      database: prodEnv.DB_NAME,
      ssl: prodEnv.DB_SSL === 'require' ? { rejectUnauthorized: false } : false,
      logging: false,
    });

    // Get schema from both databases
    const localSchema = await localConnection.query(`
      SELECT 
        column_name, 
        data_type, 
        udt_name,
        is_nullable,
        character_maximum_length,
        column_default
      FROM information_schema.columns
      WHERE table_name = 'candidate_comparisons'
      AND table_schema = 'public'
      ORDER BY ordinal_position
    `);

    const prodSchema = await prodConnection.query(`
      SELECT 
        column_name, 
        data_type, 
        udt_name,
        is_nullable,
        character_maximum_length,
        column_default
      FROM information_schema.columns
      WHERE table_name = 'candidate_comparisons'
      AND table_schema = 'public'
      ORDER BY ordinal_position
    `);

    console.log('📊 Schema Comparison:\n');
    console.log(`Local columns: ${localSchema.length}`);
    console.log(`Production columns: ${prodSchema.length}`);

    if (localSchema.length !== prodSchema.length) {
      console.log('\n❌ Column count mismatch!');
    } else {
      console.log('\n✅ Column count matches');
    }

    // Compare each column
    console.log('\n📋 Column-by-column comparison:');
    const maxColumns = Math.max(localSchema.length, prodSchema.length);

    for (let i = 0; i < maxColumns; i++) {
      const localCol = localSchema[i];
      const prodCol = prodSchema[i];

      if (!localCol) {
        console.log(`\n❌ Column ${i + 1}: Missing in local`);
        console.log(`   Production: ${prodCol.column_name} (${prodCol.data_type})`);
        continue;
      }

      if (!prodCol) {
        console.log(`\n❌ Column ${i + 1}: Missing in production`);
        console.log(`   Local: ${localCol.column_name} (${localCol.data_type})`);
        continue;
      }

      const match = 
        localCol.column_name === prodCol.column_name &&
        localCol.data_type === prodCol.data_type &&
        localCol.udt_name === prodCol.udt_name &&
        localCol.is_nullable === prodCol.is_nullable;

      if (match) {
        console.log(`✅ ${localCol.column_name}: Identical`);
      } else {
        console.log(`\n❌ ${localCol.column_name || prodCol.column_name}: Mismatch`);
        console.log(`   Local:      ${localCol.data_type} (${localCol.udt_name}), nullable: ${localCol.is_nullable}`);
        console.log(`   Production: ${prodCol.data_type} (${prodCol.udt_name}), nullable: ${prodCol.is_nullable}`);
      }
    }

    // Check indexes
    console.log('\n📇 Checking indexes...');
    
    const localIndexes = await localConnection.query(`
      SELECT indexname 
      FROM pg_indexes 
      WHERE tablename = 'candidate_comparisons' 
      AND schemaname = 'public'
      ORDER BY indexname
    `);

    const prodIndexes = await prodConnection.query(`
      SELECT indexname 
      FROM pg_indexes 
      WHERE tablename = 'candidate_comparisons' 
      AND schemaname = 'public'
      ORDER BY indexname
    `);

    console.log(`\nLocal indexes: ${localIndexes.length}`);
    console.log(`Production indexes: ${prodIndexes.length}`);

    if (localIndexes.length === prodIndexes.length) {
      console.log('✅ Index count matches');
    } else {
      console.log('❌ Index count mismatch');
    }

  } catch (error) {
    console.error('❌ Error comparing schemas:', error);
    throw error;
  } finally {
    if (localConnection) await localConnection.close();
    if (prodConnection) await prodConnection.close();
  }
}

// Run the comparison
compareSchemas()
  .then(() => {
    console.log('\n✅ Schema comparison completed!');
    process.exit(0);
  })
  .catch((error) => {
    console.error('\n❌ Schema comparison failed:', error);
    process.exit(1);
  });