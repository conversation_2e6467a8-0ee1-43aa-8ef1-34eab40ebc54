import { Attach<PERSON>, ParsedMail, simpleParser } from 'mailparser';
import * as mammoth from 'mammoth';
// @ts-expect-error - pdf-parse doesn't have type definitions
import * as pdfParse from 'pdf-parse';
import { PDFExtract, PDFExtractOptions } from 'pdf.js-extract';
import * as readline from 'readline';
import sanitizeHtml from 'sanitize-html';
// @ts-expect-error - word-extractor doesn't have type definitions
import WordExtractor from 'word-extractor';
import * as unzipper from 'unzipper';
import { Readable } from 'stream';

const extractor = new WordExtractor();

const pdfExtract = new PDFExtract();
const options: PDFExtractOptions = { normalizeWhitespace: true };

// File extract
async function extractTextFromPDFBuffer(pdfBuffer: Buffer) {
  try {
    const res = await pdfExtract.extractBuffer(pdfBuffer, options);
    let text = '';
    for (const page of res.pages) {
      const pageText = page.content.map((c) => c.str).join('\n');
      text += pageText + '\n';
    }
    return sanitizeContent(text);
  } catch (error) {
    console.error('Error extracting text from PDF buffer using pdf.js-extract:', error);
    throw error; // Re-throw the error to handle it in the calling function
  }
}

async function extractTextFromWordBuffer(buffer: Buffer) {
  const doc = await extractor.extract(buffer);
  const bod = doc.getBody();
  return bod;
}

async function parseMboxFile(file: { buffer: any }) {
  const readable = Readable.from(file.buffer);
  const rl = readline.createInterface({
    input: readable,
    crlfDelay: Infinity,
  });

  const emails = [];
  let currentEmail = '';

  for await (const line of rl) {
    if (line.startsWith('From ')) {
      if (currentEmail) {
        try {
          const parsed = await simpleParser(currentEmail);
          emails.push(formatEmail(parsed));
        } catch (error) {
          console.error('Error parsing email:', error);
        }
      }
      currentEmail = '';
    } else {
      currentEmail += line + '\n';
    }
  }

  // Parse the last email
  if (currentEmail) {
    try {
      const parsed = await simpleParser(currentEmail);
      emails.push(formatEmail(parsed));
    } catch (error) {
      console.error('Error parsing last email:', error);
    }
  }

  return emails;
}

function formatEmail(parsed: ParsedMail) {
  let fromText = '';
  let toText = '';

  if (parsed.from) {
    if (typeof parsed.from === 'object' && 'text' in parsed.from) {
      fromText = parsed.from.text as string;
    }
  }

  if (parsed.to) {
    if (typeof parsed.to === 'object' && 'text' in parsed.to) {
      toText = parsed.to.text as string;
    }
  }

  return {
    subject: parsed.subject,
    from: fromText,
    to: toText,
    date: parsed.date,
    text: parsed.text,
    // html: parsed.html,
    attachments: parsed.attachments.map(formatAttachment),
  };
}

function formatAttachment(attachment: Attachment) {
  return {
    filename: attachment.filename,
    contentType: attachment.contentType,
    size: attachment.size,
    content: attachment.content,
  };
}

function sanitizeContent(content: string) {
  return sanitizeHtml(content, {
    allowedTags: [],
    allowedAttributes: {},
  }).replace(/\x00/g, '');
}

export async function extractFileContentOnly(file: {
  mimetype: string;
  buffer: Buffer;
  originalname: string;
}) {
  let failedFileName: string | undefined;
  const extractedContents: { fileName: string; content: string }[] = [];

  // Check if the file size is zero
  if (file.buffer.length === 0) {
    return { extractedContents, failedFileName: file.originalname }; // Return early if file is empty
  }

  try {
    switch (file.mimetype) {
      case 'application/pdf':
        try {
          const data = await pdfParse(file.buffer);
          extractedContents.push({
            fileName: file.originalname,
            content: sanitizeContent(data.text),
          });
        } catch (pdfParseError) {
          try {
            const content = await extractPDFContent({ buffer: file.buffer });
            extractedContents.push({ fileName: file.originalname, content });
          } catch (pdfLibError) {
            failedFileName = file.originalname;
          }
        }
        break;
      case 'application/vnd.openxmlformats-officedocument.wordprocessingml.document':
      case 'application/msword':
        try {
          const result = await mammoth.extractRawText({ buffer: file.buffer });
          extractedContents.push({
            fileName: file.originalname,
            content: sanitizeContent(result.value.toString()),
          });
        } catch (mammothError) {
          try {
            const content = await extractWordContent({ buffer: file.buffer });
            extractedContents.push({
              fileName: file.originalname,
              content: sanitizeContent(content),
            });
          } catch (officeParserError) {}
        }
        break;
      case 'application/zip':
        try {
          const zipContent = await extractZipContent(file);
          extractedContents.push(...zipContent);
        } catch (zipError) {
          failedFileName = file.originalname;
        }
        break;
      case 'message/rfc822':
        try {
          const emailContent = await extractEmailContent(file);
          extractedContents.push(...emailContent);
        } catch (emailError) {
          failedFileName = file.originalname;
        }
        break;
      case 'application/mbox':
      case 'application/octet-stream':
        try {
          const emails: any = await parseMboxFile(file);
          for (const email of emails) {
            const emailContents = await processEmail(email, true);
            extractedContents.push(...emailContents);
          }
        } catch (error) {
          failedFileName = file.originalname;
        }
        break;
      default:
        throw new Error('Unsupported file type: ' + file.mimetype);
    }
  } catch (error) {
    failedFileName = file.originalname;
  }

  return { extractedContents, failedFileName };
}

export async function extractFileContents(
  files: Express.Multer.File[],
  existingResumes: string[] = [],
) {
  const results = {
    contents: [] as { fileName: string; content: string }[],
    failedFileNames: [] as string[],
    duplicateFiles: [] as { fileName: string; exists: boolean }[],
  };

  // Process files in parallel for better performance
  const extractionPromises = files.map(async (file) => {
    const { extractedContents, failedFileName } = await extractFileContentOnly(file);

    return {
      extractedContents,
      failedFileName,
    };
  });

  const extractionResults = await Promise.all(extractionPromises);

  // Combine all results
  for (const result of extractionResults) {
    if (result.failedFileName) {
      results.failedFileNames.push(result.failedFileName);
    }

    for (const content of result.extractedContents) {
      if (existingResumes.includes(content.fileName)) {
        results.duplicateFiles.push({ fileName: content.fileName, exists: true });
      } else {
        results.contents.push(content);
      }
    }
  }

  return results;
}

async function extractPDFContent(file: { buffer: Buffer; originalname?: string }) {
  let content = '';
  try {
    const data = await pdfParse(file.buffer);
    content = sanitizeContent(data.text);
  } catch (pdfParseError) {
    if (file.originalname) {
    }
    try {
      content = await extractTextFromPDFBuffer(file.buffer);
    } catch (pdfLibError) {
      if (file.originalname) {
      }
    }
  }
  return content;
}

async function extractWordContent(file: { buffer: Buffer; originalname?: string }) {
  let content = '';
  try {
    content = await extractTextFromWordBuffer(file.buffer);
  } catch (officeParserError) {
    if (file.originalname) {
    }
  }
  return content;
}

const getMimeType = (fileName: string) => {
  const extension = fileName.split('.').pop()?.toLowerCase() || '';
  switch (extension) {
    case 'pdf':
      return 'application/pdf';
    case 'docx':
      return 'application/vnd.openxmlformats-officedocument.wordprocessingml.document';
    case 'doc':
      return 'application/msword';
    default:
      return 'application/octet-stream';
  }
};

async function createFileContent(file: any) {
  const mimetype = getMimeType(file.path);
  const fileContent = await file.buffer();
  const buffer = Buffer.from(fileContent);
  return {
    buffer,
    name: file.path,
    originalname: file.path,
    mimetype: mimetype,
    type: mimetype,
    size: file.uncompressedSize,
  };
}

async function extractZipContent(zipFile: any) {
  const extractedContents = [];
  const directory = await unzipper.Open.buffer(zipFile.buffer || zipFile.content);
  const filteredFiles = directory.files.filter((file: any) => !file.path.startsWith('__MACOSX/'));

  for (const file of filteredFiles) {
    if (/\.(pdf|docx)$/i.test(file.path)) {
      let extractedContent = '';
      const zipFile = await createFileContent(file);
      if (/\.pdf$/i.test(zipFile.originalname)) {
        extractedContent = await extractPDFContent(zipFile);
      } else if (/\.docx$/i.test(zipFile.originalname)) {
        extractedContent = await extractWordContent(zipFile);
      }

      // Store extracted content as individual records
      extractedContents.push({
        fileName: sanitizeContent(zipFile.originalname),
        content: sanitizeContent(extractedContent),
      });
    }
  }
  return extractedContents;
}

async function extractEmailContent(file: any) {
  const extractedContents = [];

  if (/\.mbox$/i.test(file.originalname)) {
    const emails: any = await parseMboxFile(file);

    for (const email of emails) {
      //
      const emailContents = await processEmail(email);
      extractedContents.push(...emailContents);
    }
  } else {
    const emailContents = await processEmail(file);
    extractedContents.push(...emailContents);
  }

  return extractedContents;
}

function extractNameAndEmail(input: string) {
  const nameRegex = /"([^"]*)"/;
  const emailRegex = /<([^>]*)>/;

  const nameMatch = input.match(nameRegex);
  const emailMatch = input.match(emailRegex);

  const name = nameMatch ? nameMatch[1] : null;
  const email = emailMatch ? emailMatch[1] : null;

  return { name, email };
}

async function processEmail(file: any, isMbox = false) {
  const extractedContents = [];

  // Create a Readable stream from the file content
  const stream = new Readable();
  stream.push(file.buffer);
  stream.push(null);

  const parsedEmail = isMbox ? file : await simpleParser(stream);

  let senderName: string = '';
  let senderEmail: string = '';

  if (isMbox) {
    const { name, email } = extractNameAndEmail(parsedEmail.from);
    senderName = name || '';
    senderEmail = email || '';
  } else {
    senderName = parsedEmail.from?.value[0].name || '';
    senderEmail = parsedEmail.from?.value[0].address || '';
  }

  let emailContent = '';
  if (senderName && senderEmail) {
    emailContent = `${senderName}, ${senderEmail}\n\n${parsedEmail.text}\n\nAttachments:\n`;
  }

  let fileName: string = '';
  for (const attachment of parsedEmail.attachments) {
    emailContent += `- ${attachment.filename}\n`;
    fileName = isMbox ? `${senderName}_${attachment.filename}` : attachment.filename;
    if (/\.(pdf|docx)$/i.test(attachment.filename)) {
      const buffer = Buffer.from(attachment.content);

      const zipFile = {
        buffer,
        name: fileName,
        originalname: fileName,
        mimetype: attachment.contentType,
        type: attachment.contentType,
        size: attachment.size,
      };

      let extractedContent = '';

      if (/\.pdf$/i.test(fileName)) {
        extractedContent = await extractPDFContent(zipFile);
      } else if (/\.docx$/i.test(fileName)) {
        extractedContent = await extractWordContent(zipFile);
      }

      extractedContents.push({
        fileName: sanitizeContent(fileName),
        content: sanitizeContent(extractedContent),
      });
    }
    if (/\.(zip)$/i.test(attachment.filename)) {
      const zipContents = await extractZipContent(attachment);
      extractedContents.push(...zipContents);
    }
  }

  if (!fileName && emailContent) {
    extractedContents.push({
      fileName: `${senderName}-email-body.txt`,
      content: sanitizeHtml(emailContent, {
        allowedTags: [],
        allowedAttributes: {},
      }),
    });
  }

  return extractedContents;
}
