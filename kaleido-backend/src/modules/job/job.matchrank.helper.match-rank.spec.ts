import { Queue } from 'bull';
import { Repository } from 'typeorm';

import { QUEUE_NAMES } from '@/shared/constants/queue.constants';
import { RedisConfigService } from '@/shared/services/redis-config.service';
import { CandidateStatus } from '@/shared/types';
import { getQueueToken } from '@nestjs/bull';
import { NotFoundException } from '@nestjs/common';
import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';

import { QueueService } from '../queue/queue.service';
import { Job } from './entities/job.entity';
import { JobMatchRankHelper } from './job.matchrank.helper';
import * as jobHelper2 from './job.helper-2';
import * as jobHelpers from './job.helpers';

describe('JobMatchRankHelper - Match and Rank Functions', () => {
  let helper: JobMatchRankHelper;
  let jobRepository: jest.Mocked<Repository<Job>>;
  let matchRankQueue: jest.Mocked<Queue>;
  let redisConfigService: jest.Mocked<RedisConfigService>;
  let queueService: jest.Mocked<QueueService>;

  const mockCandidate = {
    id: 'candidate-1',
    firstName: 'John',
    lastName: 'Doe',
    email: '<EMAIL>',
    status: CandidateStatus.NEW,
    evaluation: {
      matchScore: 0.85,
      criterionMatchedOn: ['skill1', 'skill2'],
    },
    videoResponses: [],
  };

  const mockJob = {
    id: 'job-1',
    jobType: 'Software Engineer',
    companyName: 'Test Company',
    department: 'Engineering',
    location: ['New York, NY'],
    skills: ['JavaScript', 'React'],
    candidates: [mockCandidate],
    topCandidateThreshold: 0.7,
    secondTierCandidateThreshold: 0.5,
    updatedAt: new Date(),
  };

  beforeEach(async () => {
    const mockJobRepository = {
      createQueryBuilder: jest.fn(),
      findOne: jest.fn(),
      save: jest.fn(),
    };

    const mockMatchRankQueue = {
      add: jest.fn(),
      getJob: jest.fn(),
    };

    const mockRedisConfigService = {
      getRedisConnection: jest.fn(),
      checkRedisConnection: jest.fn().mockResolvedValue(true),
    };

    const mockQueueService = {
      addMatchRankTask: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        JobMatchRankHelper,
        {
          provide: getRepositoryToken(Job),
          useValue: mockJobRepository,
        },
        {
          provide: getQueueToken(QUEUE_NAMES.MATCH_RANK),
          useValue: mockMatchRankQueue,
        },
        {
          provide: RedisConfigService,
          useValue: mockRedisConfigService,
        },
        {
          provide: QueueService,
          useValue: mockQueueService,
        },
      ],
    }).compile();

    helper = module.get<JobMatchRankHelper>(JobMatchRankHelper);
    jobRepository = module.get(getRepositoryToken(Job));
    matchRankQueue = module.get(getQueueToken(QUEUE_NAMES.MATCH_RANK));
    redisConfigService = module.get(RedisConfigService);
    queueService = module.get(QueueService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('getMatchedCandidates', () => {
    const mockQueryBuilder = {
      createQueryBuilder: jest.fn().mockReturnThis(),
      leftJoinAndSelect: jest.fn().mockReturnThis(),
      where: jest.fn().mockReturnThis(),
      andWhere: jest.fn().mockReturnThis(),
      orderBy: jest.fn().mockReturnThis(),
      getOne: jest.fn(),
    };

    beforeEach(() => {
      jobRepository.createQueryBuilder.mockReturnValue(mockQueryBuilder as any);
    });

    it('should get matched candidates with pagination', async () => {
      const jobWithCandidates = {
        ...mockJob,
        candidates: [
          { ...mockCandidate, evaluation: { matchScore: 0.9 } },
          { ...mockCandidate, id: 'candidate-2', evaluation: { matchScore: 0.8 } },
          { ...mockCandidate, id: 'candidate-3', evaluation: { matchScore: 0.7 } },
        ],
      };

      mockQueryBuilder.getOne.mockResolvedValue(jobWithCandidates);

      const result = await helper.getMatchedCandidates('job-1', { page: 1, limit: 2 });

      expect(jobRepository.createQueryBuilder).toHaveBeenCalledWith('job');
      expect(mockQueryBuilder.leftJoinAndSelect).toHaveBeenCalledWith(
        'job.candidates',
        'candidate',
      );
      expect(mockQueryBuilder.leftJoinAndSelect).toHaveBeenCalledWith(
        'candidate.videoResponses',
        'videoResponses',
      );
      expect(mockQueryBuilder.leftJoinAndSelect).toHaveBeenCalledWith(
        'candidate.evaluations',
        'candidateEvaluations',
      );
      expect(mockQueryBuilder.where).toHaveBeenCalledWith('job.id = :jobId', { jobId: 'job-1' });
      expect(mockQueryBuilder.andWhere).toHaveBeenCalledWith('candidate.id IS NOT NULL');

      expect(result).toEqual({
        metadata: {
          jobTitle: jobWithCandidates.jobType,
          company: jobWithCandidates.companyName,
          department: jobWithCandidates.department,
          location: jobWithCandidates.location,
          skills: jobWithCandidates.skills,
          totalCandidates: 3,
          currentPage: 1,
          totalPages: 2,
          hasMore: true,
        },
        candidates: expect.any(Array),
      });
    });

    it('should handle default pagination values', async () => {
      const jobWithCandidates = { ...mockJob };
      mockQueryBuilder.getOne.mockResolvedValue(jobWithCandidates);

      const result = await helper.getMatchedCandidates('job-1');

      expect(result.metadata.totalCandidates).toBe(1); // Mock job has 1 candidate
      expect(result.metadata.currentPage).toBe(1); // Default page
    });

    it('should order candidates by match score descending', async () => {
      const jobWithCandidates = { ...mockJob };
      mockQueryBuilder.getOne.mockResolvedValue(jobWithCandidates);

      await helper.getMatchedCandidates('job-1');

      expect(mockQueryBuilder.orderBy).toHaveBeenCalledWith(
        "ROUND(COALESCE((candidate.evaluation->>'matchScore')::float, 0) * 100)",
        'DESC',
      );
    });

    it('should throw NotFoundException when job is not found', async () => {
      mockQueryBuilder.getOne.mockResolvedValue(null);

      await expect(helper.getMatchedCandidates('non-existent-job')).rejects.toThrow(
        NotFoundException,
      );
      await expect(helper.getMatchedCandidates('non-existent-job')).rejects.toThrow(
        'Job with ID non-existent-job not found',
      );
    });
  });

  describe('getAllMatchedCandidates', () => {
    it('should get all matched candidates for a client', async () => {
      const mockResult = {
        data: [
          {
            id: 'job-1',
            jobTitle: 'Software Engineer',
            company: 'Test Company',
            department: 'Engineering',
            location: ['Remote'],
            candidates: {
              total: 5,
              evaluated: 5,
              byStatus: Object.values(CandidateStatus).reduce((acc, status) => {
                acc[status] = status === CandidateStatus.INTERVIEWING ? 2 : status === CandidateStatus.SHORTLISTED ? 3 : 0;
                return acc;
              }, {} as Record<CandidateStatus, number>),
              topTier: 2,
              secondTier: 2,
              contacted: 1,
              notContacted: 4,
              completedVideoInterviews: 1,
            },
            matchScores: {
              average: '75.0',
              distribution: {
                excellent: 1,
                veryGood: 1,
                good: 2,
                fair: 1,
                poor: 0,
              },
            },
            thresholds: {
              topTier: 80,
              secondTier: 60,
            },
          },
        ],
        pagination: {
          currentPage: 1,
          totalPages: 1,
          totalItems: 1,
          itemsPerPage: 9,
        },
      };

      // Mock the helper function that would be imported
      jest.spyOn(jobHelper2, 'getAllMatchedCandidatesHelper').mockResolvedValue(mockResult);

      const result = await helper.getAllMatchedCandidates('client-1', { page: 1, limit: 9 });

      expect(result).toEqual(mockResult);
    });
  });

  describe('getMatchRanks', () => {
    it('should get match ranks for a job', async () => {
      const mockResult = {
        topTier: { candidates: [], total: 0 },
        secondTier: { candidates: [], total: 0 },
        others: { candidates: [], total: 0 },
        pagination: {
          currentPage: 1,
          totalPages: 0,
          totalItems: 0,
          itemsPerPage: 10,
        },
      };

      // Mock the helper function that would be imported
      jest.spyOn(jobHelpers, 'getMatchRanksHelper').mockResolvedValue(mockResult);

      const result = await helper.getMatchRanks('job-1', { page: 1, limit: 10 });

      expect(result).toEqual(mockResult);
    });
  });

  describe('getMatchRankedJobs', () => {
    it('should get match ranked jobs for a user', async () => {
      const mockResult = {
        data: [
          {
            id: 'job-1',
            companyName: 'Test Company',
            jobType: 'Software Engineer',
            department: 'Engineering',
            candidates: [],
            updatedAt: new Date(),
            metadata: {
              totalCandidates: 0,
              topTierCount: 0,
              secondTierCount: 0,
            },
            status: 'No candidates',
          },
        ],
        pagination: {
          currentPage: 1,
          totalPages: 1,
          totalItems: 1,
          itemsPerPage: 10,
        },
      };

      // Mock the helper function that would be imported
      jest.spyOn(jobHelpers, 'getMatchRankedJobsHelper').mockResolvedValue(mockResult);

      const result = await helper.getMatchRankedJobs('user-1', { page: 1, limit: 10 });

      expect(result).toEqual(mockResult);
    });
  });

  describe('startMatchRankProcess', () => {
    it('should start match rank process successfully', async () => {
      const mockQueueJob = {
        id: 123,
        data: { jobId: 'job-1', clientId: 'client-1' },
      };

      jobRepository.findOne.mockResolvedValue(mockJob as any);
      queueService.addMatchRankTask.mockResolvedValue(mockQueueJob as any);

      const result = await helper.startMatchRankProcess('job-1', 'client-1');

      expect(jobRepository.findOne).toHaveBeenCalledWith({
        where: { id: 'job-1', clientId: 'client-1' },
        relations: ['candidates'],
      });
      expect(queueService.addMatchRankTask).toHaveBeenCalledWith('job-1', 'client-1', {
        topTierThreshold: 70,
        secondTierThreshold: 50,
      });

      expect(result).toEqual({
        success: true,
        message: 'Match rank process started successfully',
        jobId: 'job-1',
        queueJobId: '123',
        status: 'queued',
      });
    });

    it('should throw NotFoundException when job is not found', async () => {
      jobRepository.findOne.mockResolvedValue(null);

      await expect(helper.startMatchRankProcess('non-existent-job', 'client-1')).rejects.toThrow(
        NotFoundException,
      );
      await expect(helper.startMatchRankProcess('non-existent-job', 'client-1')).rejects.toThrow(
        'Job with ID non-existent-job not found',
      );
    });

    it('should throw error when job has no candidates', async () => {
      const jobWithoutCandidates = { ...mockJob, candidates: [] };
      jobRepository.findOne.mockResolvedValue(jobWithoutCandidates as any);

      const result = await helper.startMatchRankProcess('job-1', 'client-1');

      expect(result).toEqual({
        success: false,
        message: 'Job has no candidates to process',
        jobId: 'job-1',
        status: 'failed',
      });
    });

    it('should use default thresholds when not provided', async () => {
      const mockQueueJob = { id: 123 };
      jobRepository.findOne.mockResolvedValue(mockJob as any);
      queueService.addMatchRankTask.mockResolvedValue(mockQueueJob as any);

      await helper.startMatchRankProcess('job-1', 'client-1');

      expect(queueService.addMatchRankTask).toHaveBeenCalledWith('job-1', 'client-1', {
        topTierThreshold: 70,
        secondTierThreshold: 50,
      });
    });
  });

  describe('getMatchRankStatus', () => {
    it('should get match rank status for queue job ID', async () => {
      const mockStatus = {
        status: 'completed',
        result: {
          jobId: 'job-1',
          totalCandidates: 5,
          matchedCandidates: 5,
        },
      };

      const mockQueueJob = {
        finishedOn: Date.now(),
        returnvalue: mockStatus.result,
        failedReason: null,
        getState: jest.fn().mockResolvedValue('completed'),
        progress: jest.fn().mockReturnValue(100),
      };

      matchRankQueue.getJob.mockResolvedValue(mockQueueJob as any);

      const result = await helper.getMatchRankStatus('123');

      expect(matchRankQueue.getJob).toHaveBeenCalledWith('123');
      expect(result.status).toBe('completed');
    });

    it('should handle job entity ID format', async () => {
      const jobEntityId = 'job-uuid-format';

      // Should not call queue.getJob for non-numeric IDs
      const result = await helper.getMatchRankStatus(jobEntityId);

      expect(matchRankQueue.getJob).not.toHaveBeenCalled();
      expect(result).toEqual({
        status: 'unknown',
        message: 'Unable to determine status for this job ID format',
      });
    });

    it('should handle queue job not found', async () => {
      matchRankQueue.getJob.mockResolvedValue(null);

      const result = await helper.getMatchRankStatus('123');

      expect(result).toEqual({
        jobId: '123',
        status: 'not_found',
        progress: 0,
        message: 'Queue job not found',
      });
    });
  });
});
