import { Test, TestingModule } from '@nestjs/testing';
import { Repository } from 'typeorm';
import { getRepositoryToken } from '@nestjs/typeorm';

import { JobSearchService } from './job-search.service';
import { Job } from '../entities/job.entity';
import { Candidate } from '@modules/candidate/entities/candidate.entity';
import { JobService } from '../job.service';

describe('JobSearchService - getJobsByStatus with correct candidate counts', () => {
  let service: JobSearchService;
  let jobRepository: Repository<Job>;
  let candidateRepository: Repository<Candidate>;

  beforeEach(async () => {
    const mockJobRepository = {
      createQueryBuilder: jest.fn(),
    };

    const mockCandidateRepository = {
      createQueryBuilder: jest.fn(),
    };

    const mockJobService = {
      // Add any required methods
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        JobSearchService,
        {
          provide: getRepositoryToken(Job),
          useValue: mockJobRepository,
        },
        {
          provide: getRepositoryToken(Candidate),
          useValue: mockCandidateRepository,
        },
        {
          provide: JobService,
          useValue: mockJobService,
        },
      ],
    }).compile();

    service = module.get<JobSearchService>(JobSearchService);
    jobRepository = module.get(getRepositoryToken(Job));
    candidateRepository = module.get(getRepositoryToken(Candidate));
  });

  describe('getJobsByStatus', () => {
    it('should return correct totalCandidates count including appliedJobs', async () => {
      const mockJobs = [
        { id: 'job-1', clientId: 'client-1', status: 'ACTIVE' },
        { id: 'job-2', clientId: 'client-1', status: 'ACTIVE' },
      ];

      // Mock job query
      const mockJobQueryBuilder = {
        leftJoinAndSelect: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        andWhere: jest.fn().mockReturnThis(),
        skip: jest.fn().mockReturnThis(),
        take: jest.fn().mockReturnThis(),
        orderBy: jest.fn().mockReturnThis(),
        getManyAndCount: jest.fn().mockResolvedValue([mockJobs, 2]),
      };

      (jobRepository.createQueryBuilder as jest.Mock).mockReturnValue(mockJobQueryBuilder);

      // Mock candidate counts for each job
      const mockCandidateQueryBuilder = {
        where: jest.fn().mockReturnThis(),
        getCount: jest
          .fn()
          .mockResolvedValueOnce(5) // job-1 has 5 candidates (3 direct + 2 via appliedJobs)
          .mockResolvedValueOnce(3), // job-2 has 3 candidates (1 direct + 2 via appliedJobs)
      };

      (candidateRepository.createQueryBuilder as jest.Mock).mockReturnValue(
        mockCandidateQueryBuilder,
      );

      const result = await service.getJobsByStatus('client-1', {
        page: 1,
        limit: 10,
        status: 'ACTIVE',
        includeStats: false,
      });

      // Verify job query
      expect(mockJobQueryBuilder.where).toHaveBeenCalledWith('job.clientId = :clientId', {
        clientId: 'client-1',
      });
      expect(mockJobQueryBuilder.andWhere).toHaveBeenCalledWith('job.status = :status', {
        status: 'ACTIVE',
      });

      // Verify candidate count queries use the correct WHERE clause
      expect(mockCandidateQueryBuilder.where).toHaveBeenCalledWith(
        '(candidate.jobId = :jobId OR :jobId = ANY(candidate."appliedJobs"))',
        { jobId: 'job-1' },
      );
      expect(mockCandidateQueryBuilder.where).toHaveBeenCalledWith(
        '(candidate.jobId = :jobId OR :jobId = ANY(candidate."appliedJobs"))',
        { jobId: 'job-2' },
      );

      // Verify results
      expect(result.data).toHaveLength(2);
      expect(result.data[0].totalCandidates).toBe(5);
      expect(result.data[1].totalCandidates).toBe(3);
    });

    it('should count both ranked and unranked candidates', async () => {
      const mockJobs = [{ id: 'job-1', clientId: 'client-1', status: 'ACTIVE' }];

      const mockJobQueryBuilder = {
        leftJoinAndSelect: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        andWhere: jest.fn().mockReturnThis(),
        skip: jest.fn().mockReturnThis(),
        take: jest.fn().mockReturnThis(),
        orderBy: jest.fn().mockReturnThis(),
        getManyAndCount: jest.fn().mockResolvedValue([mockJobs, 1]),
      };

      (jobRepository.createQueryBuilder as jest.Mock).mockReturnValue(mockJobQueryBuilder);

      // Mock candidate count - this should count ALL candidates regardless of evaluation status
      const mockCandidateQueryBuilder = {
        where: jest.fn().mockReturnThis(),
        getCount: jest.fn().mockResolvedValue(10), // Total: 6 ranked + 4 unranked
      };

      (candidateRepository.createQueryBuilder as jest.Mock).mockReturnValue(
        mockCandidateQueryBuilder,
      );

      const result = await service.getJobsByStatus('client-1', {
        page: 1,
        limit: 10,
        includeStats: false,
      });

      // The query should NOT filter by evaluation status
      expect(mockCandidateQueryBuilder.where).toHaveBeenCalledTimes(1);
      expect(mockCandidateQueryBuilder.where).toHaveBeenCalledWith(
        '(candidate.jobId = :jobId OR :jobId = ANY(candidate."appliedJobs"))',
        { jobId: 'job-1' },
      );

      // Verify total includes both ranked and unranked
      expect(result.data[0].totalCandidates).toBe(10);
    });

    it('should handle pagination correctly', async () => {
      const mockJobs = Array.from({ length: 5 }, (_, i) => ({
        id: `job-${i + 1}`,
        clientId: 'client-1',
        status: 'ACTIVE',
      }));

      const mockJobQueryBuilder = {
        leftJoinAndSelect: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        skip: jest.fn().mockReturnThis(),
        take: jest.fn().mockReturnThis(),
        orderBy: jest.fn().mockReturnThis(),
        getManyAndCount: jest.fn().mockResolvedValue([mockJobs.slice(0, 2), 5]), // Page 1, 2 items
      };

      (jobRepository.createQueryBuilder as jest.Mock).mockReturnValue(mockJobQueryBuilder);

      const mockCandidateQueryBuilder = {
        where: jest.fn().mockReturnThis(),
        getCount: jest.fn().mockResolvedValue(3),
      };

      (candidateRepository.createQueryBuilder as jest.Mock).mockReturnValue(
        mockCandidateQueryBuilder,
      );

      const result = await service.getJobsByStatus('client-1', {
        page: 1,
        limit: 2,
      });

      expect(mockJobQueryBuilder.skip).toHaveBeenCalledWith(0); // (1-1) * 2
      expect(mockJobQueryBuilder.take).toHaveBeenCalledWith(2);

      expect(result.pagination).toEqual({
        currentPage: 1,
        totalPages: 3, // Math.ceil(5 / 2)
        totalItems: 5,
        itemsPerPage: 2,
      });
    });
  });
});
