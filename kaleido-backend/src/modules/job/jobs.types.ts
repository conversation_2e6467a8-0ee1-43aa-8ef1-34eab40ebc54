import { Candidate } from '@modules/candidate/entities/candidate.entity';

import { Job } from './entities/job.entity';

export interface PaginationOptions {
  page: number;
  limit: number;
  status?: string;
  relations?: string[];
}

export interface PublicJobFilters {
  experienceLevel?: string;
  location?: string;
  jobType?: string;
  department?: string;
  industry?: string;
  companySize?: string;
  layoutPreference?: string;
}

export interface MatchRankResponse {
  topTier: {
    candidates: Candidate[];
    total: number;
  };
  secondTier: {
    candidates: Candidate[];
    total: number;
  };
  others: {
    candidates: Candidate[];
    total: number;
  };
  unranked?: {
    candidates: Candidate[];
    total: number;
  };
  shortlisted?: {
    candidates: Candidate[];
    total: number;
  };
  pagination: {
    currentPage: number;
    totalPages: number;
    totalItems: number;
    itemsPerPage: number;
  };
  stats?: {
    totalCandidates: number;
    evaluatedCandidates: number;
    pendingEvaluation: number;
    averageMatchScore: number;
    matchingStats: {
      topTierCount: number;
      secondTierCount: number;
      belowThresholdCount: number;
      topTierThreshold: number;
      secondTierThreshold: number;
    };
    engagementStats: {
      contacted: number;
      notContacted: number;
      completedVideoInterviews: number;
      videoInterviewCompletionRate: number;
    };
  };
}

export interface JobBasicInfo {
  id: string;
  companyName: string;
  jobType: string;
  department: string;
  candidates: Candidate[];
  updatedAt: Date;
}

export interface JobWithMetadata extends JobBasicInfo {
  metadata: {
    totalCandidates: number;
    topTierCount: number;
    secondTierCount: number;
  };
  status: string;
}

export interface RankedJobsStats {
  totalJobs: number;
  totalCandidates: number;
  averageMatchScore: number;
  jobsWithCandidates: number;
  topTierCandidates: number;
  secondTierCandidates: number;
  jobsByDepartment: { [department: string]: number };
  candidatesByMatchScore: {
    excellent: number; // 90-100
    veryGood: number; // 80-89
    good: number; // 70-79
    fair: number; // 50-69
    poor: number; // 0-49
  };
  recentActivity: {
    newJobsLast7Days: number;
    newJobsLast30Days: number;
    newCandidatesLast7Days: number;
    newCandidatesLast30Days: number;
  };
}

export interface MatchRankResponseWithMetadata {
  data: JobWithMetadata[];
  pagination: {
    currentPage: number;
    totalPages: number;
    totalItems: number;
    itemsPerPage: number;
  };
}

export type JobMatchRankResponse = MatchRankResponse;

export interface JobsByStatusResponse {
  data: Job[];
  pagination: {
    currentPage: number;
    totalPages: number;
    totalItems: number;
    itemsPerPage: number;
  };
}

export interface JobsResponse {
  data: Job[];
  metadata: {
    total: number;
    filtered: number;
    preferences?: {
      jobTypes: string[];
      departments: string[];
      locations: string[];
      industries?: string[];
      companySizes?: string[];
      remotePreference?: string;
    };
  };
  pagination?: {
    currentPage: number;
    totalPages: number;
    totalItems: number;
    itemsPerPage: number;
  };
}
