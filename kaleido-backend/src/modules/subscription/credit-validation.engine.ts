import { Repository } from 'typeorm';

import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';

import {
  CREDIT_COSTS,
  CreditActionType,
  getVideoJDCreditCost,
} from '../../shared/enums/subscription-limit-type.enum';
import { getCandidateJobWhereClause } from '@shared/utils/candidate-job-query.util';
import { Company } from '../company/entities/company.entity';
import { Job } from '../job/entities/job.entity';
import { CreditService } from './credit.service';

export interface CreditValidationResult {
  isValid: boolean;
  message: string;
  actionType: CreditActionType;
  requiredCredits: number;
  availableCredits: number;
}

/**
 * Engine for validating credit-based subscription actions
 */
@Injectable()
export class CreditValidationEngine {
  private readonly logger = new Logger(CreditValidationEngine.name);

  constructor(
    private readonly creditService: CreditService,
    @InjectRepository(Job)
    private readonly jobRepository: Repository<Job>,
    @InjectRepository(Company)
    private readonly companyRepository: Repository<Company>,
  ) {}

  // Resume uploads are now free - no validation needed
  async canUploadResume(companyId: string, count = 1): Promise<CreditValidationResult> {
    // Always return valid since resume uploads are free
    return {
      isValid: true,
      actionType: CreditActionType.RESUME_UPLOAD,
      requiredCredits: 0,
      availableCredits: 0, // Not relevant for free actions
      message: 'Resume uploads are free',
    };
  }

  async canScout(companyId: string, count = 1): Promise<CreditValidationResult> {
    return this.validateCredits(companyId, CreditActionType.SCOUT, count);
  }

  async canMatchRank(companyId: string, count = 1): Promise<CreditValidationResult> {
    return this.validateCredits(companyId, CreditActionType.MATCH_RANK, count);
  }

  /**
   * Validate match & rank for a specific job by calculating actual unevaluated candidates
   * This is the authoritative method that should be used instead of frontend calculations
   */
  async canMatchRankForJob(
    companyId: string,
    jobId: string,
  ): Promise<CreditValidationResult & { unevaluatedCandidatesCount: number }> {
    try {
      // First verify the job exists (lightweight query)
      const job = await this.jobRepository.findOne({
        where: { id: jobId, clientId: companyId },
        select: ['id', 'clientId'], // Only select what we need
      });

      if (!job) {
        return {
          isValid: false,
          message: `Job with ID ${jobId} not found`,
          actionType: CreditActionType.MATCH_RANK,
          requiredCredits: 0,
          availableCredits: 0,
          unevaluatedCandidatesCount: 0,
        };
      }

      // Get total candidates count efficiently using utility function
      const totalCandidatesCount = await this.jobRepository.manager
        .createQueryBuilder()
        .select('COUNT(*)', 'count')
        .from('candidates', 'candidate')
        .where(getCandidateJobWhereClause(), { jobId })
        .getRawOne()
        .then((result) => parseInt(result.count, 10));

      // Get evaluated candidates count efficiently using utility function
      const evaluatedCandidatesCount = await this.jobRepository.manager
        .createQueryBuilder()
        .select('COUNT(DISTINCT candidate.id)', 'count')
        .from('candidates', 'candidate')
        .innerJoin(
          'candidate_evaluations',
          'evaluation',
          'evaluation.candidateId = candidate.id AND evaluation.jobId = :jobId',
        )
        .where(getCandidateJobWhereClause(), { jobId })
        .getRawOne()
        .then((result) => parseInt(result.count, 10));

      // Calculate unevaluated candidates efficiently
      const unevaluatedCount = totalCandidatesCount - evaluatedCandidatesCount;

      if (unevaluatedCount === 0) {
        return {
          isValid: true,
          message: 'All candidates have already been evaluated for this job',
          actionType: CreditActionType.MATCH_RANK,
          requiredCredits: 0,
          availableCredits: 0,
          unevaluatedCandidatesCount: 0,
        };
      }

      // Validate credits for the actual number of unevaluated candidates
      const validationResult = await this.validateCredits(
        companyId,
        CreditActionType.MATCH_RANK,
        unevaluatedCount,
      );

      return {
        ...validationResult,
        unevaluatedCandidatesCount: unevaluatedCount,
      };
    } catch (error: any) {
      this.logger.error(`Error validating match rank for job ${jobId}:`, error);
      return {
        isValid: false,
        message: `Error validating match rank: ${error.message}`,
        actionType: CreditActionType.MATCH_RANK,
        requiredCredits: 0,
        availableCredits: 0,
        unevaluatedCandidatesCount: 0,
      };
    }
  }

  async canCreateVideoJD(
    companyId: string,
    count = 1,
    scriptLength = 'medium',
  ): Promise<CreditValidationResult> {
    return this.validateVideoJDCredits(companyId, count, scriptLength);
  }

  // Job description generation is FREE - no validation needed

  /**
   * Private method to validate credits for video JD with variable costs based on script length
   * Also validates monthly video JD limits to protect against Synthesia cost abuse
   */
  private async validateVideoJDCredits(
    companyId: string,
    count = 1,
    scriptLength = 'medium',
  ): Promise<CreditValidationResult> {
    const credits = await this.creditService.getCompanyCredits(companyId);

    // Check monthly video JD limit first (Option 2 implementation)
    const currentMonthlyUsage = credits.videoJdUsedThisMonth || 0;
    const monthlyLimit = credits.videoJdMonthlyLimit || 0;

    if (currentMonthlyUsage + count > monthlyLimit) {
      return {
        isValid: false,
        message: `Monthly video JD limit exceeded. Used: ${currentMonthlyUsage}/${monthlyLimit}, Requested: ${count}`,
        actionType: CreditActionType.VIDEO_JD,
        requiredCredits: 0,
        availableCredits: credits.remainingCredits,
      };
    }

    // Import the function dynamically to avoid circular dependency issues
    const { getVideoJDCreditCost } = await import(
      '../../shared/enums/subscription-limit-type.enum'
    );
    const requiredCredits = getVideoJDCreditCost(scriptLength) * count;
    const availableCredits = credits.remainingCredits;

    // Check if the operation would exceed available credits
    if (availableCredits < requiredCredits) {
      return {
        isValid: false,
        message: `Insufficient credits for ${this.formatVideoJDActionType(
          scriptLength,
        )}. Required: ${requiredCredits}, Available: ${availableCredits}`,
        actionType: CreditActionType.VIDEO_JD,
        requiredCredits,
        availableCredits,
      };
    }

    return {
      isValid: true,
      message: `${this.formatVideoJDActionType(scriptLength)} operation is allowed`,
      actionType: CreditActionType.VIDEO_JD,
      requiredCredits,
      availableCredits,
    };
  }

  /**
   * Private method to validate credits for a specific action
   */
  private async validateCredits(
    companyId: string,
    actionType: CreditActionType,
    count = 1,
  ): Promise<CreditValidationResult> {
    const credits = await this.creditService.getCompanyCredits(companyId);
    const requiredCredits = CREDIT_COSTS[actionType] * count;
    const availableCredits = credits.remainingCredits;

    // Check if the operation would exceed available credits
    if (availableCredits < requiredCredits) {
      return {
        isValid: false,
        message: `Insufficient credits for ${this.formatActionType(
          actionType,
          count,
        )}. Required: ${requiredCredits}, Available: ${availableCredits}`,
        actionType,
        requiredCredits,
        availableCredits,
      };
    }

    return {
      isValid: true,
      message: `${this.formatActionType(actionType, count)} operation is allowed`,
      actionType,
      requiredCredits,
      availableCredits,
    };
  }

  /**
   * Format video JD action type for user-friendly messages with variable costs
   */
  private formatVideoJDActionType(scriptLength: string): string {
    const credits = getVideoJDCreditCost(scriptLength);
    return `Video job description - ${scriptLength} (${credits} credits per video)`;
  }

  /**
   * Format action type for user-friendly messages
   */
  private formatActionType(actionType: CreditActionType, count: number = 1): string {
    switch (actionType) {
      case CreditActionType.RESUME_UPLOAD:
        return count === 1
          ? 'Resume upload (1 credit per resume)'
          : `Resume upload for ${count} resumes (1 credit per resume)`;
      case CreditActionType.SCOUT:
        return count === 1
          ? 'LinkedIn scouting (1 credit per candidate)'
          : `LinkedIn scouting for ${count} candidates (1 credit per candidate)`;
      case CreditActionType.MATCH_RANK:
        return count === 1
          ? 'Match & rank (1 credit per resume)'
          : `Match & rank for ${count} resumes (1 credit per resume)`;
      case CreditActionType.VIDEO_JD:
        return 'Video job description (variable credits per video)';
      case CreditActionType.CULTURE_FIT_QUESTIONS:
        return count === 1
          ? 'Culture fit questions (1 credit per question set)'
          : `Culture fit questions for ${count} question sets (1 credit per question set)`;
      default:
        return actionType;
    }
  }
}
