import { <PERSON>umn, DeleteDateColumn, <PERSON>tity, Index, Join<PERSON><PERSON>umn, ManyToOne, OneToMany } from 'typeorm';

import { Approval, Job, Offer, VideoResponse } from '@modules/entities';
import { Graduate } from '@modules/graduate/entities/graduate.entity';
import { JobSeeker } from '@modules/job-seeker/entities/job-seeker.entity';
import { Referral } from '@modules/referral/entities/referral.entity';
import { ActivityHistory } from '@shared/entities/activity-history.entity';
import { BaseEntity } from '@shared/entities/base.entity';
import { CandidateExperience, CandidateStatus, ContactMethod } from '@shared/types';
import { CandidateEvaluation as CandidateEvaluationData } from '@shared/types/candidate.types';

import { CandidateEvaluation } from './candidate-evaluation.entity';

/**
 * EmailCorrespondence represents an email sent to or received from a candidate
 */
export interface EmailCorrespondence {
  id: string;
  type: 'SENT' | 'RECEIVED';
  subject: string;
  content: string;
  from: string;
  to: string;
  timestamp: Date;
  emailType: 'interview' | 'offer' | 'status' | 'general';
  metadata?: {
    jobId?: string;
    jobTitle?: string;
    companyName?: string;
    interviewDate?: string;
    meetingLink?: string;
    offerDetails?: any;
    [key: string]: any;
  };
  isRead?: boolean;
  requiresAction?: boolean;
  actionType?: 'ACCEPT_INTERVIEW' | 'DECLINE_INTERVIEW' | 'ACCEPT_OFFER' | 'DECLINE_OFFER';
  actionCompleted?: boolean;
  actionCompletedAt?: Date;
}

/**
 * CandidateTier represents the classification of candidates based on their match score
 * relative to the job's requirements and thresholds.
 *
 * TOP - Candidates who meet or exceed the top tier threshold
 * SECOND - Candidates who meet the second tier threshold but not the top tier
 * OTHER - Candidates who don't meet either threshold
 */
export enum CandidateTier {
  TOP = 'TOP',
  SECOND = 'SECOND',
  OTHER = 'OTHER',
}

/**
 * InterviewType represents the different types of interviews in the hiring process
 */
export enum InterviewType {
  PHONE_SCREEN = 'PHONE_SCREEN',
  TECHNICAL = 'TECHNICAL',
  BEHAVIORAL = 'BEHAVIORAL',
  CULTURAL_FIT = 'CULTURAL_FIT',
  FINAL = 'FINAL',
  PANEL = 'PANEL',
  ASSIGNMENT = 'ASSIGNMENT',
}

/**
 * DocumentType represents the different types of documents associated with a candidate
 */
export enum CandidateDocumentType {
  RESUME = 'RESUME',
  COVER_LETTER = 'COVER_LETTER',
  PORTFOLIO = 'PORTFOLIO',
  CERTIFICATE = 'CERTIFICATE',
  REFERENCE_LETTER = 'REFERENCE_LETTER',
  IDENTITY = 'IDENTITY',
  ASSESSMENT = 'ASSESSMENT',
  OTHER = 'OTHER',
}

/**
 * Interview represents a scheduled or completed interview with a candidate
 */
export interface Interview {
  id: string;
  type: InterviewType;
  scheduledDate: Date;
  location?: string;
  meetingLink?: string;
  interviewers: string[]; // User IDs or names of interviewers
  completed: boolean;
  feedback?: {
    strengths: string[];
    weaknesses: string[];
    rating: number;
    notes: string;
    recommendation: 'HIRE' | 'REJECT' | 'CONTINUE';
  };
  createdAt: Date;
  updatedAt: Date;
}

/**
 * StatusHistory tracks changes to a candidate's status over time
 */
export interface StatusHistory {
  previousStatus?: CandidateStatus; // Optional for the initial status
  newStatus: CandidateStatus;
  changedAt: Date;
  changedBy?: string; // User ID or name who changed the status
  reason?: string;
}

/**
 * CandidateDocument represents a document uploaded for a candidate
 */
export interface CandidateDocument {
  id: string;
  type: CandidateDocumentType;
  name: string;
  url: string;
  fileSize?: number;
  uploadedAt: Date;
  uploadedBy?: string; // User ID or name who uploaded the document
}

/**
 * CommunicationLog represents a record of communication with a candidate
 */
export interface CommunicationLog {
  id: string;
  type: 'EMAIL' | 'CALL' | 'TEXT' | 'IN_PERSON' | 'OTHER';
  date: Date;
  initiatedBy: string; // User ID or name who initiated the communication
  summary: string;
  details?: string;
}

/**
 * Task represents a follow-up task related to a candidate
 */
export interface Task {
  id: string;
  title: string;
  description?: string;
  dueDate: Date;
  assignedTo: string; // User ID or name the task is assigned to
  completed: boolean;
  completedAt?: Date;
  priority: 'LOW' | 'MEDIUM' | 'HIGH';
}

@Entity('candidates')
@Index('idx_candidates_job_id', ['jobId'])
@Index('idx_candidates_job_created_at', ['jobId', 'createdAt'])
@Index('idx_candidates_job_status_tier', ['jobId', 'status', 'tier'])
@Index('idx_candidates_email', ['email'])
@Index('idx_candidates_status', ['status'])
@Index('idx_candidates_tier', ['tier'])
@Index('idx_candidates_source', ['source'])
@Index('idx_candidates_job_seeker_id', ['jobSeekerId'])
@Index('idx_candidates_graduate_id', ['graduateId'])
@Index('idx_candidates_external_id', ['externalId'])
@Index('idx_candidates_contacted', ['contacted'])
@Index('idx_candidates_years_experience', ['yearsOfExperience'])
@Index('idx_candidates_fullname', ['fullName'])
@Index('idx_candidates_clientid', ['clientId'])
@Index('idx_candidates_userid', ['userId'])
export class Candidate extends BaseEntity {
  @Column({ nullable: true })
  userId?: string;

  @Column({ type: 'uuid', nullable: true })
  jobId?: string;

  @OneToMany(() => CandidateEvaluation, (evaluation) => evaluation.candidate, {
    cascade: true,
  })
  evaluations?: CandidateEvaluation[];

  @Column({ type: 'varchar', nullable: true })
  externalId!: string | null;

  @Column({ type: 'varchar' })
  fullName!: string;

  @Column({ type: 'varchar' })
  firstName!: string;

  @Column({ type: 'varchar' })
  lastName!: string;

  @Column({ type: 'varchar', nullable: true })
  jobTitle!: string;

  @Column({ type: 'varchar', nullable: true })
  location!: string;

  @Column({ type: 'varchar', nullable: true })
  myProfileImage?: string;

  @Column({ type: 'text', nullable: true })
  summary!: string | null;

  @Column('text', { array: true, nullable: true, default: () => "'{}'::text[]" })
  skills!: string[];

  @Column('jsonb', { nullable: true })
  experience!: CandidateExperience[] | null;

  @Column({ type: 'varchar', nullable: true })
  profileUrl!: string;

  @Column({ type: 'varchar', nullable: true })
  originalFilename?: string;

  @Column({ type: 'varchar', nullable: true })
  linkedinUrl!: string | null;

  @Column({ type: 'varchar', nullable: true })
  githubUrl!: string | null;

  @Column({ type: 'varchar', nullable: true })
  sourceType!: string;

  @Column({
    type: 'enum',
    enum: CandidateStatus,
    default: CandidateStatus.NEW,
  })
  status!: CandidateStatus;

  @Column({
    type: 'enum',
    enum: CandidateTier,
    nullable: true,
  })
  tier?: CandidateTier;

  @Column({ type: 'boolean', default: false })
  contacted!: boolean;

  @Column({
    type: 'enum',
    enum: ContactMethod,
    nullable: true,
  })
  contactMethod?: ContactMethod;

  @Column({ type: 'text', nullable: true })
  notes?: string;

  @Column({ type: 'boolean', default: false })
  hasCompletedVideoInterview!: boolean;

  @Column('jsonb', { nullable: true })
  evaluation!: CandidateEvaluationData | null;

  // Interview Management
  @Column('jsonb', { array: true, nullable: true })
  interviews?: Interview[];

  // Status Change History
  @Column('jsonb', { array: true, nullable: true })
  statusHistory?: StatusHistory[];

  // Document Management
  @Column('jsonb', { array: true, nullable: true })
  documents?: CandidateDocument[];

  // Communication Log
  @Column('jsonb', { array: true, nullable: true })
  communicationLogs?: CommunicationLog[];

  // Tasks and Reminders
  @Column('jsonb', { array: true, nullable: true })
  tasks?: Task[];

  // Team Collaboration
  @Column({ type: 'uuid', nullable: true })
  assignedRecruiter?: string;

  @Column({ type: 'uuid', nullable: true })
  assignedHiringManager?: string;

  // Referral Tracking
  @Column({ type: 'varchar', nullable: true })
  referralSource?: string;

  @Column({ type: 'uuid', nullable: true })
  referredBy?: string;

  @Column({ type: 'boolean', default: false })
  referralBonusPaid?: boolean;

  // New Referral System Fields
  @Column({ type: 'varchar', nullable: true })
  referralCode?: string;

  @Column({ type: 'uuid', nullable: true })
  referralId?: string;

  @ManyToOne(() => Referral, { nullable: true })
  @JoinColumn({ name: 'referralId' })
  referral?: Referral;

  // Onboarding Tracking
  @Column({ type: 'jsonb', nullable: true })
  onboardingStatus?: {
    stage: 'NOT_STARTED' | 'IN_PROGRESS' | 'COMPLETED';
    completedSteps: string[];
    pendingSteps: string[];
    startDate?: Date;
    completedDate?: Date;
  };

  @Column('uuid', { array: true, nullable: true })
  appliedJobs?: string[];

  @ManyToOne(() => Job, (job) => job.candidates, {
    lazy: true,
    onDelete: 'CASCADE',
  })
  @JoinColumn({ name: 'jobId' })
  job?: Promise<Job>;

  @OneToMany(() => VideoResponse, (videoResponse) => videoResponse.candidate)
  videoResponses?: VideoResponse[];

  @OneToMany(() => Approval, (approval) => approval.candidate)
  approvals?: Approval[];

  @OneToMany(() => Offer, (offer) => offer.candidate)
  offers?: Offer[];

  @Column({ type: 'varchar', nullable: true })
  email?: string;

  @Column({ type: 'varchar', nullable: true })
  phone?: string;

  @Column({ type: 'timestamp', nullable: true })
  lastContactDate?: Date;

  @Column({ type: 'varchar', nullable: true })
  currentCompany?: string;

  @Column({ type: 'int', nullable: true })
  yearsOfExperience?: number | null;

  @Column({ type: 'varchar', nullable: true })
  preferredLocation?: string;

  @Column({ type: 'boolean', default: false })
  isRemoteOnly?: boolean;

  @Column({ type: 'jsonb', nullable: true })
  salary?: {
    min?: number;
    max?: number;
    currency?: string;
  };

  @Column({ type: 'timestamp', nullable: true })
  availableFrom?: Date;

  @Column({ type: 'uuid', nullable: true })
  jobSeekerId?: string;

  @ManyToOne(() => JobSeeker, (jobSeeker) => jobSeeker.candidates)
  @JoinColumn({ name: 'jobSeekerId' })
  jobSeeker?: JobSeeker;

  @Column({
    type: 'varchar',
    default: 'RESUME_UPLOAD',
    comment: 'Source of the candidate (RESUME_UPLOAD, JOB_SEEKER, etc)',
  })
  source!: string;

  @Column({ type: 'uuid', nullable: true })
  graduateId?: string;

  @ManyToOne(() => Graduate, (graduate) => graduate.candidates)
  @JoinColumn({ name: 'graduateId' })
  graduate?: Graduate;

  @Column('jsonb', { nullable: true })
  activityHistory?: ActivityHistory[];

  @Column('jsonb', { nullable: true, default: [] })
  emailCorrespondence?: EmailCorrespondence[];

  @Column({ type: 'jsonb', nullable: true })
  extractionMetadata?: Array<{
    filename: string;
    extractedAt: Date;
    extractionSource: string;
    rawExtractedData?: any;
    matchScore?: number;
  }>;

  @Column({ type: 'jsonb', nullable: true })
  clientSpecificData?: {
    deletedByClients?: { [clientId: string]: Date };
    [clientId: string]: string | Date | { [clientId: string]: Date } | any;
  };

  /**
   * Get the most recent job title from experience or fallback to jobTitle field
   */
  getDisplayTitle(): string {
    // First try to get the most recent experience title
    if (this.experience && Array.isArray(this.experience) && this.experience.length > 0) {
      // Sort by end date (most recent first), with current jobs (no end date) at the top
      const sortedExperience = [...this.experience].sort((a, b) => {
        // Current jobs (no end date) come first
        if (!a.endDate && b.endDate) return -1;
        if (a.endDate && !b.endDate) return 1;
        if (!a.endDate && !b.endDate) {
          // Both are current, sort by start date (most recent first)
          if (a.startDate && b.startDate) {
            return new Date(b.startDate).getTime() - new Date(a.startDate).getTime();
          }
          return 0;
        }

        // Both have end dates, sort by end date (most recent first)
        if (a.endDate && b.endDate) {
          return new Date(b.endDate).getTime() - new Date(a.endDate).getTime();
        }

        return 0;
      });

      const latestExperience = sortedExperience[0];
      if (latestExperience?.title) {
        return latestExperience.title;
      }
    }

    // Fallback to jobTitle field
    if (this.jobTitle) {
      return this.jobTitle;
    }

    // Final fallback
    return 'Not Provided';
  }

  /**
   * Calculate years of experience from experience array or return existing value
   */
  getYearsOfExperience(): number {
    // Return existing value if available
    if (this.yearsOfExperience !== null && this.yearsOfExperience !== undefined) {
      return this.yearsOfExperience;
    }

    // Calculate from experience array if available
    if (this.experience && Array.isArray(this.experience) && this.experience.length > 0) {
      let totalMonths = 0;
      const currentDate = new Date();

      for (const exp of this.experience) {
        if (!exp.startDate) continue;

        const startDate = new Date(exp.startDate);
        const endDate = exp.endDate ? new Date(exp.endDate) : currentDate;

        if (isNaN(startDate.getTime()) || isNaN(endDate.getTime())) continue;

        const months =
          (endDate.getFullYear() - startDate.getFullYear()) * 12 +
          (endDate.getMonth() - startDate.getMonth());

        if (months > 0) {
          totalMonths += months;
        }
      }

      // Convert months to years (rounded to nearest integer)
      return Math.round(totalMonths / 12);
    }

    // Default fallback
    return 0;
  }

  // Candidate Management Fields
  @Column({ type: 'boolean', default: false })
  isFavorited?: boolean;

  @Column({ type: 'boolean', default: false })
  isShortlisted?: boolean;

  @Column({ type: 'jsonb', nullable: true, default: {} })
  scrapebookNotes?: {
    [pageId: string]: Array<{
      id: string;
      content: string;
      createdAt: Date;
      updatedAt: Date;
      createdBy: string; // userId who created the note
      tags?: string[];
      isPrivate?: boolean;
      // Sticky note specific fields
      color?: string; // For sticky note color
      position?: { x: number; y: number }; // For sticky note position
      type: 'text' | 'sticky'; // To differentiate between regular notes and sticky notes
    }>;
  };

  @DeleteDateColumn({ type: 'timestamp', nullable: true })
  deletedAt?: Date;

  /**
   * Gets the latest evaluation for this candidate and the specified job
   * Only returns job-specific evaluations, never falls back to legacy evaluation data from other jobs
   */
  getEvaluationForJob(jobId?: string): CandidateEvaluationData | null {
    // If no evaluations exist, return null (no evaluation for this job)
    if (!this.evaluations || this.evaluations.length === 0) {
      return null;
    }

    // If no jobId is specified, use the candidate's primary jobId
    const targetJobId = jobId || this.jobId;
    if (!targetJobId) {
      return null;
    }

    // Find the evaluation for the specified job
    const matchingEvaluation = this.evaluations.find((evalItem) => evalItem.jobId === targetJobId);
    if (matchingEvaluation && matchingEvaluation.evaluation) {
      return matchingEvaluation.evaluation;
    }

    // Return null if no job-specific evaluation exists
    // DO NOT fallback to legacy evaluation field as it contains data from other jobs
    return null;
  }

  /**
   * Checks if a video intro email has been sent for a specific job
   * @param jobId - The job ID to check for (optional, uses candidate's primary jobId if not provided)
   * @returns boolean indicating if video intro email was sent
   */
  getVideoIntroEmailSent(jobId?: string): boolean {
    const targetJobId = jobId || this.jobId;

    // Check activity history for video intro email sent
    if (this.activityHistory && this.activityHistory.length > 0) {
      return this.activityHistory.some(
        (activity) =>
          activity.type === 'EMAIL_SENT' &&
          activity.metadata?.emailType === 'video_intro_shortlist' &&
          activity.metadata?.jobId === targetJobId,
      );
    }

    // Fallback: check emailCorrespondence for video intro emails
    if (this.emailCorrespondence && this.emailCorrespondence.length > 0) {
      return this.emailCorrespondence.some(
        (email) =>
          email.type === 'SENT' &&
          email.metadata?.emailType === 'video_intro_shortlist' &&
          email.metadata?.jobId === targetJobId,
      );
    }

    return false;
  }
}
