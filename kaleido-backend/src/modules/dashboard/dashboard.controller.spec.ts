import { Test, TestingModule } from '@nestjs/testing';
import { ConfigService } from '@nestjs/config';
import { UserRole } from '@shared/types';
import { Auth0Guard } from '../../auth/auth.guard';

import { DashboardController } from './dashboard.controller';
import { DashboardService } from './dashboard.service';

describe('DashboardController', () => {
  let controller: DashboardController;
  let dashboardService: jest.Mocked<DashboardService>;

  const mockUser = {
    userId: 'test-user-123',
    email: '<EMAIL>',
    roles: ['employer'],
  };

  const mockDashboardStats = {
    userRole: UserRole.EMPLOYER,
    jobs: {
      total: 5,
      active: 3,
      draft: 2,
      byStatus: { ACTIVE: 3, DRAFT: 2 },
      recentJobs: [],
    },
    candidates: {
      total: 10,
      matched: 8,
      culturalFitAnswered: 5,
      recentMatches: [],
      byStatus: { MATCHED: 8, APPLIED: 2 },
    },
    notifications: {
      total: 3,
      unread: 1,
      today: 2,
      recent: [],
    },
  };

  const mockRegistrationStats = {
    employers: { total: 50, thisWeek: 2, thisMonth: 8 },
    jobSeekers: { total: 200, thisWeek: 10, thisMonth: 40 },
    graduates: { total: 30, thisWeek: 1, thisMonth: 5 },
    totalRegistrations: 280,
    pendingApprovals: 5,
    registrationsByPeriod: [],
    overallTrends: [],
  };

  const mockUsageStats = {
    totalCompanies: 50,
    totalJobs: 100,
    totalVideoJDs: 25,
    totalCandidates: 200,
    activeCompanies: 40,
    industryDistribution: [
      { name: 'Technology', value: 20 },
      { name: 'Finance', value: 15 },
    ],
  };

  const mockHiredStatus = {
    isHired: true,
    jobInfo: {
      jobType: 'Software Engineer',
      companyName: 'Tech Corp',
      department: 'Engineering',
    },
  };

  beforeEach(async () => {
    const mockDashboardService = {
      getDashboardStats: jest.fn(),
      getEnhancedDashboardStats: jest.fn(),
      getRegistrationStats: jest.fn(),
      getUsageStats: jest.fn(),
      getHiredStatus: jest.fn(),
    };
    const mockConfigService = {
      get: jest.fn((key: string) => {
        switch (key) {
          case 'NODE_ENV':
            return 'test';
          case 'AUTH_OFFLINE_DEV':
            return 'false';
          default:
            return undefined;
        }
      }),
    };

    const module: TestingModule = await Test.createTestingModule({
      controllers: [DashboardController],
      providers: [
        {
          provide: DashboardService,
          useValue: mockDashboardService,
        },
        {
          provide: ConfigService,
          useValue: mockConfigService,
        },
      ],
    })
      .overrideGuard(Auth0Guard)
      .useValue({
        canActivate: jest.fn(() => true),
      })
      .compile();

    controller = module.get<DashboardController>(DashboardController);
    dashboardService = module.get(DashboardService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  describe('getDashboardStats', () => {
    it('should return dashboard stats', async () => {
      dashboardService.getDashboardStats.mockResolvedValue(mockDashboardStats as any);

      const result = await controller.getDashboardStats(mockUser as any);

      expect(result).toEqual(mockDashboardStats);
      expect(dashboardService.getDashboardStats).toHaveBeenCalledWith('test-user-123');
    });

    it('should handle service errors', async () => {
      dashboardService.getDashboardStats.mockRejectedValue(new Error('Service error'));

      await expect(controller.getDashboardStats(mockUser as any)).rejects.toThrow('Service error');
    });
  });

  describe('getEnhancedDashboardStats', () => {
    it('should return enhanced dashboard stats', async () => {
      const mockEnhancedStats = {
        ...mockDashboardStats,
        company: { id: 'company-1', companyName: 'Test Company' },
      };
      dashboardService.getEnhancedDashboardStats.mockResolvedValue(mockEnhancedStats as any);

      const result = await controller.getEnhancedDashboardStats(mockUser as any);

      expect(result).toEqual(mockEnhancedStats);
      expect(dashboardService.getEnhancedDashboardStats).toHaveBeenCalledWith('test-user-123');
    });

    it('should handle service errors', async () => {
      dashboardService.getEnhancedDashboardStats.mockRejectedValue(new Error('Service error'));

      await expect(controller.getEnhancedDashboardStats(mockUser as any)).rejects.toThrow(
        'Service error',
      );
    });
  });

  describe('getRegistrationStats', () => {
    it('should return registration stats', async () => {
      dashboardService.getRegistrationStats.mockResolvedValue(mockRegistrationStats as any);

      const result = await controller.getRegistrationStats();

      expect(result).toEqual(mockRegistrationStats);
      expect(dashboardService.getRegistrationStats).toHaveBeenCalled();
    });

    it('should handle service errors', async () => {
      dashboardService.getRegistrationStats.mockRejectedValue(new Error('Service error'));

      await expect(controller.getRegistrationStats()).rejects.toThrow('Service error');
    });
  });

  describe('getUsageStats', () => {
    it('should return usage stats', async () => {
      dashboardService.getUsageStats.mockResolvedValue(mockUsageStats as any);

      const result = await controller.getUsageStats();

      expect(result).toEqual(mockUsageStats);
      expect(dashboardService.getUsageStats).toHaveBeenCalled();
    });

    it('should handle service errors', async () => {
      dashboardService.getUsageStats.mockRejectedValue(new Error('Service error'));

      await expect(controller.getUsageStats()).rejects.toThrow('Service error');
    });
  });

  describe('getHiredStatus', () => {
    it('should return hired status', async () => {
      dashboardService.getHiredStatus.mockResolvedValue(mockHiredStatus as any);

      const result = await controller.getHiredStatus(mockUser as any);

      expect(result).toEqual(mockHiredStatus);
      expect(dashboardService.getHiredStatus).toHaveBeenCalledWith('test-user-123');
    });

    it('should handle not hired status', async () => {
      const notHiredStatus = { isHired: false, jobInfo: null };
      dashboardService.getHiredStatus.mockResolvedValue(notHiredStatus as any);

      const result = await controller.getHiredStatus(mockUser as any);

      expect(result).toEqual(notHiredStatus);
      expect(result.isHired).toBe(false);
    });

    it('should handle service errors', async () => {
      dashboardService.getHiredStatus.mockRejectedValue(new Error('Service error'));

      await expect(controller.getHiredStatus(mockUser as any)).rejects.toThrow('Service error');
    });
  });
});
