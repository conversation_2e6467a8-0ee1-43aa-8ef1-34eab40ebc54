export interface ComparisonOption {
  id: string;
  name: string;
  description: string;
  criteria: string[];
  estimatedTime: string;
  prompt: string;
}

export interface ComparisonTemplate {
  id: string;
  template: string;
  variables: string[];
}

export interface BenefitAnalysis {
  benefits: string[];
  bestUseCases: string[];
}

export const COMPARISON_OPTIONS = {
  presetComparisons: [
    {
      id: 'quick_overview',
      name: 'Quick Overview',
      description: 'Get a rapid side-by-side comparison of key metrics',
      criteria: ['matchScore', 'experience', 'skills', 'availability'],
      estimatedTime: '30 seconds',
      prompt:
        'Provide a concise comparison of these {count} candidates focusing on their match scores, relevant experience years, key skills alignment, and immediate availability. Highlight the most significant differences.',
    },
    {
      id: 'skills_deep_dive',
      name: 'Skills & Technical Fit',
      description: 'Detailed analysis of technical capabilities and skill gaps',
      criteria: ['technicalSkills', 'toolsProficiency', 'skillGaps', 'trainingNeeds'],
      estimatedTime: '2 minutes',
      prompt:
        "Compare these {count} candidates' technical competencies in detail. Analyze: 1) Matched skills vs required skills, 2) Proficiency in required tools/technologies, 3) Critical skill gaps, 4) Training investment needed for each candidate. Provide specific examples from their experience.",
    },
    {
      id: 'leadership_comparison',
      name: 'Leadership & Management',
      description: 'Compare leadership experience and team management capabilities',
      criteria: ['leadershipExperience', 'teamSize', 'projectComplexity', 'managementStyle'],
      estimatedTime: '2 minutes',
      prompt:
        'Analyze the leadership capabilities of these {count} candidates. Compare: 1) Years in leadership roles, 2) Size of teams managed, 3) Complexity of projects led, 4) Evidence of leadership style and effectiveness. Consider their potential for the {jobTitle} role.',
    },
    {
      id: 'cultural_team_fit',
      name: 'Cultural & Team Fit',
      description: 'Assess how each candidate would integrate with the existing team',
      criteria: ['culturalAlignment', 'collaborationStyle', 'communicationSkills', 'workStyle'],
      estimatedTime: '1.5 minutes',
      prompt:
        'Evaluate how these {count} candidates would fit within the team culture. Consider: 1) Alignment with company values, 2) Collaboration and communication style, 3) Potential team dynamics impact, 4) Unique perspectives they bring.',
    },
    {
      id: 'cost_benefit',
      name: 'Cost-Benefit Analysis',
      description: 'Compare compensation expectations vs. value delivered',
      criteria: ['salaryExpectations', 'experienceValue', 'immediateImpact', 'longTermPotential'],
      estimatedTime: '2 minutes',
      prompt:
        'Provide a cost-benefit analysis for these {count} candidates. Compare: 1) Salary expectations vs. market rate, 2) ROI based on experience and skills, 3) Time to productivity, 4) Long-term growth potential and retention likelihood.',
    },
    {
      id: 'risk_assessment',
      name: 'Risk Assessment',
      description: 'Identify and compare potential risks for each candidate',
      criteria: ['flightRisk', 'skillGaps', 'overqualification', 'adaptability'],
      estimatedTime: '1.5 minutes',
      prompt:
        'Assess the risks associated with hiring each of these {count} candidates. Consider: 1) Flight risk and retention factors, 2) Critical skill gaps and their impact, 3) Overqualification concerns, 4) Adaptability to company culture and processes.',
    },
  ] as ComparisonOption[],

  customComparisonPrompts: {
    templates: [
      {
        id: 'specific_project',
        template:
          'Compare how these {count} candidates would handle {projectDescription}. Consider their relevant experience, technical skills, and leadership approach.',
        variables: ['projectDescription'],
      },
      {
        id: 'scenario_based',
        template:
          'If we need someone who can {scenario}, which of these {count} candidates would be most suitable? Explain your reasoning.',
        variables: ['scenario'],
      },
      {
        id: 'growth_focused',
        template:
          "Looking at a {timeframe} timeline, compare these {count} candidates' potential for growth into {futureRole}.",
        variables: ['timeframe', 'futureRole'],
      },
    ] as ComparisonTemplate[],
  },

  defaultWeights: {
    skills: 0.25,
    experience: 0.25,
    culturalFit: 0.2,
    leadership: 0.15,
    compensation: 0.1,
    availability: 0.05,
  },

  benefitsAnalysis: {
    twoCandidate: {
      benefits: [
        'Direct head-to-head comparison simplifies decision-making',
        'Clear identification of trade-offs between two options',
        'Easier to spot complementary strengths and weaknesses',
        'Faster analysis and decision process',
        'Ideal for final round decisions',
      ],
      bestUseCases: [
        'Final selection between top candidates',
        'Comparing candidates with different strengths',
        'Quick decision needed with clear criteria',
        'When budget allows for only one hire',
      ],
    } as BenefitAnalysis,
    threeCandidate: {
      benefits: [
        'Broader perspective on available talent',
        'Ability to identify "dark horse" candidates',
        'Better understanding of market availability',
        'Options for different hiring scenarios',
        'Backup options if first choice declines',
      ],
      bestUseCases: [
        'Earlier stage evaluation',
        'When considering different candidate profiles',
        'Building a talent pipeline',
        'When multiple positions might open',
        'Negotiation leverage understanding',
      ],
    } as BenefitAnalysis,
  },

  outputFormats: {
    comparisonMatrix: {
      description: 'Side-by-side tabular comparison',
      fields: ['candidate', 'matchScore', 'keyStrengths', 'concerns', 'uniqueValue'],
    },
    narrativeSummary: {
      description: 'Detailed written analysis',
      sections: [
        'executiveSummary',
        'individualAnalysis',
        'comparativeAdvantages',
        'recommendations',
      ],
    },
    visualDashboard: {
      description: 'Interactive charts and graphs',
      components: ['radarChart', 'scoreCards', 'timelineComparison', 'skillsHeatmap'],
    },
  },
};

export function getComparisonOption(id: string): ComparisonOption | undefined {
  return COMPARISON_OPTIONS.presetComparisons.find((option) => option.id === id);
}

export function getDefaultCriteria(comparisonType: string): string[] {
  const option = getComparisonOption(comparisonType);
  return option?.criteria || COMPARISON_OPTIONS.presetComparisons[0].criteria;
}

export function interpolatePrompt(template: string, variables: Record<string, any>): string {
  let result = template;
  Object.entries(variables).forEach(([key, value]) => {
    result = result.replace(new RegExp(`\\{${key}\\}`, 'g'), value);
  });
  return result;
}
