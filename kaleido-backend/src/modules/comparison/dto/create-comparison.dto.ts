import {
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  IsNotEmpty,
  IsO<PERSON>al,
  IsString,
  IsUUID,
  ArrayMinSize,
  ArrayMaxSize,
  IsObject,
  ValidateNested,
} from 'class-validator';
import { Type } from 'class-transformer';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export enum ComparisonType {
  QUICK_OVERVIEW = 'quick_overview',
  SKILLS_DEEP_DIVE = 'skills_deep_dive',
  LEADERSHIP_COMPARISON = 'leadership_comparison',
  CULTURAL_TEAM_FIT = 'cultural_team_fit',
  COST_BENEFIT = 'cost_benefit',
  RISK_ASSESSMENT = 'risk_assessment',
  CUSTOM = 'custom',
}

class WeightsDto {
  @ApiPropertyOptional({ example: 0.25 })
  @IsOptional()
  skills?: number;

  @ApiPropertyOptional({ example: 0.25 })
  @IsOptional()
  experience?: number;

  @ApiPropertyOptional({ example: 0.2 })
  @IsOptional()
  culturalFit?: number;

  @ApiPropertyOptional({ example: 0.15 })
  @IsOptional()
  leadership?: number;

  @ApiPropertyOptional({ example: 0.1 })
  @IsOptional()
  compensation?: number;

  @ApiPropertyOptional({ example: 0.05 })
  @IsOptional()
  availability?: number;
}

export class CreateComparisonDto {
  @ApiProperty({ description: 'Job ID for the comparison' })
  @IsUUID()
  @IsNotEmpty()
  jobId!: string;

  @ApiProperty({
    description: 'Array of 2-3 candidate IDs to compare',
    type: [String],
    minItems: 2,
    maxItems: 3,
  })
  @IsArray()
  @ArrayMinSize(2)
  @ArrayMaxSize(3)
  @IsUUID('4', { each: true })
  candidateIds!: string[];

  @ApiProperty({
    description: 'Type of comparison',
    enum: ComparisonType,
    example: ComparisonType.QUICK_OVERVIEW,
  })
  @IsEnum(ComparisonType)
  comparisonType!: ComparisonType;

  @ApiPropertyOptional({
    description: 'Custom prompt or additional context from user',
    example: 'Focus on who can start immediately',
  })
  @IsOptional()
  @IsString()
  userPrompt?: string;

  @ApiPropertyOptional({
    description: 'Custom criteria for comparison',
    type: [String],
    example: ['availability', 'relocation'],
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  criteria?: string[];

  @ApiPropertyOptional({
    description: 'Weights for different criteria',
    type: WeightsDto,
  })
  @IsOptional()
  @IsObject()
  @ValidateNested()
  @Type(() => WeightsDto)
  weights?: WeightsDto;
}

export class ScenarioComparisonDto {
  @ApiProperty({ description: 'Job ID for the comparison' })
  @IsUUID()
  @IsNotEmpty()
  jobId!: string;

  @ApiProperty({
    description: 'Array of 2-3 candidate IDs to compare',
    type: [String],
    minItems: 2,
    maxItems: 3,
  })
  @IsArray()
  @ArrayMinSize(2)
  @ArrayMaxSize(3)
  @IsUUID('4', { each: true })
  candidateIds!: string[];

  @ApiProperty({
    description: 'Scenario description',
    example:
      'We need someone who can quickly modernize our animation pipeline and mentor junior animators',
  })
  @IsString()
  @IsNotEmpty()
  scenario!: string;
}
