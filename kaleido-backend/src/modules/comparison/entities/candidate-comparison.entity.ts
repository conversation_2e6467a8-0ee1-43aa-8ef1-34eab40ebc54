import {
  Entity,
  PrimaryGeneratedC<PERSON>umn,
  <PERSON><PERSON>n,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
  Index,
} from 'typeorm';
import { BaseEntity } from '@shared/entities/base.entity';

export interface ComparisonCriteria {
  type: 'preset' | 'custom';
  criteria: string[];
  weights?: { [key: string]: number };
}

export interface CandidateComparisonDetail {
  strengths: string[];
  weaknesses: string[];
  uniqueAdvantages: string[];
  riskFactors: string[];
  overallRank: number;
  scores: { [criterion: string]: number };
  keyDifferentiator?: string;
}

export interface ComparisonRecommendation {
  topChoice: {
    candidateId: string;
    reasoning: string;
  };
  alternativeScenarios: Array<{
    scenario: string;
    recommendedCandidate: string;
    reasoning?: string;
  }>;
  hiringStrategy?: string;
}

export interface TradeOff {
  candidateA: string;
  candidateB: string;
  comparison: string;
}

export interface ComparisonResults {
  summary: string;
  executiveSummary?: string;
  detailedComparison: {
    [candidateId: string]: CandidateComparisonDetail;
  };
  candidateAnalysis?: {
    [candidateId: string]: CandidateComparisonDetail;
  };
  recommendations: ComparisonRecommendation;
  tradeOffs?: TradeOff[];
  headToHeadComparisons?: Array<{
    candidate1: string;
    candidate2: string;
    keyDifference: string;
    recommendation: string;
  }>;
  criticalConsiderations?: string[];
}

export interface VisualizationData {
  radarChartData?: any;
  barChartData?: any;
  comparisonMatrix?: any;
}

@Entity('candidate_comparisons')
@Index('idx_comparison_client_job', ['clientId', 'jobId'])
@Index('idx_comparison_status', ['status'])
@Index('idx_comparison_created', ['createdAt'])
export class CandidateComparison extends BaseEntity {
  @Column({ type: 'uuid' })
  jobId!: string;

  @Column({ type: 'jsonb' })
  candidateIds!: string[]; // Array of 2-3 candidate IDs

  @Column({ type: 'varchar', length: 500 })
  comparisonTitle!: string;

  @Column({ type: 'text', nullable: true })
  userPrompt?: string;

  @Column({ type: 'json' })
  comparisonCriteria!: ComparisonCriteria;

  @Column({ type: 'json', nullable: true })
  comparisonResults?: ComparisonResults;

  @Column({ type: 'json', nullable: true })
  visualData?: VisualizationData;

  @Column({
    type: 'enum',
    enum: ['pending', 'completed', 'failed'],
    default: 'pending',
  })
  status!: string;

  @Column({ type: 'varchar', nullable: true })
  performedBy?: string;

  @Column({ type: 'timestamp', nullable: true })
  expiresAt?: Date;

  @Column({ type: 'varchar', nullable: true })
  comparisonType?: string; // quick_overview, skills_deep_dive, etc.

  @Column({ type: 'json', nullable: true })
  metadata?: any; // For additional data storage
}
