import { Module, forwardRef } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { BullModule } from '@nestjs/bull';
import { CandidateComparison } from './entities/candidate-comparison.entity';
import { CandidateComparisonService } from './services/candidate-comparison.service';
import { CandidateComparisonController } from './controllers/candidate-comparison.controller';
import { ComparisonPromptGenerator } from './services/comparison-prompt-generator';
import { Candidate } from '@modules/candidate/entities/candidate.entity';
import { Job } from '@modules/job/entities/job.entity';
import { OpenAIModule } from './openai.module';
import { QueueModule } from '@modules/queue/queue.module';
import { QUEUE_NAMES } from '@/shared/constants/queue.constants';

@Module({
  imports: [
    TypeOrmModule.forFeature([CandidateComparison, Candidate, Job]),
    OpenAIModule,
    BullModule.registerQueue({
      name: QUEUE_NAMES.CANDIDATE_COMPARISON,
    }),
    forwardRef(() => QueueModule),
  ],
  providers: [CandidateComparisonService, ComparisonPromptGenerator],
  controllers: [CandidateComparisonController],
  exports: [CandidateComparisonService],
})
export class CandidateComparisonModule {}
