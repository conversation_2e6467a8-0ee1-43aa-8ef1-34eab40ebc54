import { Test, TestingModule } from '@nestjs/testing';
import { CandidateComparisonController } from './candidate-comparison.controller';
import { CandidateComparisonService } from '../services/candidate-comparison.service';
import { QueueService } from '@/modules/queue/queue.service';
import { CreateComparisonDto, ScenarioComparisonDto, ComparisonType } from '../dto/create-comparison.dto';
import { CandidateComparison } from '../entities/candidate-comparison.entity';
import { User } from '@/shared/decorators/get-user.decorator';
import { Auth0Guard } from '@/auth/auth.guard';

describe('CandidateComparisonController', () => {
  let controller: CandidateComparisonController;
  let comparisonService: jest.Mocked<CandidateComparisonService>;
  let queueService: jest.Mocked<QueueService>;

  const mockUser: User = {
    userId: 'user-123',
    email: '<EMAIL>',
    roles: ['employer'],
  };

  const mockCreateComparisonDto: CreateComparisonDto = {
    jobId: 'job-123',
    candidateIds: ['candidate-1', 'candidate-2', 'candidate-3'],
    comparisonType: ComparisonType.QUICK_OVERVIEW,
    userPrompt: 'Compare these candidates for the role',
  };

  const mockScenarioComparisonDto: ScenarioComparisonDto = {
    jobId: 'job-123',
    candidateIds: ['candidate-1', 'candidate-2'],
    scenario: 'How well suited are these candidates for remote work?',
  };

  const mockComparison: Partial<CandidateComparison> = {
    id: 'comparison-123',
    jobId: 'job-123',
    candidateIds: ['candidate-1', 'candidate-2', 'candidate-3'],
    comparisonType: ComparisonType.QUICK_OVERVIEW,
    status: 'pending',
    createdAt: new Date(),
    updatedAt: new Date(),
  };

  const mockQueueResponse = {
    id: 'queue-job-123',
    jobId: 'comparison-123',
    status: 'queued',
    message: 'Comparison job created successfully',
  };

  const mockQueueStatus = {
    jobId: 'queue-job-123',
    status: 'processing',
    progress: 45,
    message: 'Analyzing candidates...',
    result: null,
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [CandidateComparisonController],
      providers: [
        {
          provide: CandidateComparisonService,
          useValue: {
            createComparison: jest.fn(),
            createScenarioComparison: jest.fn(),
            getComparison: jest.fn(),
            getComparisonStatus: jest.fn(),
            getJobComparisons: jest.fn(),
            generateComparisonReport: jest.fn(),
            deleteComparison: jest.fn(),
          },
        },
        {
          provide: QueueService,
          useValue: {
            getComparisonStatus: jest.fn(),
          },
        },
      ],
    })
      .overrideGuard(Auth0Guard)
      .useValue({
        canActivate: jest.fn(() => true),
      })
      .compile();

    controller = module.get<CandidateComparisonController>(CandidateComparisonController);
    comparisonService = module.get(CandidateComparisonService);
    queueService = module.get(QueueService);
  });

  describe('createComparison', () => {
    it('should create a new comparison and return job info', async () => {
      comparisonService.createComparison.mockResolvedValue(mockQueueResponse);

      const result = await controller.createComparison(mockCreateComparisonDto, mockUser);

      expect(comparisonService.createComparison).toHaveBeenCalledWith(
        mockUser.userId,
        mockCreateComparisonDto
      );
      expect(result).toEqual(mockQueueResponse);
    });

    it('should handle errors when creating comparison', async () => {
      const error = new Error('Failed to create comparison');
      comparisonService.createComparison.mockRejectedValue(error);

      await expect(
        controller.createComparison(mockCreateComparisonDto, mockUser)
      ).rejects.toThrow('Failed to create comparison');
    });
  });

  describe('createScenarioComparison', () => {
    it('should create a scenario-based comparison', async () => {
      comparisonService.createScenarioComparison.mockResolvedValue(mockQueueResponse);

      const result = await controller.createScenarioComparison(mockScenarioComparisonDto, mockUser);

      expect(comparisonService.createScenarioComparison).toHaveBeenCalledWith(
        mockUser.userId,
        mockScenarioComparisonDto
      );
      expect(result).toEqual(mockQueueResponse);
    });
  });

  describe('getComparison', () => {
    it('should return comparison details', async () => {
      comparisonService.getComparison.mockResolvedValue(mockComparison as CandidateComparison);

      const result = await controller.getComparison('comparison-123', mockUser);

      expect(comparisonService.getComparison).toHaveBeenCalledWith('comparison-123', mockUser.userId);
      expect(result).toEqual(mockComparison);
    });

    it('should handle not found error', async () => {
      comparisonService.getComparison.mockRejectedValue(new Error('Comparison not found'));

      await expect(
        controller.getComparison('invalid-id', mockUser)
      ).rejects.toThrow('Comparison not found');
    });
  });

  describe('getComparisonStatus', () => {
    it('should return comparison processing status', async () => {
      const mockStatus = { status: 'completed', progress: 100 };
      comparisonService.getComparisonStatus.mockResolvedValue(mockStatus);

      const result = await controller.getComparisonStatus('comparison-123', mockUser);

      expect(comparisonService.getComparisonStatus).toHaveBeenCalledWith(
        'comparison-123',
        mockUser.userId
      );
      expect(result).toEqual(mockStatus);
    });
  });

  describe('getComparisonJobStatus', () => {
    it('should return queue job status following the pattern /comparisons/status/:jobId', async () => {
      queueService.getComparisonStatus.mockResolvedValue(mockQueueStatus);

      const result = await controller.getComparisonJobStatus('queue-job-123');

      expect(queueService.getComparisonStatus).toHaveBeenCalledWith('queue-job-123');
      expect(result).toEqual(mockQueueStatus);
    });

    it('should handle completed status with results', async () => {
      const completedStatus = {
        ...mockQueueStatus,
        status: 'completed',
        progress: 100,
        result: {
          comparisonId: 'comparison-123',
          executiveSummary: 'Based on the analysis...',
          recommendations: {
            topChoice: {
              candidateId: 'candidate-1',
              candidateName: 'John Doe',
              reasoning: 'Best fit for the role',
            },
          },
        },
      };

      queueService.getComparisonStatus.mockResolvedValue(completedStatus);

      const result = await controller.getComparisonJobStatus('queue-job-123');

      expect(result).toEqual(completedStatus);
      expect(result.status).toBe('completed');
      expect(result.result).toBeDefined();
    });

    it('should handle failed status', async () => {
      const failedStatus = {
        ...mockQueueStatus,
        status: 'failed',
        progress: 0,
        message: 'Failed to process comparison',
        error: 'OpenAI API error',
      };

      queueService.getComparisonStatus.mockResolvedValue(failedStatus);

      const result = await controller.getComparisonJobStatus('queue-job-123');

      expect(result.status).toBe('failed');
      expect(result.error).toBeDefined();
    });
  });

  describe('getJobComparisons', () => {
    it('should return all comparisons for a job', async () => {
      const mockComparisons = [mockComparison, { ...mockComparison, id: 'comparison-456' }];
      comparisonService.getJobComparisons.mockResolvedValue(mockComparisons as CandidateComparison[]);

      const result = await controller.getJobComparisons('job-123', mockUser);

      expect(comparisonService.getJobComparisons).toHaveBeenCalledWith('job-123', mockUser.userId);
      expect(result).toHaveLength(2);
      expect(result[0].jobId).toBe('job-123');
    });
  });

  describe('generateReport', () => {
    it('should generate comparison report', async () => {
      const mockReport = {
        comparisonId: 'comparison-123',
        summary: 'Detailed comparison report',
        data: {},
      };
      comparisonService.generateComparisonReport.mockResolvedValue(mockReport);

      const result = await controller.generateReport('comparison-123', mockUser);

      expect(comparisonService.generateComparisonReport).toHaveBeenCalledWith(
        'comparison-123',
        mockUser.userId
      );
      expect(result).toEqual(mockReport);
    });
  });

  describe('deleteComparison', () => {
    it('should delete comparison successfully', async () => {
      comparisonService.deleteComparison.mockResolvedValue(undefined);

      await controller.deleteComparison('comparison-123', mockUser);

      expect(comparisonService.deleteComparison).toHaveBeenCalledWith(
        'comparison-123',
        mockUser.userId
      );
    });
  });

  describe('getComparisonOptions', () => {
    it('should return comparison configuration options', () => {
      const result = controller.getComparisonOptions();

      expect(result).toBeDefined();
      expect(result).toHaveProperty('presetComparisons');
      expect(result).toHaveProperty('defaultWeights');
      expect(result).toHaveProperty('outputFormats');
      expect(result).toHaveProperty('benefitsAnalysis');
      expect(result).toHaveProperty('customComparisonPrompts');
      expect(Array.isArray(result.presetComparisons)).toBe(true);
      expect(result.presetComparisons.length).toBeGreaterThan(0);
    });
  });
});