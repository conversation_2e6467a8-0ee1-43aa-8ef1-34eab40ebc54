import { Injectable, NotFoundException, BadRequestException, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, In } from 'typeorm';
import { InjectQueue } from '@nestjs/bull';
import { Queue } from 'bull';
import { CandidateComparison } from '../entities/candidate-comparison.entity';
import { Candidate } from '@modules/candidate/entities/candidate.entity';
import { Job } from '@modules/job/entities/job.entity';
import { ComparisonPromptGenerator } from './comparison-prompt-generator';
import { CreateComparisonDto, ScenarioComparisonDto } from '../dto/create-comparison.dto';
import { getDefaultCriteria } from '../config/comparison-options.config';
import { OpenAIService } from './openai.service';
import { QUEUE_NAMES, PROCESSOR_NAMES } from '@/shared/constants/queue.constants';

@Injectable()
export class CandidateComparisonService {
  private readonly logger = new Logger(CandidateComparisonService.name);

  constructor(
    @InjectRepository(CandidateComparison)
    private comparisonRepository: Repository<CandidateComparison>,
    @InjectRepository(Candidate)
    private candidateRepository: Repository<Candidate>,
    @InjectRepository(Job)
    private jobRepository: Repository<Job>,
    private openAIService: OpenAIService,
    @InjectQueue(QUEUE_NAMES.CANDIDATE_COMPARISON)
    private comparisonQueue: Queue,
  ) {}

  /**
   * Create a new candidate comparison
   */
  async createComparison(
    clientId: string,
    createComparisonDto: CreateComparisonDto,
  ): Promise<{ id: string; jobId: string; status: string; message: string }> {
    const { jobId, candidateIds, comparisonType, userPrompt, criteria, weights } =
      createComparisonDto;

    // Validate candidate count
    if (candidateIds.length < 2 || candidateIds.length > 3) {
      throw new BadRequestException('Comparison requires 2 or 3 candidates');
    }

    // Fetch job
    const job = await this.jobRepository.findOne({
      where: { id: jobId, clientId },
    });

    if (!job) {
      throw new NotFoundException('Job not found');
    }

    // Fetch candidates with evaluations
    const candidates = await this.candidateRepository
      .createQueryBuilder('candidate')
      .leftJoinAndSelect('candidate.evaluations', 'evaluations')
      .where('candidate.id IN (:...candidateIds)', { candidateIds })
      .andWhere('candidate.clientId = :clientId', { clientId })
      .getMany();

    if (candidates.length !== candidateIds.length) {
      throw new NotFoundException('One or more candidates not found');
    }

    // Create comparison entity
    const comparison = this.comparisonRepository.create({
      clientId,
      jobId,
      candidateIds,
      comparisonTitle: `${comparisonType.replace(/_/g, ' ')} comparison for ${candidates.map((c) => c.firstName).join(', ')}`,
      userPrompt,
      comparisonType,
      comparisonCriteria: {
        type: criteria ? 'custom' : 'preset',
        criteria: criteria || getDefaultCriteria(comparisonType),
        weights: weights as { [key: string]: number } | undefined,
      },
      status: 'pending',
      performedBy: clientId,
      metadata: {
        jobTitle: job.jobType,
        companyName: job.companyName,
        candidateNames: candidates.map((c) => ({ id: c.id, name: c.fullName })),
      },
    });

    // Save initial comparison
    const savedComparison = await this.comparisonRepository.save(comparison);

    // Add job to queue for async processing
    const queueJob = await this.comparisonQueue.add(
      PROCESSOR_NAMES.PROCESS_COMPARISON,
      {
        comparisonId: savedComparison.id,
        userId: clientId,
      },
      {
        attempts: 3,
        backoff: {
          type: 'exponential',
          delay: 2000,
        },
      },
    );

    this.logger.log(`Added comparison ${savedComparison.id} to queue with job ID: ${queueJob.id}`);

    // Return data following the pattern where id is the queue job ID and jobId is the entity ID
    return {
      id: queueJob.id.toString(), // Bull queue job ID
      jobId: savedComparison.id, // Comparison entity ID
      status: savedComparison.status,
      message: 'Comparison initiated. Check status for results.',
    } as any;
  }

  /**
   * Create a scenario-based comparison
   */
  async createScenarioComparison(
    clientId: string,
    scenarioDto: ScenarioComparisonDto,
  ): Promise<{ id: string; jobId: string; status: string; message: string }> {
    const { jobId, candidateIds, scenario } = scenarioDto;

    // Validate candidate count
    if (candidateIds.length < 2 || candidateIds.length > 3) {
      throw new BadRequestException('Comparison requires 2 or 3 candidates');
    }

    // Fetch job
    const job = await this.jobRepository.findOne({
      where: { id: jobId, clientId },
    });

    if (!job) {
      throw new NotFoundException('Job not found');
    }

    // Fetch candidates
    const candidates = await this.candidateRepository
      .createQueryBuilder('candidate')
      .leftJoinAndSelect('candidate.evaluations', 'evaluations')
      .where('candidate.id IN (:...candidateIds)', { candidateIds })
      .andWhere('candidate.clientId = :clientId', { clientId })
      .getMany();

    if (candidates.length !== candidateIds.length) {
      throw new NotFoundException('One or more candidates not found');
    }

    // Create comparison entity
    const comparison = this.comparisonRepository.create({
      clientId,
      jobId,
      candidateIds,
      comparisonTitle: `Scenario comparison: ${scenario.substring(0, 100)}...`,
      userPrompt: scenario,
      comparisonType: 'custom',
      comparisonCriteria: {
        type: 'custom',
        criteria: ['scenario_fit'],
      },
      status: 'pending',
      performedBy: clientId,
      metadata: {
        jobTitle: job.jobType,
        companyName: job.companyName,
        candidateNames: candidates.map((c) => ({ id: c.id, name: c.fullName })),
        scenario,
      },
    });

    // Save initial comparison
    const savedComparison = await this.comparisonRepository.save(comparison);

    // Add job to queue for async processing
    const queueJob = await this.comparisonQueue.add(
      PROCESSOR_NAMES.PROCESS_COMPARISON,
      {
        comparisonId: savedComparison.id,
        userId: clientId,
      },
      {
        attempts: 3,
        backoff: {
          type: 'exponential',
          delay: 2000,
        },
      },
    );

    this.logger.log(
      `Added scenario comparison ${savedComparison.id} to queue with job ID: ${queueJob.id}`,
    );

    // Return data following the pattern where id is the queue job ID and jobId is the entity ID
    return {
      id: queueJob.id.toString(), // Bull queue job ID
      jobId: savedComparison.id, // Comparison entity ID
      status: savedComparison.status,
      message: 'Comparison initiated. Check status for results.',
    } as any;
  }

  /**
   * Process a comparison (called by the queue processor)
   */
  async processComparison(comparison: CandidateComparison): Promise<any> {
    try {
      // Get the job and candidates
      const job = await this.jobRepository.findOne({
        where: { id: comparison.jobId },
        select: [
          'id',
          'jobType',
          'companyDescription',
          'requirements',
          'jobResponsibilities',
          'location',
          'companyName',
        ],
      });

      if (!job) {
        throw new Error('Job not found');
      }

      const candidates = await this.candidateRepository.find({
        where: { id: In(comparison.candidateIds) },
        relations: ['evaluations', 'job', 'jobSeeker', 'graduate'],
      });

      if (candidates.length !== comparison.candidateIds.length) {
        throw new Error('Some candidates not found');
      }

      // Determine if this is a scenario comparison
      const isScenario = comparison.metadata?.scenario !== undefined;

      if (isScenario) {
        return await this._processScenarioComparison(
          comparison.id,
          candidates,
          job,
          comparison.metadata.scenario,
        );
      } else {
        return await this._processStandardComparison(
          comparison.id,
          candidates,
          job,
          comparison.comparisonType || 'quick_overview',
          comparison.userPrompt,
          comparison.comparisonCriteria?.criteria,
        );
      }
    } catch (error) {
      this.logger.error('Error processing comparison:', error);
      throw error;
    }
  }

  /**
   * Process the comparison using AI (internal method)
   */
  private async _processStandardComparison(
    comparisonId: string,
    candidates: Candidate[],
    job: Job,
    comparisonType: string,
    userPrompt?: string,
    customCriteria?: string[],
  ): Promise<void> {
    try {
      // Generate the prompt
      const prompt = ComparisonPromptGenerator.generateComparisonPrompt(
        candidates,
        job,
        comparisonType,
        userPrompt,
        customCriteria,
      );

      // Create candidate name map for visualization
      const candidateNameMap = candidates.reduce(
        (acc, candidate) => {
          acc[candidate.id] = candidate.fullName;
          return acc;
        },
        {} as Record<string, string>,
      );

      // Call AI service
      const aiResponse = await this.openAIService.generateCompletion(prompt, {
        model: 'gpt-4o-mini',
        temperature: 0.7,
        max_tokens: 2000,
      });

      // Parse the response
      const comparisonResults = ComparisonPromptGenerator.parseAIResponse(aiResponse);

      // Generate visualization data with candidate names
      const visualData = ComparisonPromptGenerator.generateVisualizationData(
        comparisonResults,
        candidateNameMap,
      );

      // Update comparison with results
      await this.comparisonRepository.update(comparisonId, {
        comparisonResults,
        visualData,
        status: 'completed',
      });
    } catch (error) {
      console.error('Error in processComparison:', error);
      await this.comparisonRepository.update(comparisonId, {
        status: 'failed',
        metadata: {
          error: error.message,
        },
      });
      throw error;
    }
  }

  /**
   * Process scenario-based comparison (internal method)
   */
  private async _processScenarioComparison(
    comparisonId: string,
    candidates: Candidate[],
    job: Job,
    scenario: string,
  ): Promise<void> {
    try {
      // Generate the prompt
      const prompt = ComparisonPromptGenerator.generateScenarioPrompt(candidates, job, scenario);

      // Create candidate name map for visualization
      const candidateNameMap = candidates.reduce(
        (acc, candidate) => {
          acc[candidate.id] = candidate.fullName;
          return acc;
        },
        {} as Record<string, string>,
      );

      // Call AI service
      const aiResponse = await this.openAIService.generateCompletion(prompt, {
        model: 'gpt-4o-mini',
        temperature: 0.7,
        max_tokens: 2000,
      });

      // Parse the response
      const comparisonResults = ComparisonPromptGenerator.parseAIResponse(aiResponse);

      // Generate visualization data with candidate names
      const visualData = ComparisonPromptGenerator.generateVisualizationData(
        comparisonResults,
        candidateNameMap,
      );

      // Update comparison with results
      await this.comparisonRepository.update(comparisonId, {
        comparisonResults,
        visualData,
        status: 'completed',
      });
    } catch (error) {
      console.error('Error in processScenarioComparison:', error);
      await this.comparisonRepository.update(comparisonId, {
        status: 'failed',
        metadata: {
          error: error.message,
        },
      });
      throw error;
    }
  }

  /**
   * Get comparison by ID
   */
  async getComparison(comparisonId: string, clientId: string): Promise<CandidateComparison> {
    const comparison = await this.comparisonRepository.findOne({
      where: { id: comparisonId, clientId },
    });

    if (!comparison) {
      throw new NotFoundException('Comparison not found');
    }

    return comparison;
  }

  /**
   * Get all comparisons for a job
   */
  async getJobComparisons(jobId: string, clientId: string): Promise<CandidateComparison[]> {
    return this.comparisonRepository.find({
      where: { jobId, clientId },
      order: { createdAt: 'DESC' },
      // Remove the select to get all fields including comparisonResults
    });
  }

  /**
   * Generate a comparison report
   */
  async generateComparisonReport(comparisonId: string, clientId: string): Promise<any> {
    const comparison = await this.getComparison(comparisonId, clientId);

    if (comparison.status !== 'completed') {
      throw new BadRequestException('Comparison is not yet completed');
    }

    // Fetch candidates for the report
    const candidates = await this.candidateRepository.find({
      where: { id: In(comparison.candidateIds) },
    });

    // Fetch job for context
    const job = await this.jobRepository.findOne({ where: { id: comparison.jobId } });

    if (!job) {
      throw new NotFoundException('Job not found');
    }

    // Format the report
    const results = (comparison.comparisonResults as any) || {};

    return {
      title: comparison.comparisonTitle,
      generatedAt: new Date(),
      job: {
        id: job.id,
        title: job.jobType,
        company: job.companyName,
      },
      summary: results.summary || results.executiveSummary || '',
      candidates: candidates.map((c) => ({
        id: c.id,
        name: c.fullName,
        currentRole: c.getDisplayTitle(),
        company: c.currentCompany,
      })),
      analysis: results.candidateAnalysis || results.detailedComparison || {},
      recommendations: results.recommendations || {},
      tradeOffs: results.tradeOffs || [],
      headToHeadComparisons: results.headToHeadComparisons || [],
      criticalConsiderations: results.criticalConsiderations || [],
      visualizations: comparison.visualData,
    };
  }

  /**
   * Delete a comparison
   */
  async deleteComparison(comparisonId: string, clientId: string): Promise<void> {
    const comparison = await this.getComparison(comparisonId, clientId);
    await this.comparisonRepository.remove(comparison);
  }

  /**
   * Get comparison status
   */
  async getComparisonStatus(
    comparisonId: string,
    clientId: string,
  ): Promise<{ status: string; progress?: number }> {
    const comparison = await this.comparisonRepository.findOne({
      where: { id: comparisonId, clientId },
      select: ['status', 'createdAt'],
    });

    if (!comparison) {
      throw new NotFoundException('Comparison not found');
    }

    // Estimate progress based on time elapsed (comparisons usually take 30s to 2min)
    let progress = 0;
    if (comparison.status === 'pending') {
      const elapsed = Date.now() - comparison.createdAt.getTime();
      progress = Math.min(90, Math.floor((elapsed / 60000) * 90)); // Cap at 90% until completed
    } else if (comparison.status === 'completed') {
      progress = 100;
    }

    return {
      status: comparison.status,
      progress,
    };
  }
}
