import { Candidate } from '@modules/candidate/entities/candidate.entity';
import { Job } from '@modules/job/entities/job.entity';
import { getComparisonOption, interpolatePrompt } from '../config/comparison-options.config';

export interface CandidateData {
  id: string;
  fullName: string;
  jobTitle: string;
  experience: Array<{
    title: string;
    company: string;
    duration?: string;
    startDate: string;
    endDate: string | null;
  }>;
  skills: string[];
  evaluation: {
    matchScore: number;
    criterionMatchedOn: string[];
    detailedScoreAnalysis: any;
  };
  yearsOfExperience: number | null;
  currentCompany: string;
  status: string;
  tier: string;
}

export class ComparisonPromptGenerator {
  /**
   * Generates the optimal prompt for comparing candidates based on criteria and context
   */
  static generateComparisonPrompt(
    candidates: Candidate[],
    job: Job,
    comparisonType: string,
    userContext?: string,
    customCriteria?: string[],
  ): string {
    const candidateCount = candidates.length;
    const jobTitle = job.jobType;

    // Transform candidates to CandidateData format
    const candidateData = candidates.map((candidate) => ({
      id: candidate.id,
      fullName: candidate.fullName,
      jobTitle: candidate.getDisplayTitle(),
      currentCompany: candidate.currentCompany || 'Not specified',
      yearsOfExperience: candidate.getYearsOfExperience(),
      status: candidate.status,
      tier: candidate.tier || 'OTHER',
      skills: candidate.skills || [],
      experience: candidate.experience || [],
      evaluation: candidate.getEvaluationForJob(job.id) || {
        matchScore: 0,
        criterionMatchedOn: [],
        detailedScoreAnalysis: {},
      },
    }));

    // Create a mapping of candidate IDs to names for reference
    const candidateNameMap = candidateData.reduce(
      (acc, candidate) => {
        acc[candidate.id] = candidate.fullName;
        return acc;
      },
      {} as Record<string, string>,
    );

    // Base context about the comparison
    const baseContext = `You are comparing ${candidateCount} candidates for the ${jobTitle} position. 

IMPORTANT: Always use candidate names (not "Candidate 1", "Candidate 2", etc.) throughout your analysis.

${candidateData
  .map(
    (candidate) => `
${candidate.fullName}:
- Current: ${candidate.jobTitle} at ${candidate.currentCompany}
- Match Score: ${candidate.evaluation.matchScore}%
- Experience: ${candidate.yearsOfExperience || 0} years
- Key Skills: ${candidate.skills.slice(0, 5).join(', ')}
- Recent Roles: ${candidate.experience
      .slice(0, 2)
      .map((exp) => `${exp.title} at ${exp.company}`)
      .join('; ')}
- Strengths: ${(candidate.evaluation.detailedScoreAnalysis as any)?.areasOfStrength?.slice(0, 2).join(', ') || 'Not evaluated'}
`,
  )
  .join('\n')}`;

    // Specific instructions based on comparison type
    const typeSpecificInstructions = this.getTypeSpecificInstructions(
      comparisonType,
      candidateCount,
      jobTitle,
    );

    // Custom criteria instructions
    const criteriaInstructions =
      customCriteria && customCriteria.length > 0 ? `\nFocus on: ${customCriteria.join(', ')}` : '';

    // User context integration
    const contextInstructions = userContext ? `\nHiring context: "${userContext}"` : '';

    // Output format instructions with improved conciseness
    const outputInstructions = `
CRITICAL INSTRUCTIONS:
1. Be concise and direct - no fluff or generic statements
2. Use actual candidate names throughout (e.g., "John Smith" not "Candidate 1")
3. In candidateAnalysis, use candidate NAMES as keys (e.g., "John Smith": {...}), NOT IDs
4. Focus on specific, actionable insights
5. Highlight key differentiators between candidates
6. Provide data-driven recommendations

IMPORTANT: The candidateAnalysis object MUST use candidate names as keys. For example:
"candidateAnalysis": {
  "Alex Prada": { ... },
  "Jason Jainnini": { ... },
  "Mirko Vukadinovic": { ... }
}
NOT candidate IDs like "9ae59056-2d27-4e8a-9bc2-eba494858213"

Output JSON format:
{
  "executiveSummary": "2-3 sentences comparing ${candidateData.map((c) => c.fullName).join(', ')} with clear winner and why",
  "candidateAnalysis": {
    "${candidateData[0].fullName}": {
      "overallRank": 1-${candidateCount},
      "strengths": ["specific strength 1", "specific strength 2"],
      "weaknesses": ["specific weakness 1", "specific weakness 2"],
      "uniqueAdvantages": ["what only this candidate offers"],
      "riskFactors": ["specific risk 1", "specific risk 2"],
      "scores": {
        "skills": 0-100,
        "experience": 0-100,
        "leadership": 0-100,
        "culturalFit": 0-100,
        "availability": 0-100
      },
      "keyDifferentiator": "One sentence on what makes ${candidateData[0].fullName} unique"
    }
    // ... repeat for other candidates using their actual NAMES (not IDs)
  },
  "headToHeadComparisons": [
    {
      "candidate1": "${candidateData[0].fullName}",
      "candidate2": "${candidateData[1].fullName}", 
      "keyDifference": "Specific differentiator",
      "recommendation": "Who wins and why"
    }
  ],
  "recommendations": {
    "topChoice": {
      "candidateId": "${candidateData[0].fullName}",
      "reasoning": "Specific reasons why they're #1"
    },
    "alternativeScenarios": [
      {
        "scenario": "If [specific condition]",
        "recommendedCandidate": "Name (not ID)",
        "reasoning": "Why they'd be better in this case"
      }
    ],
    "hiringStrategy": "Specific action plan for hiring"
  },
  "criticalConsiderations": ["Specific factor 1", "Specific factor 2", "Specific factor 3"]
}`;

    // Combine all parts
    return `${baseContext}

${typeSpecificInstructions}
${criteriaInstructions}
${contextInstructions}

${outputInstructions}

Ensure your analysis is objective, data-driven, and provides actionable insights for the hiring decision.`;
  }

  /**
   * Get specific instructions based on comparison type
   */
  private static getTypeSpecificInstructions(
    comparisonType: string,
    candidateCount: number,
    jobTitle: string,
  ): string {
    const option = getComparisonOption(comparisonType);

    if (option) {
      return interpolatePrompt(option.prompt, { count: candidateCount, jobTitle });
    }

    // Default detailed comparison
    return `Provide a comprehensive analysis covering all aspects: skills match, experience relevance, leadership capability, cultural fit, compensation considerations, and risk factors. This should be the most thorough comparison possible.`;
  }

  /**
   * Generate a prompt for scenario-based comparison
   */
  static generateScenarioPrompt(candidates: Candidate[], job: Job, scenario: string): string {
    return `SCENARIO: "${scenario}"

${this.generateComparisonPrompt(candidates, job, 'scenario_based')}

CRITICAL: 
- Analyze how each candidate (by name) would specifically handle this scenario
- Base your analysis on their actual experience and demonstrated capabilities
- Be specific about why one candidate is better suited for this scenario than others`;
  }

  /**
   * Generate visualization data from comparison results
   */
  static generateVisualizationData(
    comparisonResults: any,
    candidateNameMap?: Record<string, string>,
  ): any {
    // Transform comparison results into chart-ready format
    const candidateAnalysis =
      comparisonResults.candidateAnalysis || comparisonResults.detailedComparison;

    // Helper to get candidate name
    const getCandidateName = (key: string) => {
      // Check if it's a UUID (candidate ID)
      const isUuid = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(key);

      if (isUuid && candidateNameMap?.[key]) {
        // It's an ID and we have a mapping
        return candidateNameMap[key];
      }

      // Otherwise, it's already a name or we don't have a mapping
      return key;
    };

    const radarChartData = {
      labels: ['Skills', 'Experience', 'Leadership', 'Cultural Fit', 'Availability'],
      datasets: Object.entries(candidateAnalysis).map(([key, analysis]: [string, any]) => ({
        label: getCandidateName(key),
        data: [
          analysis.scores?.skills || 0,
          analysis.scores?.experience || 0,
          analysis.scores?.leadership || 0,
          analysis.scores?.culturalFit || 0,
          analysis.scores?.availability || 0,
        ],
      })),
    };

    // Bar chart for overall scores
    const barChartData = {
      labels: Object.keys(candidateAnalysis).map((key) => getCandidateName(key)),
      datasets: [
        {
          label: 'Overall Score',
          data: Object.values(candidateAnalysis).map((analysis: any) => {
            const scores = analysis.scores || {};
            return (
              Object.values(scores).reduce((sum: number, score: any) => sum + score, 0) /
              Object.keys(scores).length
            );
          }),
        },
      ],
    };

    // Comparison matrix
    const comparisonMatrix = {
      headers: ['Candidate', 'Match Score', 'Key Strengths', 'Main Concerns', 'Unique Value'],
      rows: Object.entries(candidateAnalysis).map(([key, analysis]: [string, any]) => [
        getCandidateName(key),
        analysis.matchScore || 'N/A',
        analysis.strengths?.slice(0, 2).join(', ') || 'N/A',
        analysis.weaknesses?.slice(0, 2).join(', ') || 'N/A',
        analysis.keyDifferentiator || 'N/A',
      ]),
    };

    return {
      radarChartData,
      barChartData,
      comparisonMatrix,
    };
  }

  /**
   * Transform raw AI response into structured comparison results
   */
  static parseAIResponse(aiResponse: string): any {
    try {
      // Remove any markdown code block markers if present
      const cleanedResponse = aiResponse.replace(/```json\n?/g, '').replace(/```\n?/g, '');
      return JSON.parse(cleanedResponse);
    } catch (error) {
      console.error('Failed to parse AI response:', error);
      throw new Error('Failed to parse comparison results from AI');
    }
  }
}
