import { Column, Entity, Index } from 'typeorm';
import { BaseEntity } from '@shared/entities/base.entity';

export enum BountyType {
  PERCENTAGE = 'PERCENTAGE',
  FIXED = 'FIXED',
  TIERED = 'TIERED',
}

export interface TieredStructure {
  tiers: Array<{
    minHires: number;
    maxHires: number;
    value: number; // percentage or fixed based on bountyType
  }>;
}

@Entity('bounty_configurations')
@Index(['companyId', 'jobId', 'referralPartnerId'])
export class BountyConfiguration extends BaseEntity {
  @Column({ nullable: true })
  companyId?: string; // Company-specific config

  @Column({ nullable: true })
  jobId?: string; // Job-specific config

  @Column({ nullable: true })
  referralPartnerId?: string; // Partner-specific config

  @Column({
    type: 'enum',
    enum: BountyType,
    default: BountyType.PERCENTAGE,
  })
  bountyType!: BountyType;

  @Column({ type: 'decimal', precision: 5, scale: 2, nullable: true })
  percentageValue?: number; // e.g., 10.00 for 10%

  @Column({ type: 'decimal', precision: 10, scale: 2, nullable: true })
  fixedAmount?: number;

  @Column({ type: 'jsonb', nullable: true })
  tieredStructure?: TieredStructure;

  @Column({ default: true })
  isActive!: boolean;

  @Column({ type: 'int', nullable: true })
  priority?: number; // For config precedence
}
