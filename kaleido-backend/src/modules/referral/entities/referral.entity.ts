import { Column, Entity, Index, JoinColumn, ManyToOne } from 'typeorm';
import { BaseEntity } from '@shared/entities/base.entity';
import { ReferralPartner } from './referral-partner.entity';
import { Candidate } from '../../candidate/entities/candidate.entity';
import { Job } from '../../job/entities/job.entity';
import { Company } from '../../company/entities/company.entity';

export enum ReferralStatus {
  PENDING = 'PENDING',
  CANDIDATE_APPLIED = 'CANDIDATE_APPLIED',
  CANDIDATE_INTERVIEWED = 'CANDIDATE_INTERVIEWED',
  CANDIDATE_HIRED = 'CANDIDATE_HIRED',
  BOUNTY_APPROVED = 'BOUNTY_APPROVED',
  BOUNTY_PAID = 'BOUNTY_PAID',
  EXPIRED = 'EXPIRED',
  CANCELLED = 'CANCELLED',
}

export interface BountyCalculation {
  baseSalary?: number;
  percentage?: number;
  fixedAmount?: number;
  calculationType: 'PERCENTAGE' | 'FIXED' | 'TIERED';
}

export interface TrackingData {
  source: string;
  medium: string;
  campaign?: string;
  clickedAt: Date;
  ipAddress?: string;
  userAgent?: string;
}

@Entity('referrals')
@Index(['referralPartnerId', 'candidateId', 'jobId'])
@Index(['referralCode'])
@Index(['status'])
export class Referral extends BaseEntity {
  @Column({ type: 'uuid' })
  referralPartnerId!: string;

  @Column({ type: 'uuid' })
  candidateId!: string;

  @Column({ type: 'uuid' })
  jobId!: string;

  @Column({ type: 'uuid', nullable: true })
  companyId?: string;

  @Column()
  referralCode!: string; // The code used for this referral

  @Column({
    type: 'enum',
    enum: ReferralStatus,
    default: ReferralStatus.PENDING,
  })
  status!: ReferralStatus;

  @Column({ type: 'decimal', precision: 10, scale: 2, nullable: true })
  bountyAmount?: number;

  @Column({ type: 'jsonb', nullable: true })
  bountyCalculation?: BountyCalculation;

  @Column({ type: 'timestamp', nullable: true })
  candidateAppliedAt?: Date;

  @Column({ type: 'timestamp', nullable: true })
  candidateHiredAt?: Date;

  @Column({ type: 'timestamp', nullable: true })
  bountyPaidAt?: Date;

  @Column({ nullable: true })
  stripeTransferId?: string;

  @Column({ nullable: true })
  stripePayoutId?: string;

  @Column({ type: 'jsonb', nullable: true })
  trackingData?: TrackingData;

  // Relations
  @ManyToOne(() => ReferralPartner, (partner) => partner.referrals)
  @JoinColumn({ name: 'referralPartnerId' })
  referralPartner!: ReferralPartner;

  @ManyToOne(() => Candidate, { nullable: true })
  @JoinColumn({ name: 'candidateId' })
  candidate?: Candidate;

  @ManyToOne(() => Job, { nullable: true })
  @JoinColumn({ name: 'jobId' })
  job?: Job;

  @ManyToOne(() => Company, { nullable: true })
  @JoinColumn({ name: 'companyId' })
  company?: Company;
}
