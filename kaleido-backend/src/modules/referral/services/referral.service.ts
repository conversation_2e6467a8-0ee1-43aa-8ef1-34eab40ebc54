import { Injectable, NotFoundException, BadRequestException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Referral, ReferralStatus } from '../entities/referral.entity';
import { ReferralPartner } from '../entities/referral-partner.entity';
import { CreateReferralDto, UpdateReferralStatusDto } from '../dto';
import { ReferralPartnerService } from './referral-partner.service';
import { BountyCalculationService } from './bounty-calculation.service';
import { Candidate } from '../../candidate/entities/candidate.entity';
import { Job } from '../../job/entities/job.entity';
import { Company } from '../../company/entities/company.entity';

@Injectable()
export class ReferralService {
  constructor(
    @InjectRepository(Referral)
    private referralRepository: Repository<Referral>,
    @InjectRepository(Candidate)
    private candidateRepository: Repository<Candidate>,
    @InjectRepository(Job)
    private jobRepository: Repository<Job>,
    @InjectRepository(Company)
    private companyRepository: Repository<Company>,
    private referralPartnerService: ReferralPartnerService,
    private bountyCalculationService: BountyCalculationService,
  ) {}

  async create(createDto: CreateReferralDto): Promise<Referral> {
    // Verify referral partner exists
    const partner = await this.referralPartnerService.findByReferralCode(createDto.referralCode);
    if (!partner) {
      throw new BadRequestException('Invalid referral code');
    }

    if (!partner.isActive) {
      throw new BadRequestException('Referral partner is not active');
    }

    // Check if referral already exists for this candidate and job
    const existingReferral = await this.referralRepository.findOne({
      where: {
        candidateId: createDto.candidateId,
        jobId: createDto.jobId,
      },
    });

    if (existingReferral) {
      return existingReferral; // Return existing referral instead of creating duplicate
    }

    const referral = this.referralRepository.create({
      ...createDto,
      referralPartnerId: partner.id,
      status: ReferralStatus.PENDING,
      trackingData: {
        ...createDto.trackingData,
        clickedAt: new Date(),
      },
    });

    const savedReferral = await this.referralRepository.save(referral);

    // Update partner metrics
    await this.referralPartnerService.updateMetrics(partner.id);

    return savedReferral;
  }

  async findAll(filters: {
    referralPartnerId?: string;
    candidateId?: string;
    jobId?: string;
    status?: ReferralStatus;
  }): Promise<Referral[]> {
    const query = this.referralRepository
      .createQueryBuilder('referral')
      .leftJoinAndSelect('referral.candidate', 'candidate')
      .leftJoinAndSelect('referral.job', 'job')
      .leftJoinAndSelect('job.company', 'company');

    if (filters.referralPartnerId) {
      query.andWhere('referral.referralPartnerId = :referralPartnerId', {
        referralPartnerId: filters.referralPartnerId,
      });
    }

    if (filters.candidateId) {
      query.andWhere('referral.candidateId = :candidateId', {
        candidateId: filters.candidateId,
      });
    }

    if (filters.jobId) {
      query.andWhere('referral.jobId = :jobId', {
        jobId: filters.jobId,
      });
    }

    if (filters.status) {
      query.andWhere('referral.status = :status', {
        status: filters.status,
      });
    }

    query.orderBy('referral.createdAt', 'DESC');

    return query.getMany();
  }

  async findOne(id: string): Promise<Referral> {
    const referral = await this.referralRepository.findOne({
      where: { id },
      relations: ['referralPartner'],
    });

    if (!referral) {
      throw new NotFoundException(`Referral with ID ${id} not found`);
    }

    return referral;
  }

  async findByCandidateAndJob(candidateId: string, jobId: string): Promise<Referral | null> {
    return this.referralRepository.findOne({
      where: { candidateId, jobId },
    });
  }

  async updateStatus(id: string, updateDto: UpdateReferralStatusDto): Promise<Referral> {
    const referral = await this.findOne(id);
    const previousStatus = referral.status;

    referral.status = updateDto.status;

    // Update timestamps based on status
    switch (updateDto.status) {
      case ReferralStatus.CANDIDATE_APPLIED:
        referral.candidateAppliedAt = new Date();
        break;
      case ReferralStatus.CANDIDATE_HIRED:
        referral.candidateHiredAt = new Date();
        // Calculate bounty when candidate is hired
        const bountyDetails = await this.bountyCalculationService.calculateBounty(referral);
        referral.bountyAmount = bountyDetails.amount;
        referral.bountyCalculation = bountyDetails.calculation;
        break;
      case ReferralStatus.BOUNTY_APPROVED:
        if (updateDto.bountyAmount) {
          referral.bountyAmount = updateDto.bountyAmount;
        }
        if (updateDto.bountyCalculation) {
          referral.bountyCalculation = updateDto.bountyCalculation;
        }
        // Update partner's pending earnings
        await this.referralPartnerService.updateEarnings(
          referral.referralPartnerId,
          referral.bountyAmount || 0,
          'pending',
        );
        break;
      case ReferralStatus.BOUNTY_PAID:
        referral.bountyPaidAt = new Date();
        // Move earnings from pending to paid
        await this.referralPartnerService.updateEarnings(
          referral.referralPartnerId,
          referral.bountyAmount || 0,
          'paid',
        );
        break;
    }

    const updatedReferral = await this.referralRepository.save(referral);

    // Update partner metrics if status changed
    if (previousStatus !== updateDto.status) {
      await this.referralPartnerService.updateMetrics(referral.referralPartnerId);
    }

    return updatedReferral;
  }

  async trackClick(referralCode: string, trackingData: any): Promise<void> {
    const partner = await this.referralPartnerService.findByReferralCode(referralCode);
    if (!partner || !partner.isActive) {
      throw new BadRequestException('Invalid or inactive referral code');
    }

    // You can log this click or create a separate tracking entity if needed
    console.log(`Referral click tracked for code: ${referralCode}`, trackingData);
  }

  async getPartnerReferrals(partnerId: string): Promise<Referral[]> {
    return this.referralRepository.find({
      where: { referralPartnerId: partnerId },
      order: { createdAt: 'DESC' },
    });
  }

  async getPartnerEarnings(partnerId: string): Promise<{
    totalEarnings: number;
    pendingEarnings: number;
    paidEarnings: number;
    referrals: Array<{
      id: string;
      candidateName: string;
      jobTitle: string;
      status: string;
      bountyAmount: number | null;
      createdAt: Date;
    }>;
  }> {
    const partner = await this.referralPartnerService.findOne(partnerId);
    const referrals = await this.getPartnerReferrals(partnerId);

    // TODO: Fetch candidate and job details from their respective services
    const referralDetails = referrals.map((r) => ({
      id: r.id,
      candidateName: 'Candidate Name', // Replace with actual candidate name
      jobTitle: 'Job Title', // Replace with actual job title
      status: r.status as string,
      bountyAmount: r.bountyAmount ?? null,
      createdAt: r.createdAt,
    }));

    return {
      totalEarnings: Number(partner.totalEarnings),
      pendingEarnings: Number(partner.pendingEarnings),
      paidEarnings: Number(partner.paidEarnings),
      referrals: referralDetails,
    };
  }
}
