import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { BountyConfiguration, BountyType } from '../entities/bounty-configuration.entity';
import { Referral } from '../entities/referral.entity';
import { ReferralPartnerService } from './referral-partner.service';

interface BountyCalculationResult {
  amount: number;
  calculation: {
    baseSalary?: number;
    percentage?: number;
    fixedAmount?: number;
    calculationType: 'PERCENTAGE' | 'FIXED' | 'TIERED';
  };
}

@Injectable()
export class BountyCalculationService {
  constructor(
    @InjectRepository(BountyConfiguration)
    private bountyConfigRepository: Repository<BountyConfiguration>,
    private referralPartnerService: ReferralPartnerService,
  ) {}

  async calculateBounty(referral: Referral): Promise<BountyCalculationResult> {
    // Get the most specific configuration (priority: Job > Company > Partner > System default)
    const config = await this.getBountyConfiguration(
      referral.jobId,
      referral.companyId,
      referral.referralPartnerId,
    );

    // TODO: Get job salary from job service
    const jobSalary = 100000; // Placeholder - should fetch from job service

    if (!config) {
      // Default configuration if none found
      return {
        amount: jobSalary * 0.1, // 10% default
        calculation: {
          baseSalary: jobSalary,
          percentage: 10,
          calculationType: 'PERCENTAGE',
        },
      };
    }

    switch (config.bountyType) {
      case BountyType.PERCENTAGE:
        return this.calculatePercentageBounty(jobSalary, config);

      case BountyType.FIXED:
        return this.calculateFixedBounty(config);

      case BountyType.TIERED:
        return this.calculateTieredBounty(referral.referralPartnerId, jobSalary, config);

      default:
        throw new Error(`Unknown bounty type: ${config.bountyType}`);
    }
  }

  private calculatePercentageBounty(
    salary: number,
    config: BountyConfiguration,
  ): BountyCalculationResult {
    const percentage = config.percentageValue || 10;
    const amount = salary * (percentage / 100);

    return {
      amount,
      calculation: {
        baseSalary: salary,
        percentage,
        calculationType: 'PERCENTAGE',
      },
    };
  }

  private calculateFixedBounty(config: BountyConfiguration): BountyCalculationResult {
    const amount = config.fixedAmount || 0;

    return {
      amount,
      calculation: {
        fixedAmount: amount,
        calculationType: 'FIXED',
      },
    };
  }

  private async calculateTieredBounty(
    referralPartnerId: string,
    salary: number,
    config: BountyConfiguration,
  ): Promise<BountyCalculationResult> {
    // Get partner's successful hire count
    const partner = await this.referralPartnerService.findOne(referralPartnerId);
    const successfulHires = partner.dashboardMetrics?.successfulPlacements || 0;

    if (!config.tieredStructure?.tiers) {
      throw new Error('Tiered configuration missing tier structure');
    }

    // Find applicable tier
    const tier = config.tieredStructure.tiers.find(
      (t) => successfulHires >= t.minHires && successfulHires <= t.maxHires,
    );

    if (!tier) {
      throw new Error('No applicable tier found for hire count');
    }

    const amount = salary * (tier.value / 100);

    return {
      amount,
      calculation: {
        baseSalary: salary,
        percentage: tier.value,
        calculationType: 'TIERED',
      },
    };
  }

  async getBountyConfiguration(
    jobId?: string,
    companyId?: string,
    referralPartnerId?: string,
  ): Promise<BountyConfiguration | null> {
    const query = this.bountyConfigRepository
      .createQueryBuilder('config')
      .where('config.isActive = :isActive', { isActive: true });

    // Build OR conditions for different config levels
    const conditions: string[] = [];
    const parameters: any = {};

    if (jobId) {
      conditions.push('config.jobId = :jobId');
      parameters.jobId = jobId;
    }

    if (companyId) {
      conditions.push('(config.companyId = :companyId AND config.jobId IS NULL)');
      parameters.companyId = companyId;
    }

    if (referralPartnerId) {
      conditions.push(
        '(config.referralPartnerId = :referralPartnerId AND config.jobId IS NULL AND config.companyId IS NULL)',
      );
      parameters.referralPartnerId = referralPartnerId;
    }

    // Add system default condition
    conditions.push(
      '(config.jobId IS NULL AND config.companyId IS NULL AND config.referralPartnerId IS NULL)',
    );

    if (conditions.length > 0) {
      query.andWhere(`(${conditions.join(' OR ')})`, parameters);
    }

    // Order by priority and specificity
    query
      .orderBy('config.priority', 'DESC')
      .addOrderBy(
        'CASE WHEN config.jobId IS NOT NULL THEN 3 WHEN config.companyId IS NOT NULL THEN 2 WHEN config.referralPartnerId IS NOT NULL THEN 1 ELSE 0 END',
        'DESC',
      );

    return query.getOne();
  }

  async createConfiguration(config: Partial<BountyConfiguration>): Promise<BountyConfiguration> {
    const configuration = this.bountyConfigRepository.create({
      ...config,
      isActive: true,
    });

    return this.bountyConfigRepository.save(configuration);
  }

  async updateConfiguration(
    id: string,
    updates: Partial<BountyConfiguration>,
  ): Promise<BountyConfiguration> {
    await this.bountyConfigRepository.update(id, updates);
    return this.bountyConfigRepository.findOneOrFail({ where: { id } });
  }

  async deactivateConfiguration(id: string): Promise<void> {
    await this.bountyConfigRepository.update(id, { isActive: false });
  }
}
