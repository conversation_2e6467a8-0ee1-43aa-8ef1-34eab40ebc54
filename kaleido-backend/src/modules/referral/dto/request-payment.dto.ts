import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsNumber, IsOptional, IsString, Min, IsEnum } from 'class-validator';

export enum PaymentMethod {
  BANK_TRANSFER = 'bank_transfer',
  PAYPAL = 'paypal',
  CHECK = 'check',
}

export class RequestPaymentDto {
  @ApiProperty({
    description: 'Amount to request for payment',
    example: 1000.0,
    minimum: 0.01,
  })
  @IsNumber()
  @Min(0.01)
  amount!: number;

  @ApiPropertyOptional({
    description: 'Preferred payment method',
    enum: PaymentMethod,
    default: PaymentMethod.BANK_TRANSFER,
  })
  @IsOptional()
  @IsEnum(PaymentMethod)
  paymentMethod?: PaymentMethod = PaymentMethod.BANK_TRANSFER;

  @ApiPropertyOptional({
    description: 'Additional notes for the payment request',
    example: 'Payment for Q1 2024 referrals',
  })
  @IsOptional()
  @IsString()
  notes?: string;
}
