import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsNotEmpty, IsOptional, IsString, IsObject } from 'class-validator';

export class CreateReferralDto {
  @ApiProperty({ description: 'Referral code used' })
  @IsNotEmpty()
  @IsString()
  referralCode!: string;

  @ApiProperty({ description: 'Candidate ID' })
  @IsNotEmpty()
  @IsString()
  candidateId!: string;

  @ApiProperty({ description: 'Job ID' })
  @IsNotEmpty()
  @IsString()
  jobId!: string;

  @ApiPropertyOptional({
    description: 'Tracking data',
    example: {
      source: 'email',
      medium: 'campaign',
      campaign: 'summer-2024',
    },
  })
  @IsOptional()
  @IsObject()
  trackingData?: {
    source: string;
    medium: string;
    campaign?: string;
    clickedAt: Date;
    ipAddress?: string;
    userAgent?: string;
  };
}
