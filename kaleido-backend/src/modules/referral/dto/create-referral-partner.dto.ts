import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsEmail, IsNotEmpty, IsOptional, IsString, IsObject, IsNumber } from 'class-validator';

export class CreateReferralPartnerDto {
  @ApiProperty({ description: 'Name of the referral partner' })
  @IsNotEmpty()
  @IsString()
  partnerName!: string;

  @ApiProperty({ description: 'Contact email for the referral partner' })
  @IsNotEmpty()
  @IsEmail()
  contactEmail!: string;

  @ApiPropertyOptional({ description: 'Contact phone number' })
  @IsOptional()
  @IsString()
  contactPhone?: string;

  @ApiPropertyOptional({ description: 'Company ID if partner is company-specific' })
  @IsOptional()
  @IsString()
  companyId?: string;

  @ApiPropertyOptional({
    description: 'Partner settings',
    example: {
      defaultBountyPercentage: 10,
      paymentPreferences: {
        method: 'bank_transfer',
        details: {},
      },
    },
  })
  @IsOptional()
  @IsObject()
  settings?: {
    defaultBountyPercentage?: number;
    customBountyRules?: any[];
    paymentPreferences?: {
      method: string;
      details: any;
    };
  };
}
