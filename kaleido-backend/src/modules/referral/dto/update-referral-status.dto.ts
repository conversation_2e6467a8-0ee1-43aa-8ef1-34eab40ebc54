import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsEnum, IsNotEmpty, IsOptional, IsNumber, IsObject } from 'class-validator';
import { ReferralStatus } from '../entities/referral.entity';

export class UpdateReferralStatusDto {
  @ApiProperty({ enum: ReferralStatus, description: 'New status for the referral' })
  @IsNotEmpty()
  @IsEnum(ReferralStatus)
  status!: ReferralStatus;

  @ApiPropertyOptional({ description: 'Bounty amount if status is BOUNTY_APPROVED' })
  @IsOptional()
  @IsNumber()
  bountyAmount?: number;

  @ApiPropertyOptional({
    description: 'Bounty calculation details',
    example: {
      baseSalary: 100000,
      percentage: 10,
      calculationType: 'PERCENTAGE',
    },
  })
  @IsOptional()
  @IsObject()
  bountyCalculation?: {
    baseSalary?: number;
    percentage?: number;
    fixedAmount?: number;
    calculationType: 'PERCENTAGE' | 'FIXED' | 'TIERED';
  };
}
