import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import {
  IsEnum,
  IsNotEmpty,
  IsN<PERSON>ber,
  IsOptional,
  IsString,
  IsObject,
  Min,
  Max,
} from 'class-validator';
import { BountyType } from '../entities/bounty-configuration.entity';

export class CreateBountyConfigurationDto {
  @ApiPropertyOptional({ description: 'Company ID for company-specific configuration' })
  @IsOptional()
  @IsString()
  companyId?: string;

  @ApiPropertyOptional({ description: 'Job ID for job-specific configuration' })
  @IsOptional()
  @IsString()
  jobId?: string;

  @ApiPropertyOptional({ description: 'Referral Partner ID for partner-specific configuration' })
  @IsOptional()
  @IsString()
  referralPartnerId?: string;

  @ApiProperty({ enum: BountyType, description: 'Type of bounty calculation' })
  @IsNotEmpty()
  @IsEnum(BountyType)
  bountyType!: BountyType;

  @ApiPropertyOptional({ description: 'Percentage value (0-100) for percentage-based bounties' })
  @IsOptional()
  @IsNumber()
  @Min(0)
  @Max(100)
  percentageValue?: number;

  @ApiPropertyOptional({ description: 'Fixed amount for fixed bounties' })
  @IsOptional()
  @IsNumber()
  @Min(0)
  fixedAmount?: number;

  @ApiPropertyOptional({
    description: 'Tiered structure for tiered bounties',
    example: {
      tiers: [
        { minHires: 0, maxHires: 5, value: 5 },
        { minHires: 6, maxHires: 10, value: 7.5 },
        { minHires: 11, maxHires: 999, value: 10 },
      ],
    },
  })
  @IsOptional()
  @IsObject()
  tieredStructure?: {
    tiers: Array<{
      minHires: number;
      maxHires: number;
      value: number;
    }>;
  };

  @ApiPropertyOptional({ description: 'Priority for configuration precedence' })
  @IsOptional()
  @IsNumber()
  @Min(0)
  priority?: number;
}
