import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsString, IsNotEmpty, IsOptional, IsObject } from 'class-validator';

export class TrackReferralDto {
  @ApiProperty({
    description: 'The referral code from the referral partner',
    example: 'REF123ABC',
  })
  @IsString()
  @IsNotEmpty()
  referralCode!: string;

  @ApiProperty({
    description: 'The ID of the job being referred to',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  @IsString()
  @IsNotEmpty()
  jobId!: string;

  @ApiPropertyOptional({
    description: 'The ID of the candidate being referred (if known)',
    example: '123e4567-e89b-12d3-a456-426614174001',
  })
  @IsOptional()
  @IsString()
  candidateId?: string;

  @ApiPropertyOptional({
    description: 'UTM source parameter',
    example: 'linkedin',
  })
  @IsOptional()
  @IsString()
  source?: string;

  @ApiPropertyOptional({
    description: 'UTM medium parameter',
    example: 'social',
  })
  @IsOptional()
  @IsString()
  medium?: string;

  @ApiPropertyOptional({
    description: 'UTM campaign parameter',
    example: 'summer-hiring-2024',
  })
  @IsOptional()
  @IsString()
  campaign?: string;

  @ApiPropertyOptional({
    description: 'Additional tracking data',
    example: {
      ip: '***********',
      userAgent: 'Mozilla/5.0...',
      timestamp: '2024-01-01T00:00:00Z',
    },
  })
  @IsOptional()
  @IsObject()
  trackingData?: Record<string, any>;
}
