import { Controller, Post, Body, HttpCode, HttpStatus, Headers, Req } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { ReferralPartnerService } from '../services/referral-partner.service';
import { CreateReferralPartnerDto } from '../dto';
import { ReferralPartner } from '../entities/referral-partner.entity';
import { Request } from 'express';

@ApiTags('Referral Partners - Public')
@Controller('referral-partners/public')
export class ReferralPartnerPublicController {
  constructor(private readonly referralPartnerService: ReferralPartnerService) {}

  @Post('register')
  @HttpCode(HttpStatus.CREATED)
  @ApiOperation({ summary: 'Register as a new referral partner' })
  @ApiResponse({ status: HttpStatus.CREATED, type: ReferralPartner })
  async register(
    @Body() body: any,
    @Headers('x-client-id') clientId: string,
    @Req() req: Request,
  ): Promise<ReferralPartner> {
    // Map frontend data to DTO format
    const createDto: CreateReferralPartnerDto = {
      partnerName: body.organizationName || body.partnerName,
      contactEmail: body.email || body.contactEmail,
      contactPhone: body.phoneNumber || body.contactPhone,
      settings: {
        defaultBountyPercentage: 10, // Default percentage
        customBountyRules: [],
        paymentPreferences: {
          method: 'bank_transfer',
          details: {
            contactName: body.contactName,
            website: body.website,
            description: body.description,
          },
        },
      },
    };

    // Pass the clientId separately as it's not part of the DTO
    return this.referralPartnerService.create(createDto, clientId || body.clientId);
  }

  @Post('check')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'Check if user is already a referral partner' })
  @ApiResponse({ status: HttpStatus.OK, type: Boolean })
  async checkExisting(
    @Headers('x-client-id') clientId: string,
  ): Promise<{ exists: boolean; partner?: ReferralPartner }> {
    const partner = await this.referralPartnerService.findByClientId(clientId);
    return {
      exists: !!partner,
      partner: partner || undefined,
    };
  }
}
