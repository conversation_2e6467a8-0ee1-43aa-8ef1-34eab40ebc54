import {
  Controller,
  Get,
  Post,
  Param,
  Query,
  Body,
  HttpCode,
  HttpStatus,
  Req,
  Res,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { Request, Response } from 'express';
import { ReferralService } from '../services/referral.service';
import { ReferralPartnerService } from '../services/referral-partner.service';

@ApiTags('Public Referral')
@Controller('api')
export class ReferralPublicController {
  constructor(
    private readonly referralService: ReferralService,
    private readonly referralPartnerService: ReferralPartnerService,
  ) {}

  @Get('r/:referralCode')
  @ApiOperation({ summary: 'Track referral click and redirect' })
  async trackAndRedirect(
    @Param('referralCode') referralCode: string,
    @Req() req: Request,
    @Res() res: Response,
    @Query('job') jobId?: string,
    @Query('redirect') redirectUrl?: string,
  ): Promise<void> {
    try {
      // Track the click
      await this.referralService.trackClick(referralCode, {
        source: 'direct',
        medium: 'referral',
        ipAddress: (req as any).ip || '',
        userAgent: (req as any).headers?.['user-agent'] || '',
        jobId,
      });

      // Determine redirect URL
      let targetUrl = '/open-jobs';
      if (jobId) {
        targetUrl = `/open-jobs/${jobId}?ref=${referralCode}`;
      } else if (redirectUrl) {
        targetUrl = redirectUrl + `?ref=${referralCode}`;
      }

      // Set referral cookie for tracking
      (res as any).cookie('referral_code', referralCode, {
        maxAge: 30 * 24 * 60 * 60 * 1000, // 30 days
        httpOnly: true,
        secure: process.env.NODE_ENV === 'production',
      });

      // Redirect to target URL
      (res as any).redirect(targetUrl);
    } catch (error) {
      // If error, still redirect but without tracking
      (res as any).redirect('/open-jobs');
    }
  }

  @Post('referrals/capture')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'Capture referral data from frontend' })
  async captureReferral(
    @Body()
    data: {
      referralCode: string;
      source?: string;
      medium?: string;
      campaign?: string;
    },
    @Req() req: Request,
  ): Promise<{ success: boolean }> {
    try {
      await this.referralService.trackClick(data.referralCode, {
        source: data.source || 'website',
        medium: data.medium || 'organic',
        campaign: data.campaign,
        ipAddress: (req as any).ip || '',
        userAgent: (req as any).headers?.['user-agent'] || '',
      });

      return { success: true };
    } catch (error) {
      return { success: false };
    }
  }
}
