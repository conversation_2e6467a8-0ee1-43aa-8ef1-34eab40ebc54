import { Module, forwardRef } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ReferralPartner, Referral, BountyConfiguration } from './entities';
import { ReferralPartnerService, ReferralService, BountyCalculationService } from './services';
import {
  ReferralPartnerController,
  ReferralController,
  ReferralEarningsController,
  ReferralPublicController,
  ReferralPartnerPublicController,
} from './controllers';
import { AuthModule } from '../../auth/auth.module';
import { RolesModule } from '../roles/roles.module';
import { Job } from '../job/entities/job.entity';
import { Candidate } from '../candidate/entities/candidate.entity';
import { Company } from '../company/entities/company.entity';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      ReferralPartner,
      Referral,
      BountyConfiguration,
      Job,
      Candidate,
      Company,
    ]),
    forwardRef(() => AuthModule),
    forwardRef(() => RolesModule),
  ],
  controllers: [
    ReferralPartnerController,
    ReferralPartnerPublicController,
    ReferralController,
    ReferralEarningsController,
    ReferralPublicController,
  ],
  providers: [ReferralPartnerService, ReferralService, BountyCalculationService],
  exports: [ReferralPartnerService, ReferralService, BountyCalculationService],
})
export class ReferralModule {}
