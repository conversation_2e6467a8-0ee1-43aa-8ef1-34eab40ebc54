import { Controller, Post, Body, Headers, HttpException, HttpStatus, Req } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { FastifyRequest } from 'fastify';
import { StripeService } from '../services/stripe.service';
import { StripeConnectService } from '../services/stripe-connect.service';
import { Public } from '../../../auth/public.decorator';
import Stripe from 'stripe';

interface RawBodyRequest extends FastifyRequest {
  rawBody?: string | Buffer;
}

@Controller('webhooks/stripe')
export class StripeWebhookController {
  private stripe: Stripe;
  private webhookSecret: string;

  constructor(
    private readonly stripeConnectService: StripeConnectService,
    private readonly stripeService: StripeService,
    private readonly configService: ConfigService,
  ) {
    // Use the stripe instance from StripeService instead of creating a new one
    this.stripe = this.stripeService.getStripeInstance();
    this.webhookSecret = this.configService.get<string>('STRIPE_WEBHOOK_SECRET') || '';
  }

  @Post()
  @Public()
  async handleWebhook(
    @Headers('stripe-signature') signature: string,
    @Req() request: RawBodyRequest,
  ) {
    if (!signature) {
      throw new HttpException('No signature provided', HttpStatus.BAD_REQUEST);
    }

    let event: Stripe.Event;

    try {
      const rawBody = request.rawBody;
      if (!rawBody) {
        throw new Error('Raw body is missing');
      }

      event = this.stripe.webhooks.constructEvent(rawBody, signature, this.webhookSecret);
    } catch (err) {
      console.error('Webhook signature verification failed:', err);
      throw new HttpException(`Webhook Error: ${err.message}`, HttpStatus.BAD_REQUEST);
    }

    try {
      switch (event.type) {
        case 'account.updated':
          await this.handleAccountUpdated(event.data.object as Stripe.Account);
          break;

        case 'account.application.deauthorized':
          // Handle account deauthorization
          const accountId = (event.data.object as any).account;
          if (accountId) {
            await this.stripeConnectService.updateAccountStatus(accountId, 'disabled');
          }
          break;

        case 'transfer.created':
          await this.handleTransferCreated(event.data.object as Stripe.Transfer);
          break;

        // Note: transfer.failed is not in the current Stripe API version
        // Handle transfer errors through other events

        case 'payout.paid':
          await this.handlePayoutPaid(event.data.object as Stripe.Payout);
          break;

        case 'payout.failed':
          await this.handlePayoutFailed(event.data.object as Stripe.Payout);
          break;

        default:
          console.log(`Unhandled event type: ${event.type}`);
      }
    } catch (error) {
      console.error(`Error processing webhook event ${event.type}:`, error);
      throw new HttpException('Webhook processing failed', HttpStatus.INTERNAL_SERVER_ERROR);
    }

    return { received: true };
  }

  private async handleAccountUpdated(account: Stripe.Account) {
    const { id, charges_enabled, payouts_enabled, details_submitted } = account;

    let status: 'pending' | 'active' | 'restricted' | 'disabled' = 'pending';

    if (charges_enabled && payouts_enabled && details_submitted) {
      status = 'active';
    } else if (details_submitted && (!charges_enabled || !payouts_enabled)) {
      status = 'restricted';
    } else if (!details_submitted) {
      status = 'pending';
    }

    await this.stripeConnectService.updateAccountStatus(id, status);
  }

  private async handleTransferCreated(transfer: Stripe.Transfer) {
    // Update referral with transfer ID
    if (transfer.metadata?.referralId) {
      await this.stripeConnectService.updateReferralTransfer(
        transfer.metadata.referralId,
        transfer.id,
      );
    }
  }

  private async handlePayoutPaid(payout: Stripe.Payout) {
    // Update referral partner's paid earnings
    console.log('Payout successful:', payout.id);
  }

  private async handlePayoutFailed(payout: Stripe.Payout) {
    // Handle failed payout
    console.error('Payout failed:', payout.id, payout.failure_message);
  }
}
