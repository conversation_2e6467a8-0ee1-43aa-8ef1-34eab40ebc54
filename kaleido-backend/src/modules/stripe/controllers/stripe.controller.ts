import {
  Controller,
  Post,
  Get,
  Body,
  Param,
  UseGuards,
  Req,
  Headers,
  BadRequestException,
} from '@nestjs/common';
import { ApiBearerAuth, ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';
import { Auth0Guard } from '@/auth/auth.guard';
import { Roles } from '@/shared/decorators/roles.decorator';
import { UserRole } from '@/common/enums/role.enum';
import { StripeConnectService } from '../services/stripe-connect.service';
import { StripeService } from '../services/stripe.service';
import { ConfigService } from '@nestjs/config';
import Stripe from 'stripe';

@ApiTags('Stripe')
@Controller('stripe')
export class StripeController {
  private readonly webhookSecret: string;

  constructor(
    private readonly stripeConnectService: StripeConnectService,
    private readonly stripeService: StripeService,
    private readonly configService: ConfigService,
  ) {
    this.webhookSecret = this.configService.get<string>('STRIPE_WEBHOOK_SECRET') || '';
  }

  @Post('webhook')
  @ApiOperation({ summary: 'Handle Stripe webhooks' })
  async handleWebhook(
    @Headers('stripe-signature') signature: string,
    @Req() request: any,
  ): Promise<{ received: boolean }> {
    if (!signature || !this.webhookSecret) {
      throw new BadRequestException('No signature or webhook secret');
    }

    let event: Stripe.Event;

    try {
      const stripe = this.stripeService.getStripeInstance();
      event = stripe.webhooks.constructEvent(request.rawBody, signature, this.webhookSecret);
    } catch (err) {
      console.error('Webhook signature verification failed:', err);
      throw new BadRequestException('Webhook signature verification failed');
    }

    await this.stripeConnectService.handleWebhook(event);

    return { received: true };
  }
}

@ApiTags('Referral Partners - Stripe')
@Controller('referral-partners/:partnerId/stripe')
@UseGuards(Auth0Guard)
@ApiBearerAuth()
export class ReferralPartnerStripeController {
  constructor(private readonly stripeConnectService: StripeConnectService) {}

  @Post('connect')
  @Roles(UserRole.REFERRAL_PARTNER, UserRole.ADMIN)
  @ApiOperation({ summary: 'Create Stripe Connect account for referral partner' })
  @ApiResponse({
    status: 201,
    description: 'Returns account ID and onboarding URL',
    schema: {
      properties: {
        accountId: { type: 'string' },
        url: { type: 'string' },
      },
    },
  })
  async createConnectAccount(@Param('partnerId') partnerId: string) {
    const result = await this.stripeConnectService.createExpressAccount(partnerId);
    return {
      accountId: result.accountId,
      url: result.onboardingUrl,
    };
  }

  @Get('status')
  @Roles(UserRole.REFERRAL_PARTNER, UserRole.ADMIN)
  @ApiOperation({ summary: 'Get Stripe account status' })
  @ApiResponse({
    status: 200,
    description: 'Returns Stripe account connection status',
    schema: {
      properties: {
        connected: { type: 'boolean' },
        accountId: { type: 'string', nullable: true },
        status: { type: 'string', nullable: true },
        payoutsEnabled: { type: 'boolean', nullable: true },
        chargesEnabled: { type: 'boolean', nullable: true },
      },
    },
  })
  async getAccountStatus(@Param('partnerId') partnerId: string) {
    return await this.stripeConnectService.getAccountStatus(partnerId);
  }

  @Post('account-link')
  @Roles(UserRole.REFERRAL_PARTNER, UserRole.ADMIN)
  @ApiOperation({ summary: 'Create new account link for onboarding' })
  @ApiResponse({
    status: 201,
    description: 'Returns new account link URL',
    schema: {
      properties: {
        url: { type: 'string' },
      },
    },
  })
  async createAccountLink(
    @Param('partnerId') partnerId: string,
    @Body('accountId') accountId: string,
  ) {
    const link = await this.stripeConnectService.createAccountLink(accountId, partnerId);
    return { url: link.url };
  }

  @Post('payout')
  @Roles(UserRole.REFERRAL_PARTNER, UserRole.ADMIN)
  @ApiOperation({ summary: 'Create payout to referral partner' })
  @ApiResponse({
    status: 201,
    description: 'Payout created successfully',
    schema: {
      properties: {
        transferId: { type: 'string' },
        amount: { type: 'number' },
        currency: { type: 'string' },
        status: { type: 'string' },
      },
    },
  })
  async createPayout(@Param('partnerId') partnerId: string, @Body('amount') amount: number) {
    const transfer = await this.stripeConnectService.createPayout(partnerId, amount);
    return {
      transferId: transfer.id,
      amount: transfer.amount / 100, // Convert from cents
      currency: transfer.currency,
      status: 'created',
    };
  }

  @Get('balance')
  @Roles(UserRole.REFERRAL_PARTNER, UserRole.ADMIN)
  @ApiOperation({ summary: 'Get referral partner Stripe balance' })
  @ApiResponse({
    status: 200,
    description: 'Returns Stripe account balance',
    schema: {
      properties: {
        available: { type: 'array', items: { type: 'object' } },
        pending: { type: 'array', items: { type: 'object' } },
      },
    },
  })
  async getBalance(@Param('partnerId') partnerId: string) {
    const balance = await this.stripeConnectService.getBalance(partnerId);
    if (!balance) {
      return { available: [], pending: [] };
    }
    return balance;
  }
}
