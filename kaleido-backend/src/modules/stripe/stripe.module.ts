import { Module } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { TypeOrmModule } from '@nestjs/typeorm';
import { StripeService } from './services/stripe.service';
import { StripeConnectService } from './services/stripe-connect.service';
import { StripeController, ReferralPartnerStripeController } from './controllers/stripe.controller';
import { StripeWebhookController } from './controllers/stripe-webhook.controller';
import { ReferralPartner } from '../referral/entities/referral-partner.entity';
import { Referral } from '../referral/entities/referral.entity';

@Module({
  imports: [ConfigModule.forRoot(), TypeOrmModule.forFeature([ReferralPartner, Referral])],
  providers: [
    StripeService,
    StripeConnectService,
    {
      provide: 'STRIPE_API_KEY',
      useFactory: (configService: ConfigService) =>
        configService.get<string>('STRIPE_API_KEY') ||
        configService.get<string>('STRIPE_SECRET_KEY'),
      inject: [ConfigService],
    },
  ],
  controllers: [StripeController, ReferralPartnerStripeController, StripeWebhookController],
  exports: [StripeService, StripeConnectService],
})
export class StripeModule {}
