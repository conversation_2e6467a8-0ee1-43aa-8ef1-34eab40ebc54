import { Injectable, Inject } from '@nestjs/common';
import Strip<PERSON> from 'stripe';

@Injectable()
export class StripeService {
  private stripe: Stripe;

  constructor(@Inject('STRIPE_API_KEY') private apiKey: string) {
    this.stripe = new Stripe(this.apiKey || 'sk_test_dummy', {
      apiVersion: '2025-04-30.basil',
    });
  }

  getStripeInstance(): Stripe {
    return this.stripe;
  }

  async createCustomer(email: string, metadata?: Record<string, string>): Promise<Stripe.Customer> {
    return await this.stripe.customers.create({
      email,
      metadata,
    });
  }

  async createPaymentIntent(
    amount: number,
    currency: string = 'usd',
    metadata?: Record<string, string>,
  ): Promise<Stripe.PaymentIntent> {
    return await this.stripe.paymentIntents.create({
      amount: Math.round(amount * 100), // Convert to cents
      currency,
      metadata,
    });
  }

  async createTransfer(
    amount: number,
    destination: string,
    metadata?: Record<string, string>,
  ): Promise<Stripe.Transfer> {
    return await this.stripe.transfers.create({
      amount: Math.round(amount * 100), // Convert to cents
      currency: 'usd',
      destination,
      metadata,
    });
  }

  async createPayout(
    amount: number,
    stripeAccountId: string,
    metadata?: Record<string, string>,
  ): Promise<Stripe.Payout> {
    return await this.stripe.payouts.create(
      {
        amount: Math.round(amount * 100), // Convert to cents
        currency: 'usd',
        metadata,
      },
      {
        stripeAccount: stripeAccountId,
      },
    );
  }

  async retrieveAccount(accountId: string): Promise<Stripe.Account> {
    return await this.stripe.accounts.retrieve(accountId);
  }

  async retrieveBalance(stripeAccountId: string): Promise<Stripe.Balance> {
    return await this.stripe.balance.retrieve({
      stripeAccount: stripeAccountId,
    });
  }
}
