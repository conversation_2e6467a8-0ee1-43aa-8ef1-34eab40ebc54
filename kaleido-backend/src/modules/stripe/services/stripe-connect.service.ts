import { Injectable, BadRequestException, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { ConfigService } from '@nestjs/config';
import { StripeService } from './stripe.service';
import { ReferralPartner } from '../../referral/entities/referral-partner.entity';
import { Referral } from '../../referral/entities/referral.entity';
import Stripe from 'stripe';

@Injectable()
export class StripeConnectService {
  private readonly frontendUrl: string;

  constructor(
    private stripeService: StripeService,
    private configService: ConfigService,
    @InjectRepository(ReferralPartner)
    private referralPartnerRepository: Repository<ReferralPartner>,
    @InjectRepository(Referral)
    private referralRepository: Repository<Referral>,
  ) {
    this.frontendUrl = this.configService.get<string>('FRONTEND_URL') || 'http://localhost:3000';
  }

  async createExpressAccount(
    partnerId: string,
  ): Promise<{ accountId: string; onboardingUrl: string }> {
    const partner = await this.referralPartnerRepository.findOne({
      where: { id: partnerId },
    });

    if (!partner) {
      throw new NotFoundException('Referral partner not found');
    }

    if (partner.stripeAccountId) {
      // Account already exists, create a new onboarding link
      const accountLink = await this.createAccountLink(partner.stripeAccountId, partnerId);
      return {
        accountId: partner.stripeAccountId,
        onboardingUrl: accountLink.url,
      };
    }

    try {
      const stripe = this.stripeService.getStripeInstance();

      // Create Express account
      const account = await stripe.accounts.create({
        type: 'express',
        email: partner.contactEmail,
        capabilities: {
          transfers: { requested: true },
        },
        business_profile: {
          name: partner.partnerName,
        },
        metadata: {
          partnerId: partner.id,
          referralCode: partner.referralCode,
        },
      });

      // Update partner with Stripe account ID
      partner.stripeAccountId = account.id;
      partner.stripeAccountStatus = 'pending';
      await this.referralPartnerRepository.save(partner);

      // Create account link for onboarding
      const accountLink = await this.createAccountLink(account.id, partnerId);

      return {
        accountId: account.id,
        onboardingUrl: accountLink.url,
      };
    } catch (error) {
      console.error('Error creating Stripe Express account:', error);
      throw new BadRequestException('Failed to create Stripe account');
    }
  }

  async createAccountLink(accountId: string, partnerId: string): Promise<Stripe.AccountLink> {
    const stripe = this.stripeService.getStripeInstance();

    const refreshUrl = `${this.frontendUrl}/referral-partner/earnings?tab=earnings&stripe=retry`;
    const returnUrl = `${this.frontendUrl}/referral-partner/earnings?tab=earnings&stripe=success`;

    return await stripe.accountLinks.create({
      account: accountId,
      refresh_url: refreshUrl,
      return_url: returnUrl,
      type: 'account_onboarding',
    });
  }

  async getAccountStatus(partnerId: string): Promise<{
    connected: boolean;
    accountId?: string;
    status?: string;
    payoutsEnabled?: boolean;
    chargesEnabled?: boolean;
  }> {
    const partner = await this.referralPartnerRepository.findOne({
      where: { id: partnerId },
    });

    if (!partner || !partner.stripeAccountId) {
      return { connected: false };
    }

    try {
      const account = await this.stripeService.retrieveAccount(partner.stripeAccountId);

      // Update partner status based on Stripe account status
      if (account.charges_enabled && account.payouts_enabled) {
        partner.stripeAccountStatus = 'active';
      } else if (account.requirements?.disabled_reason) {
        partner.stripeAccountStatus = 'disabled';
      } else if (
        account.requirements?.currently_due &&
        account.requirements.currently_due.length > 0
      ) {
        partner.stripeAccountStatus = 'pending';
      }

      await this.referralPartnerRepository.save(partner);

      return {
        connected: true,
        accountId: partner.stripeAccountId,
        status: partner.stripeAccountStatus,
        payoutsEnabled: account.payouts_enabled,
        chargesEnabled: account.charges_enabled,
      };
    } catch (error) {
      console.error('Error fetching Stripe account status:', error);
      return {
        connected: true,
        accountId: partner.stripeAccountId,
        status: partner.stripeAccountStatus,
      };
    }
  }

  async createPayout(partnerId: string, amount: number): Promise<Stripe.Transfer> {
    const partner = await this.referralPartnerRepository.findOne({
      where: { id: partnerId },
    });

    if (!partner) {
      throw new NotFoundException('Referral partner not found');
    }

    if (!partner.stripeAccountId) {
      throw new BadRequestException('Partner has not connected their Stripe account');
    }

    if (partner.stripeAccountStatus !== 'active') {
      throw new BadRequestException('Partner Stripe account is not active');
    }

    if (amount > partner.pendingEarnings) {
      throw new BadRequestException('Payout amount exceeds pending earnings');
    }

    try {
      // Create a transfer to the connected account
      const transfer = await this.stripeService.createTransfer(amount, partner.stripeAccountId, {
        partnerId: partner.id,
        type: 'referral_payout',
      });

      // Update partner earnings
      partner.pendingEarnings -= amount;
      partner.paidEarnings += amount;
      await this.referralPartnerRepository.save(partner);

      return transfer;
    } catch (error) {
      console.error('Error creating payout:', error);
      throw new BadRequestException('Failed to create payout');
    }
  }

  async getBalance(partnerId: string): Promise<Stripe.Balance | null> {
    const partner = await this.referralPartnerRepository.findOne({
      where: { id: partnerId },
    });

    if (!partner || !partner.stripeAccountId) {
      return null;
    }

    try {
      return await this.stripeService.retrieveBalance(partner.stripeAccountId);
    } catch (error) {
      console.error('Error fetching balance:', error);
      return null;
    }
  }

  async handleWebhook(event: Stripe.Event): Promise<void> {
    switch (event.type) {
      case 'account.updated':
        await this.handleAccountUpdated(event.data.object as Stripe.Account);
        break;
      case 'transfer.created':
        await this.handleTransferCreated(event.data.object as Stripe.Transfer);
        break;
      case 'payout.paid':
        await this.handlePayoutPaid(event.data.object as Stripe.Payout);
        break;
      default:
        console.log(`Unhandled event type: ${event.type}`);
    }
  }

  private async handleAccountUpdated(account: Stripe.Account): Promise<void> {
    const partner = await this.referralPartnerRepository.findOne({
      where: { stripeAccountId: account.id },
    });

    if (partner) {
      if (account.charges_enabled && account.payouts_enabled) {
        partner.stripeAccountStatus = 'active';
      } else if (account.requirements?.disabled_reason) {
        partner.stripeAccountStatus = 'disabled';
      } else {
        partner.stripeAccountStatus = 'pending';
      }

      await this.referralPartnerRepository.save(partner);
    }
  }

  private async handleTransferCreated(transfer: Stripe.Transfer): Promise<void> {
    console.log('Transfer created:', transfer.id);
  }

  private async handlePayoutPaid(payout: Stripe.Payout): Promise<void> {
    console.log('Payout paid:', payout.id);
  }

  async updateAccountStatus(
    accountId: string,
    status: 'pending' | 'active' | 'restricted' | 'disabled',
  ): Promise<void> {
    const partner = await this.referralPartnerRepository.findOne({
      where: { stripeAccountId: accountId },
    });

    if (partner) {
      partner.stripeAccountStatus = status;
      await this.referralPartnerRepository.save(partner);
    }
  }

  async updateReferralTransfer(referralId: string, transferId: string): Promise<void> {
    const referral = await this.referralRepository.findOne({
      where: { id: referralId },
    });

    if (referral) {
      referral.stripeTransferId = transferId;
      await this.referralRepository.save(referral);
    }
  }
}
