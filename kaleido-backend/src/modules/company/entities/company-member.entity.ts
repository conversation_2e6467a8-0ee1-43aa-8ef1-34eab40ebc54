import {
  Column,
  CreateDateColumn,
  Entity,
  Index,
  JoinColumn,
  ManyToOne,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';
import { Company } from './company.entity';

export enum CompanyMemberRole {
  OWNER = 'owner',
  ADMIN = 'admin',
  MEMBER = 'member',
  VIEWER = 'viewer',
}

export enum CompanyMemberStatus {
  ACTIVE = 'active',
  INVITED = 'invited',
  SUSPENDED = 'suspended',
}

export interface CompanyMemberPermissions {
  canManageJobs?: boolean;
  canViewCandidates?: boolean;
  canManageCandidates?: boolean;
  canManageTeam?: boolean;
  canManageBilling?: boolean;
  canViewAnalytics?: boolean;
  canManageCompanySettings?: boolean;
}

@Entity('company_members')
@Index('idx_company_members_company_client', ['companyId', 'clientId'], { unique: true })
@Index('idx_company_members_client_id', ['clientId'])
@Index('idx_company_members_email', ['email'])
@Index('idx_company_members_status', ['status'])
export class CompanyMember {
  @ApiProperty({ example: 'uuid' })
  @PrimaryGeneratedColumn('uuid')
  id!: string;

  @ApiProperty({ type: () => Company })
  @ManyToOne(() => Company, (company) => company.members, { eager: false })
  @JoinColumn({ name: 'companyId' })
  company!: Company;

  @ApiProperty({ example: 'uuid' })
  @Column()
  companyId!: string;

  @ApiProperty({ example: 'auth0|123456' })
  @Column()
  clientId!: string;

  @ApiProperty({ example: '<EMAIL>' })
  @Column()
  email!: string;

  @ApiProperty({ enum: CompanyMemberRole, example: CompanyMemberRole.MEMBER })
  @Column({
    type: 'enum',
    enum: CompanyMemberRole,
    default: CompanyMemberRole.MEMBER,
  })
  role!: CompanyMemberRole;

  @ApiProperty({
    example: {
      canManageJobs: true,
      canViewCandidates: true,
      canManageCandidates: false,
      canManageTeam: false,
      canManageBilling: false,
      canViewAnalytics: true,
      canManageCompanySettings: false,
    },
  })
  @Column('jsonb', { nullable: true })
  permissions!: CompanyMemberPermissions;

  @ApiProperty({ enum: CompanyMemberStatus, example: CompanyMemberStatus.ACTIVE })
  @Column({
    type: 'enum',
    enum: CompanyMemberStatus,
    default: CompanyMemberStatus.INVITED,
  })
  status!: CompanyMemberStatus;

  @ApiProperty({
    example: 'auth0|654321',
    description: 'ClientId of the user who invited this member',
  })
  @Column({ nullable: true })
  invitedBy?: string;

  @ApiProperty({ example: '2024-01-01T00:00:00Z' })
  @CreateDateColumn()
  invitedAt!: Date;

  @ApiProperty({ example: '2024-01-02T00:00:00Z' })
  @Column({ type: 'timestamp', nullable: true })
  joinedAt?: Date;

  @ApiProperty({ example: '2024-01-01T00:00:00Z' })
  @CreateDateColumn()
  createdAt!: Date;

  @ApiProperty({ example: '2024-01-01T00:00:00Z' })
  @UpdateDateColumn()
  updatedAt!: Date;

  // Helper method to get default permissions based on role
  static getDefaultPermissions(role: CompanyMemberRole): CompanyMemberPermissions {
    switch (role) {
      case CompanyMemberRole.OWNER:
        return {
          canManageJobs: true,
          canViewCandidates: true,
          canManageCandidates: true,
          canManageTeam: true,
          canManageBilling: true,
          canViewAnalytics: true,
          canManageCompanySettings: true,
        };
      case CompanyMemberRole.ADMIN:
        return {
          canManageJobs: true,
          canViewCandidates: true,
          canManageCandidates: true,
          canManageTeam: true,
          canManageBilling: false,
          canViewAnalytics: true,
          canManageCompanySettings: true,
        };
      case CompanyMemberRole.MEMBER:
        return {
          canManageJobs: true,
          canViewCandidates: true,
          canManageCandidates: true,
          canManageTeam: false,
          canManageBilling: false,
          canViewAnalytics: true,
          canManageCompanySettings: false,
        };
      case CompanyMemberRole.VIEWER:
        return {
          canManageJobs: false,
          canViewCandidates: true,
          canManageCandidates: false,
          canManageTeam: false,
          canManageBilling: false,
          canViewAnalytics: true,
          canManageCompanySettings: false,
        };
    }
  }
}
