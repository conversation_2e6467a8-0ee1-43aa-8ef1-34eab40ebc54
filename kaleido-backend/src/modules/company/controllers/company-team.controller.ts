import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Body,
  Param,
  UseGuards,
  Request,
  HttpCode,
  HttpStatus,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth, ApiParam } from '@nestjs/swagger';
import { AuthGuard } from '@nestjs/passport';
import { CompanyTeamService } from '../services/company-team.service';
import {
  InviteTeamMemberDto,
  AcceptInvitationDto,
  UpdateTeamMemberDto,
  UpdateCompanyTeamSettingsDto,
  CompanyMemberResponseDto,
  CompanyInvitationResponseDto,
} from '../dto/team-management.dto';
import { CompanyService } from '../company.service';

@ApiTags('company-team')
@Controller('company')
@UseGuards(AuthGuard('jwt'))
@ApiBearerAuth()
export class CompanyTeamController {
  constructor(
    private companyTeamService: CompanyTeamService,
    private companyService: CompanyService,
  ) {}

  @Get(':companyId/members')
  @ApiOperation({ summary: 'Get all company team members' })
  @ApiParam({ name: 'companyId', type: 'string' })
  @ApiResponse({
    status: 200,
    description: 'List of company team members',
    type: [CompanyMemberResponseDto],
  })
  async getCompanyMembers(
    @Param('companyId') companyId: string,
    @Request() req: any,
  ): Promise<CompanyMemberResponseDto[]> {
    // Verify user has access to this company
    await this.companyTeamService.getMemberByClientId(companyId, req.user.sub);

    return this.companyTeamService.getCompanyMembers(companyId);
  }

  @Post(':companyId/members/invite')
  @ApiOperation({ summary: 'Invite a new team member' })
  @ApiParam({ name: 'companyId', type: 'string' })
  @ApiResponse({
    status: 201,
    description: 'Invitation created successfully',
    type: CompanyInvitationResponseDto,
  })
  async inviteTeamMember(
    @Param('companyId') companyId: string,
    @Body() inviteDto: InviteTeamMemberDto,
    @Request() req: any,
  ): Promise<CompanyInvitationResponseDto> {
    return this.companyTeamService.inviteTeamMember(
      companyId,
      req.user.sub,
      inviteDto.email,
      inviteDto.role,
      inviteDto.permissions,
      inviteDto.message,
    );
  }

  @Post('accept-invitation')
  @ApiOperation({ summary: 'Accept a team invitation' })
  @ApiResponse({
    status: 200,
    description: 'Invitation accepted successfully',
    type: CompanyMemberResponseDto,
  })
  async acceptInvitation(
    @Body() acceptDto: AcceptInvitationDto,
    @Request() req: any,
  ): Promise<CompanyMemberResponseDto> {
    return this.companyTeamService.acceptInvitation(acceptDto.token, req.user.sub, req.user.email);
  }

  @Put(':companyId/members/:clientId')
  @ApiOperation({ summary: 'Update team member role or permissions' })
  @ApiParam({ name: 'companyId', type: 'string' })
  @ApiParam({ name: 'clientId', type: 'string' })
  @ApiResponse({
    status: 200,
    description: 'Member updated successfully',
    type: CompanyMemberResponseDto,
  })
  async updateTeamMember(
    @Param('companyId') companyId: string,
    @Param('clientId') clientId: string,
    @Body() updateDto: UpdateTeamMemberDto,
    @Request() req: any,
  ): Promise<CompanyMemberResponseDto> {
    return this.companyTeamService.updateMember(companyId, req.user.sub, clientId, updateDto);
  }

  @Delete(':companyId/members/:clientId')
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiOperation({ summary: 'Remove a team member' })
  @ApiParam({ name: 'companyId', type: 'string' })
  @ApiParam({ name: 'clientId', type: 'string' })
  @ApiResponse({
    status: 204,
    description: 'Member removed successfully',
  })
  async removeTeamMember(
    @Param('companyId') companyId: string,
    @Param('clientId') clientId: string,
    @Request() req: any,
  ): Promise<void> {
    await this.companyTeamService.removeMember(companyId, req.user.sub, clientId);
  }

  @Get('my-companies')
  @ApiOperation({ summary: 'Get all companies the current user is a member of' })
  @ApiResponse({
    status: 200,
    description: 'List of companies',
    type: [CompanyMemberResponseDto],
  })
  async getUserCompanies(@Request() req: any): Promise<CompanyMemberResponseDto[]> {
    return this.companyTeamService.getUserCompanies(req.user.sub);
  }

  @Get(':companyId/invitations')
  @ApiOperation({ summary: 'Get all pending invitations for a company' })
  @ApiParam({ name: 'companyId', type: 'string' })
  @ApiResponse({
    status: 200,
    description: 'List of pending invitations',
    type: [CompanyInvitationResponseDto],
  })
  async getCompanyInvitations(
    @Param('companyId') companyId: string,
    @Request() req: any,
  ): Promise<CompanyInvitationResponseDto[]> {
    // Verify user is a member of this company
    const member = await this.companyTeamService.getMemberByClientId(companyId, req.user.sub);
    if (!member) {
      throw new Error('You are not a member of this company');
    }

    // This method needs to be added to the service
    return this.companyTeamService.getCompanyInvitations(companyId);
  }

  @Delete(':companyId/invitations/:invitationId')
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiOperation({ summary: 'Cancel a pending invitation' })
  @ApiParam({ name: 'companyId', type: 'string' })
  @ApiParam({ name: 'invitationId', type: 'string' })
  @ApiResponse({
    status: 204,
    description: 'Invitation cancelled successfully',
  })
  async cancelInvitation(
    @Param('companyId') companyId: string,
    @Param('invitationId') invitationId: string,
    @Request() req: any,
  ): Promise<void> {
    await this.companyTeamService.cancelInvitation(companyId, req.user.sub, invitationId);
  }

  @Post(':companyId/invitations/:invitationId/resend')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'Resend an invitation email' })
  @ApiParam({ name: 'companyId', type: 'string' })
  @ApiParam({ name: 'invitationId', type: 'string' })
  @ApiResponse({
    status: 200,
    description: 'Invitation resent successfully',
  })
  async resendInvitation(
    @Param('companyId') companyId: string,
    @Param('invitationId') invitationId: string,
    @Request() req: any,
  ): Promise<{ message: string }> {
    // Verify user has permission to manage team
    const canManage = await this.companyTeamService.hasPermission(
      companyId,
      req.user.sub,
      'canManageTeam',
    );
    if (!canManage) {
      throw new Error('You do not have permission to resend invitations');
    }

    // Resend the invitation
    await this.companyTeamService.resendInvitation(companyId, invitationId);
    return { message: 'Invitation resent successfully' };
  }

  @Put(':companyId/team-settings')
  @ApiOperation({ summary: 'Update company team settings (email domains, auto-join)' })
  @ApiParam({ name: 'companyId', type: 'string' })
  @ApiResponse({
    status: 200,
    description: 'Settings updated successfully',
  })
  async updateTeamSettings(
    @Param('companyId') companyId: string,
    @Body() settingsDto: UpdateCompanyTeamSettingsDto,
    @Request() req: any,
  ): Promise<any> {
    // Verify user has permission to manage company settings
    const canManage = await this.companyTeamService.hasPermission(
      companyId,
      req.user.sub,
      'canManageCompanySettings',
    );
    if (!canManage) {
      throw new Error('You do not have permission to update company settings');
    }

    // Update company team settings
    return this.companyTeamService.updateTeamSettings(companyId, settingsDto);
  }
}
