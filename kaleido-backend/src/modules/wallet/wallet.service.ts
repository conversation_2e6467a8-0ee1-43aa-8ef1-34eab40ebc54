import { google } from 'googleapis';
const jwt = require('jsonwebtoken');

import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';

import { JobSeeker } from '../job-seeker/entities/job-seeker.entity';

interface GoogleWalletPass {
  iss: string;
  aud: string;
  typ: string;
  iat: number;
  payload: {
    genericObjects: Array<{
      classId: string;
      id: string;
      genericType: string;
      hexBackgroundColor: string;
      logo: {
        sourceUri: {
          uri: string;
        };
      };
      cardTitle: {
        defaultValue: {
          language: string;
          value: string;
        };
      };
      subheader: {
        defaultValue: {
          language: string;
          value: string;
        };
      };
      header: {
        defaultValue: {
          language: string;
          value: string;
        };
      };
      textModulesData: Array<{
        id: string;
        header: string;
        body: string;
      }>;
      linksModuleData: {
        uris: Array<{
          uri: string;
          description: string;
        }>;
      };
      barcode: {
        type: string;
        value: string;
        alternateText: string;
      };
    }>;
  };
}

@Injectable()
export class WalletService {
  private readonly logger = new Logger(WalletService.name);
  private readonly issuerId: string;
  private readonly classId: string;
  private readonly serviceAccountEmail: string;
  private readonly privateKey: string;

  constructor(private configService: ConfigService) {
    this.issuerId = this.configService.get<string>('GOOGLE_WALLET_ISSUER_ID') || '';
    this.classId = `${this.issuerId}.job_seeker_profile_class`;
    this.serviceAccountEmail =
      this.configService.get<string>('GOOGLE_WALLET_SERVICE_ACCOUNT_EMAIL') || '';
    this.privateKey =
      this.configService.get<string>('GOOGLE_WALLET_PRIVATE_KEY')?.replace(/\\n/g, '\n') || '';
  }

  /**
   * Generate a Google Wallet save URL for a job seeker profile
   */
  async generateGoogleWalletPass(jobSeeker: JobSeeker): Promise<string> {
    try {
      this.logger.log(`Generating Google Wallet pass for job seeker: ${jobSeeker.id}`);

      // Create the pass object
      const passObject = this.createPassObject(jobSeeker);

      // Create JWT token
      const token = this.createJWT(passObject);

      // Generate the save URL
      const saveUrl = `https://pay.google.com/gp/v/save/${token}`;

      this.logger.log(`Generated Google Wallet save URL for job seeker: ${jobSeeker.id}`);
      return saveUrl;
    } catch (error) {
      this.logger.error(
        `Failed to generate Google Wallet pass for job seeker ${jobSeeker.id}:`,
        error,
      );
      throw new Error('Failed to generate Google Wallet pass');
    }
  }

  /**
   * Create the pass object with job seeker data
   */
  private createPassObject(jobSeeker: JobSeeker): GoogleWalletPass['payload'] {
    const profileUrl = `${this.configService.get<string>('FRONTEND_URL') || 'http://localhost:3000'}/job-seeker/mobile/${jobSeeker.id}/profile`;

    const textModules = [
      {
        id: 'email',
        header: 'Email',
        body: jobSeeker.email,
      },
    ];

    // Add optional fields if they exist
    if (jobSeeker.phone) {
      textModules.push({
        id: 'phone',
        header: 'Phone',
        body: jobSeeker.phone,
      });
    }

    if (jobSeeker.location) {
      textModules.push({
        id: 'location',
        header: 'Location',
        body: jobSeeker.location,
      });
    }

    // Add top skills if available
    if (jobSeeker.skills && jobSeeker.skills.length > 0) {
      const topSkills = jobSeeker.skills.slice(0, 3).join(', ');
      textModules.push({
        id: 'skills',
        header: 'Top Skills',
        body: topSkills,
      });
    }

    return {
      genericObjects: [
        {
          classId: this.classId,
          id: `${this.issuerId}.job_seeker_${jobSeeker.id}`,
          genericType: 'GENERIC_TYPE_UNSPECIFIED',
          hexBackgroundColor: '#4285F4',
          logo: {
            sourceUri: {
              uri: 'https://your-domain.com/logo.png', // Replace with your logo URL
            },
          },
          cardTitle: {
            defaultValue: {
              language: 'en-US',
              value: 'Professional Profile',
            },
          },
          subheader: {
            defaultValue: {
              language: 'en-US',
              value: 'Kaleido Talent',
            },
          },
          header: {
            defaultValue: {
              language: 'en-US',
              value: `${jobSeeker.firstName} ${jobSeeker.lastName}`,
            },
          },
          textModulesData: textModules,
          linksModuleData: {
            uris: [
              {
                uri: profileUrl,
                description: 'View Full Profile',
              },
            ],
          },
          barcode: {
            type: 'QR_CODE',
            value: profileUrl,
            alternateText: 'Scan to view profile',
          },
        },
      ],
    };
  }

  /**
   * Create JWT token for the pass
   */
  private createJWT(payload: GoogleWalletPass['payload']): string {
    const now = Math.floor(Date.now() / 1000);

    const jwtPayload: GoogleWalletPass = {
      iss: this.serviceAccountEmail,
      aud: 'google',
      typ: 'savetowallet',
      iat: now,
      payload,
    };

    return jwt.sign(jwtPayload, this.privateKey, {
      algorithm: 'RS256',
    });
  }

  /**
   * Create or update the generic class (one-time setup)
   */
  async createGenericClass(): Promise<void> {
    try {
      this.logger.log('Creating/updating Google Wallet generic class');

      const auth = new google.auth.JWT({
        email: this.serviceAccountEmail,
        key: this.privateKey,
        scopes: ['https://www.googleapis.com/auth/wallet_object.issuer'],
      });

      const walletobjects = google.walletobjects({ version: 'v1', auth });

      const genericClass = {
        id: this.classId,
        classTemplateInfo: {
          cardTemplateOverride: {
            cardRowTemplateInfos: [
              {
                twoItems: {
                  startItem: {
                    firstValue: {
                      fields: [
                        {
                          fieldPath: 'object.textModulesData["email"]',
                        },
                      ],
                    },
                  },
                  endItem: {
                    firstValue: {
                      fields: [
                        {
                          fieldPath: 'object.textModulesData["phone"]',
                        },
                      ],
                    },
                  },
                },
              },
            ],
          },
        },
      };

      await walletobjects.genericclass.insert({
        requestBody: genericClass,
      });

      this.logger.log('Successfully created Google Wallet generic class');
    } catch (error) {
      if (error.code === 409) {
        this.logger.log('Generic class already exists');
      } else {
        this.logger.error('Failed to create generic class:', error);
        throw error;
      }
    }
  }
}
