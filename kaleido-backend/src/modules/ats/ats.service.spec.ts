import { HttpService } from '@nestjs/axios';
import { Test, TestingModule } from '@nestjs/testing';

import { CompanyService } from '../company/company.service';
import { ATSCandidateAdapter } from './adapters/ats-candidate.adapter';
import { AtsConfigService } from './ats.config';
import { AtsService } from './ats.service';
import { ATSCandidate, ATSCandidateDetails, ATSJobDetails } from './interfaces/ats.interface';
import { ATSProviderFactory } from './providers/ats-provider.factory';
import axios from 'axios';

// Mock axios
jest.mock('axios');
const mockedAxios = axios as jest.Mocked<typeof axios>;

describe('AtsService', () => {
  let service: AtsService;
  let httpService: jest.Mocked<HttpService>;
  let companyService: jest.Mocked<CompanyService>;
  let candidateAdapter: jest.Mocked<ATSCandidateAdapter>;
  let atsConfigService: jest.Mocked<AtsConfigService>;
  let atsProviderFactory: jest.Mocked<ATSProviderFactory>;

  const mockCompany = {
    id: 'company-1',
    clientId: 'auth0-user-123',
    companyName: 'Test Company',
    atsConfig: {
      providerId: '1',
      provider: 'Greenhouse',
      apiKey: 'test-api-key',
      subdomain: 'test-subdomain',
      companyId: 'test-company-id',
    },
  };

  const mockATSConfig = {
    id: '1',
    name: 'Greenhouse',
    baseUrl: 'https://harvest.greenhouse.io/v1',
    jobEndpoint: 'jobs',
    applicantEndpoint: 'applications',
    authType: 'Basic',
    authHeaderKey: 'Authorization',
    pagination: {
      type: 'offset',
      pageParam: 'page',
      limitParam: 'per_page',
      defaultPageSize: 100,
    },
    filtering: {
      statusParam: 'status',
      searchParam: 'q',
      locationParam: 'location',
      defaultPageSize: 100,
    },
  };

  const mockCredentials = {
    apiKey: 'test-api-key',
    subdomain: 'test-subdomain',
    companyId: 'test-company-id',
  };

  const mockATSJob: ATSJobDetails = {
    id: 'ats-job-1',
    title: 'Software Engineer',
    shortCode: 'SE001',
    dateModified: new Date('2023-01-01'),
    dateCreated: new Date('2023-01-01'),
    status: 'open',
    applicantCount: 5,
    description: 'Test job description',
    requirements: ['JavaScript', 'React'],
    location: ['Dubai'],
    department: 'Engineering',
    employmentType: 'full-time',
    experienceLevel: 'senior',
    skills: ['JavaScript', 'React', 'Node.js'],
    salaryRange: 'AED 60,000 - 84,000',
    currency: 'AED',
  };

  const mockATSCandidate: ATSCandidate = {
    id: 'ats-candidate-1',
    externalId: 'ext-candidate-1',
    fullName: 'John Doe',
    firstName: 'John',
    lastName: 'Doe',
    email: '<EMAIL>',
    phone: '+971501234567',
    jobTitle: 'Software Engineer',
    location: 'Dubai',
    skills: ['JavaScript', 'React'],
    status: 'applied',
    stage: 'screening',
    source: 'LinkedIn',
  };

  const mockATSCandidateDetails: ATSCandidateDetails = {
    ...mockATSCandidate,
    experience: [
      {
        title: 'Senior Developer',
        company: 'Tech Corp',
        startDate: '2020-01-01',
        endDate: '2023-01-01',
        current: false,
        summary: 'Developed web applications',
        location: 'Dubai',
      },
    ],
    education: [
      {
        school: 'University of Technology',
        degree: 'Bachelor of Science',
        fieldOfStudy: 'Computer Science',
        startDate: '2016-09-01',
        endDate: '2020-06-01',
      },
    ],
    resumeUrl: 'https://example.com/resume.pdf',
    coverLetter: 'I am interested in this position...',
  };

  const mockProvider = {
    getJobs: jest.fn(),
    getJobDetails: jest.fn(),
    getCandidates: jest.fn(),
    getCandidateDetails: jest.fn(),
    mapJobsResponse: jest.fn(),
    testConnection: jest.fn(),
  };

  beforeEach(async () => {
    const mockHttpService = {
      get: jest.fn(),
      post: jest.fn(),
      put: jest.fn(),
      delete: jest.fn(),
      request: jest.fn(),
    };

    const mockCompanyService = {
      findByClientId: jest.fn(),
    };

    const mockCandidateAdapter = {
      convertToCandidate: jest.fn(),
    };

    const mockAtsConfigService = {
      getConfig: jest.fn(),
      getConfigById: jest.fn(),
      getAllConfigs: jest.fn(),
    };

    const mockAtsProviderFactory = {
      getProvider: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        AtsService,
        {
          provide: HttpService,
          useValue: mockHttpService,
        },
        {
          provide: CompanyService,
          useValue: mockCompanyService,
        },
        {
          provide: ATSCandidateAdapter,
          useValue: mockCandidateAdapter,
        },
        {
          provide: AtsConfigService,
          useValue: mockAtsConfigService,
        },
        {
          provide: ATSProviderFactory,
          useValue: mockAtsProviderFactory,
        },
      ],
    }).compile();

    service = module.get<AtsService>(AtsService);
    httpService = module.get(HttpService);
    companyService = module.get(CompanyService);
    candidateAdapter = module.get(ATSCandidateAdapter);
    atsConfigService = module.get(AtsConfigService);
    atsProviderFactory = module.get(ATSProviderFactory);

    // Reset axios mock
    mockedAxios.get.mockReset();
    mockedAxios.post.mockReset();
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('getCompanyAtsConfig', () => {
    it('should get company ATS config successfully', async () => {
      companyService.findByClientId.mockResolvedValue(mockCompany as any);
      atsConfigService.getConfigById.mockReturnValue(mockATSConfig as any);

      const result = await service['getCompanyAtsConfig']('auth0-user-123');

      expect(result).toEqual({
        config: mockATSConfig,
        credentials: {
          apiKey: 'test-api-key',
          subdomain: 'test-subdomain',
          companyId: 'test-company-id',
        },
      });
      expect(companyService.findByClientId).toHaveBeenCalledWith('auth0-user-123');
      expect(atsConfigService.getConfigById).toHaveBeenCalledWith('1');
    });

    it('should throw error when company not found', async () => {
      companyService.findByClientId.mockResolvedValue(null as any);

      await expect(service['getCompanyAtsConfig']('invalid-client-id')).rejects.toThrow(
        'Company or ATS configuration not found',
      );
    });

    it('should throw error when ATS config not found', async () => {
      const companyWithoutAts = { ...mockCompany, atsConfig: null };
      companyService.findByClientId.mockResolvedValue(companyWithoutAts as any);

      await expect(service['getCompanyAtsConfig']('auth0-user-123')).rejects.toThrow(
        'Company or ATS configuration not found',
      );
    });

    it('should throw error when ATS provider config not found', async () => {
      companyService.findByClientId.mockResolvedValue(mockCompany as any);
      atsConfigService.getConfigById.mockReturnValue(undefined);

      await expect(service['getCompanyAtsConfig']('auth0-user-123')).rejects.toThrow(
        'ATS provider with ID 1 not found',
      );
    });
  });

  describe('getJobs', () => {
    beforeEach(() => {
      companyService.findByClientId.mockResolvedValue(mockCompany as any);
      atsConfigService.getConfigById.mockReturnValue(mockATSConfig as any);
      atsProviderFactory.getProvider.mockReturnValue(mockProvider as any);
    });

    it('should get jobs successfully', async () => {
      const mockJobsResponse = [mockATSJob];
      mockProvider.mapJobsResponse.mockReturnValue(mockJobsResponse);
      mockedAxios.get.mockResolvedValue({ data: { jobs: [mockATSJob] } });

      const result = await service.getJobs('auth0-user-123', 50);

      expect(result).toEqual(mockJobsResponse);
      expect(atsProviderFactory.getProvider).toHaveBeenCalledWith('Greenhouse');
      expect(mockProvider.mapJobsResponse).toHaveBeenCalled();
    });

    it('should handle provider not found', async () => {
      atsProviderFactory.getProvider.mockReturnValue(undefined as any);

      await expect(service.getJobs('auth0-user-123')).rejects.toThrow(
        'No provider found for Greenhouse',
      );
    });

    it('should handle API errors', async () => {
      companyService.findByClientId.mockResolvedValue(mockCompany as any);
      atsConfigService.getConfigById.mockReturnValue(mockATSConfig as any);
      mockedAxios.get.mockRejectedValue(new Error('API Error'));

      const result = await service.getJobs('auth0-user-123');

      expect(result).toEqual([]);
    });
  });

  describe('getJobDetails', () => {
    beforeEach(() => {
      companyService.findByClientId.mockResolvedValue(mockCompany as any);
      atsConfigService.getConfigById.mockReturnValue(mockATSConfig as any);
    });

    it('should get job details successfully', async () => {
      mockedAxios.get.mockResolvedValue({ data: mockATSJob });

      const result = await service.getJobDetails('auth0-user-123', 'job-123');

      expect(result).toEqual(mockATSJob);
      expect(mockedAxios.get).toHaveBeenCalledWith(
        expect.stringContaining('jobs/job-123'),
        expect.objectContaining({
          headers: expect.objectContaining({
            Authorization: expect.any(String),
          }),
        }),
      );
    });

    it('should handle API errors when getting job details', async () => {
      companyService.findByClientId.mockResolvedValue(mockCompany as any);
      atsConfigService.getConfigById.mockReturnValue(mockATSConfig as any);
      mockedAxios.get.mockRejectedValue(new Error('Job not found'));

      const result = await service.getJobDetails('auth0-user-123', 'invalid-job-id');

      expect(result).toBeNull();
    });
  });

  describe('getCandidates', () => {
    beforeEach(() => {
      companyService.findByClientId.mockResolvedValue(mockCompany as any);
      atsConfigService.getConfigById.mockReturnValue(mockATSConfig as any);
    });

    it('should get candidates successfully for non-Lever provider', async () => {
      const mockCandidates = [mockATSCandidate];
      mockedAxios.get.mockResolvedValue({ data: mockCandidates });

      const result = await service.getCandidates('auth0-user-123', 'job-123');

      expect(result).toEqual(mockCandidates);
      expect(mockedAxios.get).toHaveBeenCalledWith(
        expect.stringContaining('jobs/job-123/candidates'),
        expect.objectContaining({
          headers: expect.objectContaining({
            Authorization: expect.any(String),
          }),
        }),
      );
    });

    it('should handle Lever provider specially', async () => {
      const leverCompany = {
        ...mockCompany,
        atsConfig: { ...mockCompany.atsConfig, provider: 'Lever' },
      };
      const leverConfig = { ...mockATSConfig, name: 'Lever' };

      companyService.findByClientId.mockResolvedValue(leverCompany as any);
      atsConfigService.getConfigById.mockReturnValue(leverConfig as any);
      atsConfigService.getConfig.mockReturnValue(leverConfig as any);
      atsProviderFactory.getProvider.mockReturnValue(mockProvider as any);

      const mockCandidates = [mockATSCandidate];
      mockProvider.getCandidates.mockResolvedValue(mockCandidates);

      const result = await service.getCandidates('auth0-user-123', 'job-123');

      expect(result).toEqual(mockCandidates);
      expect(mockProvider.getCandidates).toHaveBeenCalledWith('job-123', 'auth0-user-123');
    });

    it('should handle API errors when getting candidates', async () => {
      companyService.findByClientId.mockResolvedValue(mockCompany as any);
      atsConfigService.getConfigById.mockReturnValue(mockATSConfig as any);
      mockedAxios.get.mockRejectedValue(new Error('Candidates not found'));

      const result = await service.getCandidates('auth0-user-123', 'invalid-job-id');

      expect(result).toEqual([]);
    });
  });

  describe('getCandidateDetails', () => {
    beforeEach(() => {
      companyService.findByClientId.mockResolvedValue(mockCompany as any);
      atsConfigService.getConfigById.mockReturnValue(mockATSConfig as any);
    });

    it('should get candidate details successfully', async () => {
      mockedAxios.get.mockResolvedValue({ data: mockATSCandidateDetails });

      const result = await service.getCandidateDetails(
        'auth0-user-123',
        'job-123',
        'candidate-456',
      );

      expect(result).toEqual(mockATSCandidateDetails);
      expect(mockedAxios.get).toHaveBeenCalledWith(
        expect.stringContaining('jobs/job-123/applications/candidate-456'),
        expect.objectContaining({
          headers: expect.objectContaining({
            Authorization: expect.any(String),
          }),
        }),
      );
    });

    it('should handle API errors when getting candidate details', async () => {
      companyService.findByClientId.mockResolvedValue(mockCompany as any);
      atsConfigService.getConfigById.mockReturnValue(mockATSConfig as any);
      mockedAxios.get.mockRejectedValue(new Error('Candidate not found'));

      const result = await service.getCandidateDetails(
        'auth0-user-123',
        'job-123',
        'invalid-candidate-id',
      );

      expect(result).toBeNull();
    });
  });

  describe('convertToCandidate', () => {
    beforeEach(() => {
      companyService.findByClientId.mockResolvedValue(mockCompany as any);
      atsConfigService.getConfigById.mockReturnValue(mockATSConfig as any);
    });

    it('should convert ATS candidate to internal candidate successfully', async () => {
      const mockConvertedCandidate = {
        id: 'candidate-123',
        fullName: 'John Doe',
        email: '<EMAIL>',
        jobId: 'job-123',
        clientId: 'auth0-user-123',
      };

      mockedAxios.get.mockResolvedValue({ data: mockATSCandidateDetails });
      candidateAdapter.convertToCandidate.mockReturnValue(mockConvertedCandidate as any);

      const result = await service.convertToCandidate('auth0-user-123', 'job-123', 'candidate-456');

      expect(result).toEqual(mockConvertedCandidate);
      expect(candidateAdapter.convertToCandidate).toHaveBeenCalledWith(
        mockATSCandidateDetails,
        'job-123',
        'auth0-user-123',
      );
    });

    it('should handle errors when converting candidate', async () => {
      companyService.findByClientId.mockResolvedValue(mockCompany as any);
      atsConfigService.getConfigById.mockReturnValue(mockATSConfig as any);
      mockedAxios.get.mockRejectedValue(new Error('Candidate details not found'));

      const result = await service.convertToCandidate(
        'auth0-user-123',
        'job-123',
        'invalid-candidate-id',
      );

      expect(result).toBeNull();
    });
  });

  describe('testConnection', () => {
    it('should test connection successfully with provider name', async () => {
      const connectionConfig = {
        provider: 'greenhouse',
        providerName: 'Greenhouse',
        apiKey: 'test-api-key',
        subdomain: 'test-subdomain',
        authType: 'Bearer',
      };

      atsConfigService.getConfig.mockReturnValue(mockATSConfig as any);
      // Mock axios to simulate successful connection
      mockedAxios.get.mockResolvedValue({ status: 200, data: {} });

      const result = await service.testConnection(connectionConfig);

      expect(result).toBe(true);
      expect(atsConfigService.getConfig).toHaveBeenCalledWith('Greenhouse');
    });

    it('should test connection successfully with provider ID fallback', async () => {
      const connectionConfig = {
        provider: '1',
        apiKey: 'test-api-key',
      };

      // First call to getConfig should return undefined (no provider name provided)
      // Second call to getConfigById should return the config
      atsConfigService.getConfig.mockReturnValue(undefined);
      atsConfigService.getConfigById.mockReturnValue(mockATSConfig as any);
      // Mock axios to simulate successful connection
      mockedAxios.get.mockResolvedValue({ status: 200, data: {} });

      const result = await service.testConnection(connectionConfig);

      expect(result).toBe(true);
      expect(atsConfigService.getConfigById).toHaveBeenCalledWith('1');
    });

    it('should handle failed connection test', async () => {
      const connectionConfig = {
        provider: 'greenhouse',
        providerName: 'Greenhouse',
        apiKey: 'invalid-api-key',
      };

      atsConfigService.getConfig.mockReturnValue(mockATSConfig as any);
      // Mock axios to simulate failed connection (401 Unauthorized)
      const axiosError = new Error('Request failed with status code 401');
      (axiosError as any).response = {
        status: 401,
        statusText: 'Unauthorized',
        data: {},
      };
      mockedAxios.get.mockRejectedValue(axiosError);
      mockedAxios.isAxiosError.mockReturnValue(true);

      await expect(service.testConnection(connectionConfig)).rejects.toThrow(
        'Invalid credentials using default authentication',
      );
    });

    it('should handle errors when testing connection', async () => {
      const connectionConfig = {
        provider: 'invalid-provider',
        apiKey: 'test-api-key',
      };

      atsConfigService.getConfig.mockReturnValue(undefined);
      atsConfigService.getConfigById.mockReturnValue(undefined);
      // Reset axios mocks to avoid interference
      mockedAxios.get.mockReset();
      mockedAxios.isAxiosError.mockReset();

      await expect(service.testConnection(connectionConfig)).rejects.toThrow(
        'ATS provider not found: invalid-provider',
      );
    });
  });

  describe('buildUrl', () => {
    it('should build URL correctly with subdomain', () => {
      const result = service['buildUrl']('https://harvest.greenhouse.io/v1', 'jobs', {
        subdomain: 'test-subdomain',
      });

      expect(result).toBe('https://test-subdomain.greenhouse.io/v1/jobs');
    });

    it('should build URL correctly with company ID', () => {
      const result = service['buildUrl'](
        'https://api.workable.com/spi/v3/accounts/{companyId}',
        'jobs',
        { companyId: 'test-company' },
      );

      expect(result).toBe('https://api.workable.com/spi/v3/accounts/test-company/jobs');
    });

    it('should build URL correctly without placeholders', () => {
      const result = service['buildUrl']('https://api.lever.co/v1', 'postings', {});

      expect(result).toBe('https://api.lever.co/v1/postings');
    });
  });

  describe('getAuthHeaders', () => {
    it('should generate Basic auth headers correctly', () => {
      const result = service['getAuthHeaders']('test-api-key', 'Basic', { Authorization: 'Basic' });

      expect(result).toEqual({
        'Content-Type': 'application/json',
        Authorization: `Basic ${Buffer.from('test-api-key:').toString('base64')}`,
      });
    });

    it('should generate Bearer auth headers correctly', () => {
      const result = service['getAuthHeaders']('test-api-key', 'Bearer', {
        Authorization: 'Bearer',
      });

      expect(result).toEqual({
        'Content-Type': 'application/json',
        Authorization: 'Bearer test-api-key',
      });
    });

    it('should generate custom header auth correctly', () => {
      const result = service['getAuthHeaders']('test-api-key', 'Manual', {
        'x-api-key': 'X-API-Key',
      });

      expect(result).toEqual({
        'Content-Type': 'application/json',
        'x-api-key': 'test-api-key',
      });
    });
  });
});
