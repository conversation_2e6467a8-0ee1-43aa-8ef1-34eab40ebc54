import { Injectable, Logger, UnauthorizedException } from '@nestjs/common';
import { decode, verify } from 'jsonwebtoken';

import { UserRole } from '@/common/enums/role.enum';
import { User } from '@/shared/decorators/get-user.decorator';
import { ConfigService } from '@nestjs/config';
import jwksClient from 'jwks-rsa';
import { RolesService } from '../roles/roles.service';

@Injectable()
export class AuthService {
  private readonly logger = new Logger(AuthService.name);
  private readonly jwksClient: jwksClient.JwksClient;

  constructor(
    private configService: ConfigService,
    private rolesService: RolesService,
  ) {
    const auth0Issuer = this.configService.get('AUTH0_ISSUER');
    this.jwksClient = jwksClient({
      jwksUri: `https://${auth0Issuer}/.well-known/jwks.json`,
      cache: true,
      rateLimit: true,
      jwksRequestsPerMinute: 5,
    });
  }

  async hasRole(user: User, requiredRoles: UserRole[]): Promise<boolean> {
    if (!user || !user.roles) {
      return false;
    }

    // Check if the user has any of the required roles
    return requiredRoles.some((role) => user.roles.includes(role));
  }

  async verifyToken(token: string): Promise<any> {
    try {
      // Decode the token without verification to get the kid
      const decoded = decode(token, { complete: true }) as { header: { kid: string } } | null;
      if (!decoded || !decoded.header || !decoded.header.kid) {
        throw new UnauthorizedException('Invalid token');
      }

      // Get the signing key from Auth0
      const signingKey = await this.getSigningKey(decoded.header.kid);

      // Verify the token
      const auth0Issuer = this.configService.get('AUTH0_ISSUER');
      const auth0Audience = this.configService.get('AUTH0_AUDIENCE');

      return verify(token, signingKey, {
        audience: auth0Audience,
        issuer: `https://${auth0Issuer}/`,
        algorithms: ['RS256'],
      });
    } catch (error: any) {
      this.logger.error(`Token verification failed: ${error.message}`);
      throw new UnauthorizedException('Invalid token');
    }
  }

  private async getSigningKey(kid: string): Promise<string> {
    try {
      const key = await this.jwksClient.getSigningKey(kid);
      return key.getPublicKey();
    } catch (error: any) {
      this.logger.error(`Failed to get signing key: ${error.message}`);
      throw new UnauthorizedException('Unable to verify token');
    }
  }

  /**
   * Get the roles service for use in the controller
   */
  getRolesService(): RolesService {
    return this.rolesService;
  }
}
