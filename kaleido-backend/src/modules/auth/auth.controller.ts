import axios from 'axios';

import { Public } from '@/auth/public.decorator';
import { GetUser, User } from '@/shared/decorators/get-user.decorator';
import { Body, Controller, Get, Logger, Post, Req, UseGuards } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';

import { Auth0Guard } from '../../auth/auth.guard';
import { AuthService as CoreAuthService } from '../../auth/auth.service';
import { CompanyService } from '../company/company.service';
import { JobSeekerService } from '../job-seeker/job-seeker.service';
import { AuthService } from './auth.service';

@ApiTags('auth')
@Controller('auth')
export class AuthController {
  private readonly logger = new Logger(AuthController.name);

  constructor(
    private readonly authService: AuthService,
    private readonly coreAuthService: CoreAuthService,
    private readonly configService: ConfigService,
    private readonly companyService: CompanyService,
    private readonly jobSeekerService: JobSeekerService,
  ) {}

  @Get('me')
  @UseGuards(Auth0Guard)
  @ApiOperation({ summary: 'Get current user information with onboarding status' })
  @ApiResponse({ status: 200, description: 'Returns the current user information' })
  async getCurrentUser(@GetUser() user: User) {
    try {
      // Get user role
      const userRole = user.roles?.[0] || null;

      // Check onboarding status based on role
      let needsOnboarding = false;
      let profile = null;

      if (userRole === 'employer') {
        try {
          const company = await this.companyService.findByClientIdOrNull(user.userId);
          if (company) {
            // Check if essential fields are filled out
            needsOnboarding =
              !company.companyName ||
              !company.companyWebsite ||
              !company.contactEmail ||
              !company.industry ||
              !company.size;
            profile = company;
          } else {
            needsOnboarding = true;
          }
        } catch (error) {
          // No company found, needs onboarding
          needsOnboarding = true;
        }
      } else if (userRole === 'job-seeker' || userRole === 'graduate') {
        try {
          const jobSeeker = await this.jobSeekerService.getByClientId(user.userId);
          needsOnboarding = !jobSeeker || !jobSeeker.hasCompletedOnboarding;
          profile = jobSeeker;
        } catch (error) {
          // No job seeker found, needs onboarding
          needsOnboarding = true;
        }
      }

      return {
        ...user,
        role: userRole,
        needsOnboarding,
        profile,
        isNewUser:
          user.created_at && Date.now() - new Date(user.created_at).getTime() < 24 * 60 * 60 * 1000, // User created within last 24 hours
      };
    } catch (error) {
      this.logger.error('Error getting current user:', error);
      // Return basic user info even if profile check fails
      return {
        ...user,
        role: user.roles?.[0] || null,
        needsOnboarding: false,
        error: 'Failed to check onboarding status',
      };
    }
  }

  @Get('debug-auth0')
  @UseGuards(Auth0Guard)
  @ApiOperation({ summary: 'Debug Auth0 user data' })
  @ApiResponse({
    status: 200,
    description: 'Returns detailed Auth0 user information for debugging',
  })
  async debugAuth0(@GetUser() user: User) {
    this.logger.log('Auth0 user data debug request received');

    // Log the full user object for debugging
    this.logger.debug('Full Auth0 user object:', JSON.stringify(user, null, 2));

    // Extract and organize the data for better visibility
    const userData = {
      // Basic user info
      sub: user.sub,
      userId: user.userId,
      email: user.email,
      name: user.name,
      given_name: user.given_name,
      family_name: user.family_name,
      nickname: user.nickname,
      fullName: user.fullName,
      firstName: user.firstName,
      lastName: user.lastName,
      picture: user.picture,

      // LinkedIn specific data
      linkedInProfile: user.linkedInProfile,

      // Provider info
      provider: user.provider,

      // Additional fields
      locale: user.locale,
      roles: user.roles,

      // Metadata
      user_metadata: user.user_metadata,
      app_metadata: user.app_metadata,
    };

    return {
      message: 'Auth0 user data for debugging',
      userData,
      missingFields: {
        firstName: !user.given_name && (!user.name || user.name === 'undefined undefined'),
        lastName: !user.family_name && (!user.name || user.name === 'undefined undefined'),
        email: !user.email,
      },
      linkedInData: {
        available: !!user.linkedInProfile,
        fields: user.linkedInProfile ? Object.keys(user.linkedInProfile) : [],
        profileFields: user.linkedInProfile?.profile
          ? Object.keys(user.linkedInProfile.profile)
          : [],
      },
      recommendations: {
        auth0Configuration: [
          'Ensure LinkedIn connection has r_emailaddress, r_liteprofile, and r_basicprofile scopes',
          'Check Auth0 Rules to ensure user profile enrichment',
          'Verify Auth0 Action for the Login flow to extract LinkedIn data',
        ],
        applicationConfiguration: [
          'Update Auth0 callback handler to extract more data from ID token',
          'Consider implementing a custom LinkedIn OAuth flow for more data',
        ],
      },
    };
  }

  @Post('verify-token')
  @Public()
  @ApiOperation({ summary: 'Verify JWT token' })
  @ApiResponse({ status: 200, description: 'Token is valid' })
  @ApiResponse({ status: 401, description: 'Token is invalid' })
  async verifyToken(@Req() req: Request & { headers: { authorization?: string } }) {
    const authHeader = req.headers.authorization;
    if (!authHeader) {
      return { valid: false, message: 'No authorization header provided' };
    }

    const token = authHeader.split(' ')[1];
    if (!token) {
      return { valid: false, message: 'No token provided' };
    }

    try {
      const decoded = await this.authService.verifyToken(token);
      return { valid: true, user: decoded };
    } catch (error: any) {
      return { valid: false, message: error.message };
    }
  }

  @Get('create-job-seeker-profile')
  @UseGuards(Auth0Guard)
  @ApiOperation({ summary: 'Create a job seeker profile if one does not exist' })
  @ApiResponse({ status: 200, description: 'Profile created or already exists' })
  async createJobSeekerProfile(@GetUser() user: User) {
    try {
      // Use the core auth service to create a job seeker profile
      const profile = await this.coreAuthService.createJobSeekerProfileIfNotExists(user);

      if (profile) {
        return {
          success: true,
          message: 'Job seeker profile created or already exists',
          profile,
        };
      } else {
        return {
          success: false,
          message: 'Failed to create job seeker profile',
        };
      }
    } catch (error: any) {
      this.logger.error(`Error creating job seeker profile: ${error.message}`, error.stack);
      return {
        success: false,
        message: `Error creating job seeker profile: ${error.message}`,
      };
    }
  }

  @Post('process-pending-data')
  @UseGuards(Auth0Guard)
  @ApiOperation({ summary: 'Process pending registration data after Auth0 authentication' })
  @ApiResponse({ status: 200, description: 'Pending data processed successfully' })
  async processPendingData(@GetUser() user: User, @Req() req: any) {
    try {
      const { pendingData, dataType } = req.body;

      if (!pendingData || !dataType) {
        return {
          success: false,
          message: 'No pending data to process',
        };
      }

      let result;

      switch (dataType) {
        case 'company':
          result = await this.processCompanyData(user, pendingData);
          break;
        case 'jobSeeker':
          result = await this.processJobSeekerData(user, pendingData);
          break;
        case 'graduate':
          result = await this.processGraduateData(user, pendingData);
          break;
        default:
          throw new Error(`Unknown data type: ${dataType}`);
      }

      return {
        success: true,
        message: 'Pending data processed successfully',
        data: result,
      };
    } catch (error: any) {
      this.logger.error('Error processing pending data:', error);
      return {
        success: false,
        message: 'Error processing pending data',
        error: error.message,
      };
    }
  }

  @Get('userinfo')
  @UseGuards(Auth0Guard)
  @ApiOperation({ summary: 'Fetch user info directly from Auth0' })
  @ApiResponse({ status: 200, description: 'User info from Auth0' })
  async getUserInfo(@GetUser() user: User, @Req() req: any) {
    try {
      // Extract the authorization header
      const authHeader = req.headers?.authorization;
      const token = authHeader ? authHeader.split(' ')[1] : user.accessToken;

      if (!token) {
        return {
          success: false,
          message: 'No access token available',
        };
      }

      // Get Auth0 domain from config
      const auth0Domain = this.configService.get('AUTH0_ISSUER')?.replace(/\/+$/, '');

      // Fetch user info from Auth0 userinfo endpoint
      const response = await axios.get(`https://${auth0Domain}/userinfo`, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });

      return {
        success: true,
        message: 'User info fetched from Auth0',
        userInfo: response.data,
        tokenPayload: user,
      };
    } catch (error: any) {
      this.logger.error(`Error fetching user info from Auth0: ${error.message}`, error.stack);
      return {
        success: false,
        message: `Error fetching user info: ${error.message}`,
        user: user,
      };
    }
  }

  private async processCompanyData(user: User, companyData: any) {
    this.logger.log(`Processing company data for user ${user.userId}`);

    // Add the user's clientId to the company data
    const enhancedCompanyData = {
      ...companyData,
      clientId: user.userId,
      userId: user.userId,
      // Use Auth0 data as fallback for contact information
      contactEmail: companyData.contactEmail || user.email,
      contactName: companyData.contactName || user.name,
    };

    try {
      // Create or update company via the company service
      const company = await this.companyService.createOrUpdate(enhancedCompanyData);
      this.logger.log(`Successfully processed company data for user ${user.userId}`);
      return company;
    } catch (error) {
      this.logger.error(`Error processing company data for user ${user.userId}:`, error);
      throw error;
    }
  }

  private async processJobSeekerData(user: User, jobSeekerData: any) {
    this.logger.log(`Processing job seeker data for user ${user.userId}`);

    // Add the user's information to the job seeker data
    const enhancedJobSeekerData = {
      ...jobSeekerData,
      clientId: user.userId,
      userId: user.userId,
      // Use Auth0 data as fallback
      email: jobSeekerData.email || user.email,
      firstName: jobSeekerData.firstName || user.given_name || user.name?.split(' ')[0],
      lastName:
        jobSeekerData.lastName || user.family_name || user.name?.split(' ').slice(1).join(' '),
      myProfileImage: jobSeekerData.myProfileImage || user.picture,
    };

    try {
      // Create or update job seeker via the job seeker service
      const jobSeeker = await this.jobSeekerService.create(enhancedJobSeekerData);
      this.logger.log(`Successfully processed job seeker data for user ${user.userId}`);
      return jobSeeker;
    } catch (error) {
      this.logger.error(`Error processing job seeker data for user ${user.userId}:`, error);
      throw error;
    }
  }

  private async processGraduateData(user: User, graduateData: any) {
    this.logger.log(`Processing graduate data for user ${user.userId}`);

    // Graduates use the same structure as job seekers but with graduate role
    const enhancedGraduateData = {
      ...graduateData,
      clientId: user.userId,
      userId: user.userId,
      role: 'graduate',
      // Use Auth0 data as fallback
      email: graduateData.email || user.email,
      firstName: graduateData.firstName || user.given_name || user.name?.split(' ')[0],
      lastName:
        graduateData.lastName || user.family_name || user.name?.split(' ').slice(1).join(' '),
      myProfileImage: graduateData.myProfileImage || user.picture,
    };

    try {
      // Create or update graduate profile via the job seeker service (same structure)
      const graduate = await this.jobSeekerService.create(enhancedGraduateData);
      this.logger.log(`Successfully processed graduate data for user ${user.userId}`);
      return graduate;
    } catch (error) {
      this.logger.error(`Error processing graduate data for user ${user.userId}:`, error);
      throw error;
    }
  }

  @Post('sync-role')
  @UseGuards(Auth0Guard)
  @ApiOperation({ summary: 'Sync user role to Auth0 app_metadata' })
  @ApiResponse({ status: 200, description: 'Role synced successfully' })
  async syncRole(@GetUser() user: User, @Body() body: { role: string }) {
    try {
      const { role } = body;

      if (!role) {
        return {
          success: false,
          message: 'Role is required',
        };
      }

      // Validate role
      const validRoles = [
        'employer',
        'job-seeker',
        'graduate',
        'admin',
        'super-admin',
        'referral-partner',
      ];
      if (!validRoles.includes(role)) {
        return {
          success: false,
          message: 'Invalid role provided',
        };
      }

      this.logger.log(`Syncing role ${role} to Auth0 for user ${user.userId}`);

      // Update role in Auth0 app_metadata
      await this.coreAuthService.updateUserRole(user.userId, role);

      // Also ensure role exists in our database
      const rolesService = this.authService.getRolesService();
      const userRole = await rolesService.findByClientId(user.userId);

      if (!userRole) {
        // Create role in database
        await rolesService.create({
          clientId: user.userId,
          role: role as any,
        });
      } else if (userRole.role !== role) {
        // For security, don't allow role changes through this endpoint
        // Role changes should go through a proper role change flow
        this.logger.warn(
          `Attempted to change role for user ${user.userId} from ${userRole.role} to ${role}`,
        );
        return {
          success: false,
          message:
            'User already has a different role. Role changes are not allowed through this endpoint.',
          currentRole: userRole.role,
          requestedRole: role,
        };
      }
      // If roles match, that's fine - just a duplicate sync request

      return {
        success: true,
        message: 'Role synced successfully',
        role: role,
      };
    } catch (error: any) {
      this.logger.error(`Error syncing role: ${error.message}`, error.stack);
      return {
        success: false,
        message: 'Error syncing role',
        error: error.message,
      };
    }
  }

  @Post('merge-temp-user')
  @UseGuards(Auth0Guard)
  @ApiOperation({ summary: 'Merge temporary user data with authenticated Auth0 user' })
  @ApiResponse({ status: 200, description: 'Temporary user merged successfully' })
  async mergeTempUser(@GetUser() user: User, @Req() req: any) {
    try {
      const { tempUserId } = req.body;

      if (!tempUserId) {
        return {
          success: false,
          message: 'Temporary user ID is required',
        };
      }

      this.logger.log(`Merging temporary user ${tempUserId} with Auth0 user ${user.userId}`);

      // Try to find temporary job seeker profile first
      let tempJobSeeker;
      try {
        tempJobSeeker = await this.jobSeekerService.getByClientId(tempUserId);
      } catch (error) {
        // Job seeker not found, that's okay
      }

      // Try to find temporary company profile
      let tempCompany;
      try {
        tempCompany = await this.companyService.findByClientIdOrNull(tempUserId);
      } catch (error) {
        // Company not found, that's okay
      }

      if (!tempJobSeeker && !tempCompany) {
        return {
          success: false,
          message: 'No temporary profile found',
        };
      }

      let mergedData;

      if (tempJobSeeker) {
        // Merge job seeker profile
        mergedData = await this.jobSeekerService.update(tempJobSeeker.id, {
          clientId: user.userId,
          userId: user.userId,
          // Use Auth0 data as fallback for missing fields
          email: tempJobSeeker.email || user.email,
          firstName: tempJobSeeker.firstName || user.given_name || user.name?.split(' ')[0] || '',
          lastName:
            tempJobSeeker.lastName ||
            user.family_name ||
            user.name?.split(' ').slice(1).join(' ') ||
            '',
          myProfileImage: tempJobSeeker.myProfileImage || user.picture,
        });
      }

      if (tempCompany) {
        // Merge company profile
        mergedData = await this.companyService.update(tempCompany.id, {
          clientId: user.userId,
          // Use Auth0 data as fallback for missing fields
          contactEmail: tempCompany.contactEmail || user.email,
          contactName: tempCompany.contactName || user.name || '',
        });
      }

      this.logger.log(
        `Successfully merged temporary user ${tempUserId} with Auth0 user ${user.userId}`,
      );

      return {
        success: true,
        message: 'Temporary user merged successfully',
        data: mergedData,
      };
    } catch (error: any) {
      this.logger.error('Error merging temporary user:', error);
      return {
        success: false,
        message: 'Error merging temporary user',
        error: error.message,
      };
    }
  }
}
