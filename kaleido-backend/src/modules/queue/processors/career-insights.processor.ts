import { Process, Processor } from '@nestjs/bull';
import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Job } from 'bull';
import { Repository } from 'typeorm';
import { OpenaiService } from '../../../shared/services/openai.service';
import { GroqService } from '../../../shared/services/groq.service';
import {
  CareerInsight,
  InsightStatus,
  InsightType,
} from '../../career-insights/entities/career-insight.entity';
import { CareerInsightJobData } from './career-insights/types';
import { AIService } from './career-insights/ai-service';
import {
  SkillGapProcessor,
  CareerPathProcessor,
  MarketTrendsProcessor,
  SalaryInsightsProcessor,
  RoleTransitionProcessor,
  IndustryOutlookProcessor,
} from './career-insights/processors';

@Processor('career-insights')
@Injectable()
export class CareerInsightsProcessor {
  private readonly logger = new Logger(CareerInsightsProcessor.name);
  private readonly aiService: AIService;
  private readonly skillGapProcessor: SkillGapProcessor;
  private readonly careerPathProcessor: CareerPathProcessor;
  private readonly marketTrendsProcessor: MarketTrendsProcessor;
  private readonly salaryInsightsProcessor: SalaryInsightsProcessor;
  private readonly roleTransitionProcessor: RoleTransitionProcessor;
  private readonly industryOutlookProcessor: IndustryOutlookProcessor;

  constructor(
    @InjectRepository(CareerInsight)
    private readonly careerInsightRepository: Repository<CareerInsight>,
    private readonly openaiService: OpenaiService,
    private readonly groqService: GroqService,
  ) {
    this.aiService = new AIService(openaiService, groqService);
    this.skillGapProcessor = new SkillGapProcessor(this.aiService);
    this.careerPathProcessor = new CareerPathProcessor(this.aiService);
    this.marketTrendsProcessor = new MarketTrendsProcessor(this.aiService);
    this.salaryInsightsProcessor = new SalaryInsightsProcessor(this.aiService);
    this.roleTransitionProcessor = new RoleTransitionProcessor(this.aiService);
    this.industryOutlookProcessor = new IndustryOutlookProcessor(this.aiService);
  }

  @Process('process-insight')
  async processInsight(job: Job<CareerInsightJobData>) {
    const { insightId, userId, insightType, params } = job.data;
    this.logger.log(`Processing career insight ${insightId} of type ${insightType}`);

    try {
      // Update progress
      await job.progress(10);

      // Fetch the insight entity
      const insight = await this.careerInsightRepository.findOne({
        where: { id: insightId },
        relations: ['jobSeeker'],
      });

      if (!insight) {
        throw new Error(`Career insight not found: ${insightId}`);
      }

      // Update status to processing
      await this.careerInsightRepository.update(insightId, {
        status: InsightStatus.PROCESSING,
      });

      await job.progress(20);

      // Process based on insight type
      let result: any;
      switch (insightType) {
        case InsightType.SKILL_GAP_ANALYSIS:
          result = await this.skillGapProcessor.process(insight, params);
          break;
        case InsightType.CAREER_PATH_RECOMMENDATION:
          result = await this.careerPathProcessor.process(insight, params);
          break;
        case InsightType.MARKET_TREND_ANALYSIS:
          result = await this.marketTrendsProcessor.process(insight, params);
          break;
        case InsightType.COMPENSATION_BENCHMARK:
          result = await this.salaryInsightsProcessor.process(insight, params);
          break;
        case InsightType.ROLE_TRANSITION_GUIDANCE:
          result = await this.roleTransitionProcessor.process(insight, params);
          break;
        case InsightType.INDUSTRY_OUTLOOK:
          result = await this.industryOutlookProcessor.process(insight, params);
          break;
        default:
          throw new Error(`Unknown insight type: ${insightType}`);
      }

      await job.progress(80);

      // Update the insight with results
      await this.careerInsightRepository.update(insightId, {
        status: InsightStatus.READY,
        aiInsights: result.aiInsights,
        skillGapAnalysis: result.skillGapAnalysis,
        careerPathRecommendation: result.careerPathRecommendation,
        marketTrendAnalysis: result.marketTrendAnalysis,
        compensationBenchmark: result.compensationBenchmark,
        roleTransitionGuidance: result.roleTransitionGuidance,
        detailedAnalysis: result.detailedAnalysis,
      });

      await job.progress(100);

      this.logger.log(`Successfully processed career insight ${insightId}`);
      return { success: true, insightId };
    } catch (error) {
      this.logger.error(`Failed to process career insight ${insightId}:`, error);

      // Update status to failed
      await this.careerInsightRepository.update(insightId, {
        status: InsightStatus.ARCHIVED,
        // metadata: { error: error.message },
      });

      throw error;
    }
  }
}
