import { Process, Processor } from '@nestjs/bull';
import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Job } from 'bull';
import { Repository } from 'typeorm';

import { PROCESSOR_NAMES, QUEUE_NAMES } from '@/shared/constants/queue.constants';
import { CandidateComparison } from '@/modules/comparison/entities/candidate-comparison.entity';
import { CandidateComparisonService } from '@/modules/comparison/services/candidate-comparison.service';

interface ComparisonJobData {
  comparisonId: string;
  userId: string;
}

@Injectable()
@Processor(QUEUE_NAMES.CANDIDATE_COMPARISON)
export class CandidateComparisonProcessor {
  private readonly logger = new Logger(CandidateComparisonProcessor.name);

  constructor(
    @InjectRepository(CandidateComparison)
    private readonly comparisonRepository: Repository<CandidateComparison>,
    private readonly comparisonService: CandidateComparisonService,
  ) {}

  @Process(PROCESSOR_NAMES.PROCESS_COMPARISON)
  async processComparison(job: Job<ComparisonJobData>) {
    const { comparisonId, userId } = job.data;

    this.logger.log(`Starting comparison processing for ID: ${comparisonId}`);

    try {
      // Update progress
      await job.progress(10);

      // Get the comparison entity
      const comparison = await this.comparisonRepository.findOne({
        where: { id: comparisonId, clientId: userId },
      });

      if (!comparison) {
        throw new Error(`Comparison ${comparisonId} not found`);
      }

      await job.progress(20);

      // Process the comparison using the service
      const result = await this.comparisonService.processComparison(comparison);

      await job.progress(90);

      // Update the comparison status to completed
      await this.comparisonRepository.update(comparisonId, {
        status: 'completed',
        comparisonResults: result,
        updatedAt: new Date(),
      });

      await job.progress(100);

      this.logger.log(`Comparison ${comparisonId} completed successfully`);

      return {
        comparisonId,
        status: 'completed',
        result,
      };
    } catch (error) {
      this.logger.error(`Error processing comparison ${comparisonId}:`, error);

      // Update the comparison status to failed
      await this.comparisonRepository.update(comparisonId, {
        status: 'failed',
        updatedAt: new Date(),
      });

      throw error;
    }
  }
}
