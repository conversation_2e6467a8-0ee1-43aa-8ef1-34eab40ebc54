import { Logger } from '@nestjs/common';
import { OpenaiService } from '../../../../shared/services/openai.service';
import { GroqService } from '../../../../shared/services/groq.service';
import { GenerateAIResponseOptions } from './types';

export class AIService {
  private readonly logger = new Logger(AIService.name);

  constructor(
    private readonly openaiService: OpenaiService,
    private readonly groqService: GroqService,
  ) {}

  async generateResponse(prompt: string, options?: GenerateAIResponseOptions): Promise<string> {
    try {
      const { preferGroq = false, requireHighQuality = false, maxTokens = 2000 } = options || {};

      // Use OpenAI for high-quality requirements or complex analysis
      // Use Groq for faster processing when quality requirements are lower
      const useGroq = preferGroq && !requireHighQuality && this.groqService.isAvailable();

      if (useGroq) {
        this.logger.log('Using Groq for faster processing');
        // Use a custom prompt for career insights
        const careerPrompt = `You are a professional career advisor providing detailed, actionable insights.

${prompt}

Please provide comprehensive and practical advice.`;

        const groqResponse = await this.groqService.generateContent('summary', careerPrompt);
        return groqResponse || '';
      } else {
        this.logger.log('Using OpenAI for high-quality insights');
        const response = await this.openaiService.openai.chat.completions.create({
          model: 'gpt-4o-mini',
          messages: [
            {
              role: 'system',
              content:
                'You are a professional career advisor providing detailed, actionable insights.',
            },
            {
              role: 'user',
              content: prompt,
            },
          ],
          temperature: 0.7,
          max_tokens: maxTokens,
        });

        return response.choices[0]?.message?.content || '';
      }
    } catch (error) {
      this.logger.error('Error generating AI response:', error);
      // Fallback to the other service if one fails
      if (options?.preferGroq && this.groqService.isAvailable()) {
        this.logger.warn('Groq failed, falling back to OpenAI');
        return this.generateResponse(prompt, { ...options, preferGroq: false });
      }
      throw new Error('Failed to generate AI response');
    }
  }
}
