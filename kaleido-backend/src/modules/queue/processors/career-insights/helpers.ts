import { CareerHistory, LabelMaps } from './types';

export class CareerInsightHelpers {
  private static labelMaps: LabelMaps = {
    roleType: {
      engineering: 'Engineering',
      product: 'Product Management',
      design: 'Design',
      marketing: 'Marketing',
      sales: 'Sales',
      operations: 'Operations',
      hr: 'Human Resources',
      finance: 'Finance',
      other: 'Other',
    },
    experienceLevel: {
      entry: 'Entry Level',
      mid: 'Mid Level',
      senior: 'Senior Level',
      lead: 'Lead/Principal',
      manager: 'Manager',
      director: 'Director+',
    },
    timeline: {
      '3months': 'Within 3 months',
      '6months': '3-6 months',
      '12months': '6-12 months',
      '12plus': 'More than 1 year',
    },
    skillCategories: {
      technical: 'Technical Skills',
      leadership: 'Leadership',
      communication: 'Communication',
      strategic: 'Strategic Thinking',
      business: 'Business Acumen',
      project: 'Project Management',
      data: 'Data Analysis',
      design: 'Design Thinking',
    },
    aiConcern: {
      'very-concerned': 'Very concerned',
      'somewhat-concerned': 'Somewhat concerned',
      neutral: 'Neutral',
      optimistic: 'Optimistic',
      'very-optimistic': 'Very optimistic',
      'dont-know': 'Needs information',
    },
  };

  static getRoleTypeLabel(roleType: string): string {
    return this.labelMaps.roleType[roleType] || roleType;
  }

  static getExperienceLevelLabel(level: string): string {
    return this.labelMaps.experienceLevel[level] || level;
  }

  static getTimelineLabel(timeline: string): string {
    return this.labelMaps.timeline[timeline] || timeline;
  }

  static getSkillCategoriesLabels(categories: string[]): string[] {
    return categories.map((cat) => this.labelMaps.skillCategories[cat] || cat);
  }

  static getAIConcernLevel(concern: string | undefined): string {
    if (!concern) return 'Not specified';
    return this.labelMaps.aiConcern[concern] || 'Not specified';
  }

  static inferIndustryFromSkills(skills: string[]): string {
    const skillsLower = skills.map((s) => s.toLowerCase());

    if (
      skillsLower.some(
        (s) =>
          s.includes('react') ||
          s.includes('angular') ||
          s.includes('vue') ||
          s.includes('javascript'),
      )
    ) {
      return 'Technology - Web Development';
    }
    if (
      skillsLower.some(
        (s) => s.includes('python') || s.includes('machine learning') || s.includes('data science'),
      )
    ) {
      return 'Technology - Data Science/AI';
    }
    if (
      skillsLower.some((s) => s.includes('aws') || s.includes('docker') || s.includes('kubernetes'))
    ) {
      return 'Technology - Cloud/DevOps';
    }
    if (
      skillsLower.some((s) => s.includes('marketing') || s.includes('seo') || s.includes('content'))
    ) {
      return 'Marketing & Communications';
    }
    if (
      skillsLower.some(
        (s) => s.includes('finance') || s.includes('accounting') || s.includes('excel'),
      )
    ) {
      return 'Finance & Accounting';
    }
    if (
      skillsLower.some(
        (s) => s.includes('sales') || s.includes('crm') || s.includes('business development'),
      )
    ) {
      return 'Sales & Business Development';
    }

    return 'General Business';
  }

  static buildCareerHistory(experience: any[]): CareerHistory[] {
    return experience.map((exp: any) => ({
      role: exp.title,
      company: exp.company,
      duration:
        exp.startDate && exp.endDate
          ? `${new Date(exp.startDate).getFullYear()} - ${exp.endDate ? new Date(exp.endDate).getFullYear() : 'Present'}`
          : 'Duration unknown',
      responsibilities: exp.description || 'Not specified',
    }));
  }

  static calculateTotalExperience(experience: any[]): number {
    return experience.reduce((total: number, exp: any) => {
      if (exp.startDate) {
        const start = new Date(exp.startDate);
        const end = exp.endDate ? new Date(exp.endDate) : new Date();
        const years =
          end.getFullYear() - start.getFullYear() + (end.getMonth() - start.getMonth()) / 12;
        return total + years;
      }
      return total;
    }, 0);
  }

  static formatEducation(education: any[]): string {
    return (
      education
        .map((edu: any) => `- ${edu.degree} in ${edu.field} from ${edu.institution}`)
        .join('\n') || 'No education listed'
    );
  }

  static formatExperience(experience: any[], limit?: number): string {
    const experienceList = limit ? experience.slice(0, limit) : experience;
    return (
      experienceList
        .map(
          (exp: any) =>
            `- ${exp.title} at ${exp.company} (${exp.startDate ? new Date(exp.startDate).getFullYear() : 'Unknown'} - ${exp.endDate ? new Date(exp.endDate).getFullYear() : 'Present'})`,
        )
        .join('\n') || 'No experience listed'
    );
  }

  static formatCertifications(certifications: any[]): string {
    return certifications.map((cert: any) => cert.name).join(', ') || 'None';
  }

  static formatSkills(skills: string[], limit?: number): string {
    const skillsList = limit ? skills.slice(0, limit) : skills;
    return skillsList.join(', ') || 'None listed';
  }

  static formatLanguages(languages: string[]): string {
    return languages.join(', ') || 'Not specified';
  }

  static formatValues(values: string[]): string {
    return values.join(', ') || 'Not specified';
  }

  static formatCompensationPreferences(compensation: any): string {
    if (!compensation) return 'No compensation preferences specified';

    return `
    - Expected Salary: ${compensation.expectedSalary || 'Not specified'}
    - Minimum Acceptable: ${compensation.minimumSalary || 'Not specified'}
    - Preferred Currency: ${compensation.currency || 'USD'}
    - Benefits Priority: ${compensation.benefitsPriority || 'Not specified'}
    `;
  }

  static formatWorkAvailability(workAvailability: any): string {
    if (!workAvailability) return 'Standard full-time availability';

    return `
    - Type: ${workAvailability.type || 'Full-time'}
    - Remote Preference: ${workAvailability.remotePreference || 'Flexible'}
    - Start Date: ${workAvailability.startDate || 'Immediately'}
    `;
  }

  static formatAchievements(achievements: any[]): string {
    return (
      achievements.map((ach: any) => `- ${ach.title}: ${ach.description}`).join('\n') ||
      'No achievements listed'
    );
  }

  static extractCurrentRole(experience: any[]): string {
    return experience[0]?.title || 'Entry level';
  }

  static extractCurrentCompany(experience: any[]): string {
    return experience[0]?.company || 'Not specified';
  }

  static extractCurrentIndustry(experience: any[]): string {
    return experience[0]?.industry || 'Not specified';
  }

  static getIndustryFromProfile(jobSeekerProfile: any, defaultIndustry?: string): string {
    return (
      defaultIndustry ||
      jobSeekerProfile.experience[0]?.industry ||
      this.inferIndustryFromSkills(jobSeekerProfile.skills)
    );
  }

  static countIndustryRoles(experience: any[], industry: string): number {
    return experience.filter((exp: any) => exp.industry === industry).length;
  }

  static getEducationField(education: any[]): string {
    return education[0]?.field || 'Not specified';
  }

  static getHighestDegree(education: any[]): string {
    if (!education[0]) return 'Not specified';
    return `${education[0].degree} in ${education[0].field}`;
  }
}
