export interface JobSeekerProfile {
  id: string;
  firstName: string;
  lastName: string;
  email: string;
  location?: string;
  summary?: string;
  skills: string[];
  experience: any[];
  education: any[];
  certifications: any[];
  languages: string[];
  myValues: string[];
  achievements: any[];
  recommendations: any[];
  preferences?: any;
  workAvailability?: any;
  compensation?: any;
  linkedinUrl?: string;
  githubUrl?: string;
  portfolioUrl?: string;
  calculatedExperienceLevel: string;
}

export interface CareerInsightJobData {
  insightId: string;
  userId: string;
  insightType: string;
  params: {
    targetRole?: string;
    currentRole?: string;
    industry?: string;
    location?: string;
    experienceLevel?: string;
    formData?: Record<string, string | string[]>;
    jobSeekerProfile: JobSeekerProfile;
  };
}

export interface ParsedSkillGapResponse {
  summary: string;
  detailedAnalysis: string;
  strengths: string[];
  opportunities: string[];
  challenges: string[];
  recommendations: any[];
  confidenceScore: number;
  skillGaps: any[];
  onlineResources: any[];
  readinessScore: number;
  timeEstimate: string;
}

export interface ParsedCareerPathResponse {
  summary: string;
  detailedAnalysis: string;
  strengths: string[];
  opportunities: string[];
  challenges: string[];
  recommendations: any[];
  confidenceScore: number;
  paths: any[];
  immediateSteps: string[];
}

export interface ParsedMarketTrendsResponse {
  summary: string;
  detailedAnalysis: string;
  industryOverview: string;
  strengths: string[];
  opportunities: string[];
  challenges: string[];
  recommendations: any[];
  confidenceScore: number;
  emergingTrends: any[];
  decliningAreas: any[];
  marketOpportunities: any[];
}

export interface ParsedSalaryResponse {
  summary: string;
  detailedAnalysis: string;
  strengths: string[];
  opportunities: string[];
  challenges: string[];
  recommendations: any[];
  confidenceScore: number;
  range: { min: number; max: number; median: number } | null;
  percentile: number | null;
  compensationFactors: any[];
  tips: string[];
  benefitsComparison: {
    standard: string[];
    premium: string[];
  };
  geographicVariation?: any;
}

export interface ParsedRoleTransitionResponse {
  summary: string;
  detailedAnalysis: string;
  strengths: string[];
  opportunities: string[];
  challenges: string[];
  recommendations: any[];
  confidenceScore: number;
  difficulty: string;
  transferableSkills: string[];
  newSkills: any[];
  plan: any[];
  successStories?: any;
}

export interface ParsedIndustryOutlookResponse {
  summary: string;
  keyFindings: string[];
  recommendations: any[];
  detailedAnalysis: string;
}

export interface ParsedInterviewResponse {
  summary: string;
  detailedAnalysis: string;
  keyFindings: string[];
  recommendations: any[];
  behavioral: string[];
  technical: string[];
  scenarios: string[];
  toAsk: string[];
  tips: string[];
}

export interface ParsedResumeResponse {
  summary: string;
  detailedAnalysis: string;
  keyFindings: string[];
  recommendations: any[];
  keywords: string[];
  skills: string[];
  format: string[];
  improvements: string[];
}

export interface CareerHistory {
  role: string;
  company: string;
  duration: string;
  responsibilities: string;
}

export interface SkillGap {
  skillName: string;
  currentLevel: 'BEGINNER' | 'INTERMEDIATE' | 'ADVANCED';
  targetLevel: 'BEGINNER' | 'INTERMEDIATE' | 'ADVANCED';
  priority: 'HIGH' | 'MEDIUM' | 'LOW';
  learningResources: any[];
  timeToAchieve: string;
  marketDemand: number;
}

export interface OnlineResource {
  title: string;
  type: 'COURSE' | 'TUTORIAL' | 'CERTIFICATION' | 'BOOTCAMP' | 'WORKSHOP';
  provider: string;
  url: string;
  estimatedDuration: string;
  cost: string;
}

export interface Recommendation {
  priority: 'HIGH' | 'MEDIUM' | 'LOW';
  action: string;
  expectedOutcome: string;
  timeframe: string;
}

export interface MarketTrend {
  trend: string;
  impact: 'POSITIVE' | 'NEGATIVE';
  relevanceScore: number;
  description: string;
  dataSource: string;
}

export interface MarketOpportunity {
  title: string;
  description: string;
  readinessScore: number;
  requiredPreparation: string[];
}

export interface CompensationFactor {
  factor: string;
  impact: 'POSITIVE' | 'NEGATIVE';
  weight: number;
}

export interface TransitionPlan {
  phase: string;
  duration: string;
  actions: string[];
  milestones: string[];
}

export interface GenerateAIResponseOptions {
  preferGroq?: boolean;
  requireHighQuality?: boolean;
  maxTokens?: number;
}

export interface LabelMaps {
  roleType: Record<string, string>;
  experienceLevel: Record<string, string>;
  timeline: Record<string, string>;
  skillCategories: Record<string, string>;
  aiConcern: Record<string, string>;
}
