import { Logger } from '@nestjs/common';
import { CareerInsight } from '../../../../career-insights/entities/career-insight.entity';
import { AIService } from '../ai-service';
import { ResponseParser } from '../parsers';
import { CareerInsightHelpers } from '../helpers';

export class SkillGapProcessor {
  private readonly logger = new Logger(SkillGapProcessor.name);
  private readonly parser = new ResponseParser();

  constructor(private readonly aiService: AIService) {}

  async process(insight: CareerInsight, params: any) {
    const { targetRole, jobSeekerProfile, formData } = params;

    // Extract structured data from form
    let targetRoleFromForm = formData?.targetRole || targetRole;
    const targetRoleType = formData?.targetRoleType;
    const targetLevel = formData?.targetLevel;
    const skillCategories = formData?.skillCategories || [];
    const timeline = formData?.timeline;

    // Handle special cases for target role
    const isUnsureAboutRole =
      targetRoleFromForm === 'dont-know' || targetRoleFromForm === "I'm not sure yet";
    if (targetRoleFromForm === 'use-current') {
      targetRoleFromForm = jobSeekerProfile.experience[0]?.title || 'Current role progression';
    } else if (isUnsureAboutRole) {
      targetRoleFromForm = 'career advancement opportunities';
    }

    const prompt = `Analyze the skill gap for a job seeker ${isUnsureAboutRole ? 'who is unsure about their next career step' : `transitioning to ${targetRoleFromForm}`}.

    ${isUnsureAboutRole ? 'IMPORTANT: The job seeker is unsure about their career direction. Provide guidance on exploring career options and discovering suitable paths based on their current skills and interests.' : ''}

    Target Role Details:
    - Role Title: ${isUnsureAboutRole ? 'Exploring multiple options' : targetRoleFromForm}
    - Role Category: ${targetRoleType ? CareerInsightHelpers.getRoleTypeLabel(targetRoleType as string) : 'Not specified'}
    - Target Level: ${targetLevel ? CareerInsightHelpers.getExperienceLevelLabel(targetLevel as string) : 'Not specified'}
    - Timeline Goal: ${timeline ? CareerInsightHelpers.getTimelineLabel(timeline as string) : 'Not specified'}
    - Focus Areas: ${Array.isArray(skillCategories) ? CareerInsightHelpers.getSkillCategoriesLabels(skillCategories as string[]).join(', ') : 'General'}

    Job Seeker Profile:
    - Name: ${jobSeekerProfile.firstName} ${jobSeekerProfile.lastName}
    - Current Location: ${jobSeekerProfile.location || 'Not specified'}
    - Experience Level: ${jobSeekerProfile.calculatedExperienceLevel}
    - Current Skills: ${CareerInsightHelpers.formatSkills(jobSeekerProfile.skills)}

    Professional Summary: ${jobSeekerProfile.summary || 'Not provided'}

    Work Experience:
    ${CareerInsightHelpers.formatExperience(jobSeekerProfile.experience)}

    Education:
    ${CareerInsightHelpers.formatEducation(jobSeekerProfile.education)}

    Certifications: ${CareerInsightHelpers.formatCertifications(jobSeekerProfile.certifications)}
    Languages: ${CareerInsightHelpers.formatLanguages(jobSeekerProfile.languages)}
    Personal Values: ${CareerInsightHelpers.formatValues(jobSeekerProfile.myValues)}

    Based on the specific focus areas and timeline, provide a comprehensive skill gap analysis.
    
    IMPORTANT: Structure your response to include the following sections:

    STRENGTHS (3-5 bullet points):
    - Start each with "You have..." or "Your..."
    - Focus on existing skills and experience that are valuable

    OPPORTUNITIES (3-5 bullet points):
    - Start each with "There is..." or "The market..."
    - Focus on market trends and growth areas

    CHALLENGES (3-5 bullet points):
    - Start each with a clear challenge statement
    - Be honest but constructive

    RECOMMENDATIONS (3-6 actionable items):
    For each recommendation include:
    - Action: Start with "You should..." (be specific and actionable)
    - Priority: HIGH, MEDIUM, or LOW
    - Expected Outcome: What they will achieve
    - Timeframe: Specific duration (e.g., "3 months", "6 months")

    SKILL GAPS:
    For each identified skill gap, provide:
    - Skill Name
    - Current Level: BEGINNER, INTERMEDIATE, or ADVANCED
    - Target Level: BEGINNER, INTERMEDIATE, or ADVANCED
    - Priority: HIGH, MEDIUM, or LOW
    - Time to Achieve: Specific duration
    - Market Demand: Percentage (0-100)

    ONLINE RESOURCES:
    For each skill gap, suggest 2-3 specific learning resources with:
    - Title: Name of the course/resource
    - Type: COURSE, TUTORIAL, CERTIFICATION, BOOTCAMP, or WORKSHOP
    - Provider: Company/platform name
    - URL: Actual link to the resource
    - Estimated Duration: How long to complete
    - Cost: Free, $ (under $50), $$ ($50-200), $$$ (over $200)

    ASSESSMENT SCORES:
    - Confidence Score: Your confidence in this analysis (0-100%)
    - Readiness Score: How ready they are for the transition (0-100%)
    - Time Estimate: Overall time to close skill gaps (e.g., "3-6 months")

    ${
      isUnsureAboutRole
        ? `
    Additional focus areas for career exploration:
    1. Top 3-4 career paths that align with their current skills
    2. Specific roles they should research and consider
    3. Skills that are transferable across multiple paths
    4. Resources for career discovery and self-assessment
    `
        : `
    Specific analysis for ${targetRoleFromForm} role:
    1. Required skills at ${targetLevel ? CareerInsightHelpers.getExperienceLevelLabel(targetLevel as string) : 'the desired'} level
    2. Gap analysis for ${Array.isArray(skillCategories) ? skillCategories.join(', ') : 'relevant skills'}
    3. Timeline to achieve goals within ${timeline ? CareerInsightHelpers.getTimelineLabel(timeline as string) : 'the desired timeframe'}
    4. Industry insights for ${targetRoleType ? CareerInsightHelpers.getRoleTypeLabel(targetRoleType as string) : 'the target'} roles
    `
    }`;

    // Use OpenAI for skill gap analysis as it requires high-quality career guidance
    const response = await this.aiService.generateResponse(prompt, { requireHighQuality: true });
    const analysis = this.parser.parseSkillGapResponse(response);

    return {
      aiInsights: {
        strengths: analysis.strengths || [],
        opportunities: analysis.opportunities || [],
        challenges: analysis.challenges || [],
        recommendations: analysis.recommendations || [],
        confidenceScore: analysis.confidenceScore || 85,
      },
      skillGapAnalysis: {
        currentSkills: jobSeekerProfile.skills || [],
        targetRole: targetRoleFromForm || '',
        skillGaps: analysis.skillGaps || [],
        estimatedTimeToClose: analysis.timeEstimate || '6 months',
        overallReadinessScore: analysis.readinessScore || 65,
      },
      detailedAnalysis: analysis.detailedAnalysis || analysis.summary,
    };
  }
}
