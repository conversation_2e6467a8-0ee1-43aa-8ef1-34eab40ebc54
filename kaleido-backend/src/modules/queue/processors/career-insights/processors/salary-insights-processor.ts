import { Logger } from '@nestjs/common';
import { CareerInsight } from '../../../../career-insights/entities/career-insight.entity';
import { AIService } from '../ai-service';
import { ResponseParser } from '../parsers';
import { CareerInsightHelpers } from '../helpers';

export class SalaryInsightsProcessor {
  private readonly logger = new Logger(SalaryInsightsProcessor.name);
  private readonly parser = new ResponseParser();

  constructor(private readonly aiService: AIService) {}

  async process(insight: CareerInsight, params: any) {
    const { targetRole, jobSeekerProfile, formData } = params;
    const targetRoleFromForm = formData?.targetRole || targetRole;

    // Calculate total years of experience
    const totalExperience = CareerInsightHelpers.calculateTotalExperience(
      jobSeekerProfile.experience,
    );

    const prompt = `Provide comprehensive compensation insights based on the following profile:

    Job Seeker Details:
    - Target Role: ${targetRole || 'Current role progression'}
    - Current/Recent Role: ${CareerInsightHelpers.extractCurrentRole(jobSeekerProfile.experience)}
    - Total Experience: ${Math.round(totalExperience)} years
    - Location: ${jobSeekerProfile.location || 'Not specified'}
    - Experience Level: ${jobSeekerProfile.calculatedExperienceLevel}

    Skills & Qualifications:
    - Technical Skills: ${CareerInsightHelpers.formatSkills(jobSeekerProfile.skills)}
    - Certifications: ${CareerInsightHelpers.formatCertifications(jobSeekerProfile.certifications)}
    - Languages: ${CareerInsightHelpers.formatLanguages(jobSeekerProfile.languages)}
    - Education: ${CareerInsightHelpers.getHighestDegree(jobSeekerProfile.education)}

    Current Compensation Preferences:
    ${CareerInsightHelpers.formatCompensationPreferences(jobSeekerProfile.compensation)}

    Work Availability:
    ${CareerInsightHelpers.formatWorkAvailability(jobSeekerProfile.workAvailability)}

    Provide detailed compensation insights including:
    1. Current market salary range for the target role in their location
    2. How their skills and certifications impact compensation (premium skills)
    3. Comparison with their current expectations
    4. Benefits package recommendations based on their priorities
    5. Negotiation strategies specific to their experience level
    6. Remote work impact on compensation
    7. Career progression salary trajectory over next 5 years
    8. Top paying companies/industries for their skill set
    9. Cost of living considerations for their location`;

    // Use Groq for salary insights as it's data-driven and can be processed faster
    const response = await this.aiService.generateResponse(prompt, {
      preferGroq: true,
      maxTokens: 2000,
    });
    const salary = this.parser.parseSalaryResponse(response);

    return {
      aiInsights: {
        strengths: salary.strengths || [],
        opportunities: salary.opportunities || [],
        challenges: salary.challenges || [],
        recommendations: salary.recommendations || [],
        confidenceScore: salary.confidenceScore || 85,
      },
      compensationBenchmark: {
        currentRole:
          targetRoleFromForm ||
          CareerInsightHelpers.extractCurrentRole(jobSeekerProfile.experience),
        location: jobSeekerProfile.location || 'Not specified',
        experienceLevel: jobSeekerProfile.calculatedExperienceLevel || 'MID_LEVEL',
        compensationData: {
          currentMarketRate: {
            min: salary.range?.min || 0,
            max: salary.range?.max || 0,
            median: salary.range?.median || 0,
            currency: 'USD',
          },
          percentile: salary.percentile || 50,
          factors: salary.compensationFactors || [],
          geographicVariation: salary.geographicVariation,
        },
        negotiationTips: salary.tips || [],
        benefitsComparison: salary.benefitsComparison,
      },
      detailedAnalysis: salary.detailedAnalysis || salary.summary,
    };
  }
}
