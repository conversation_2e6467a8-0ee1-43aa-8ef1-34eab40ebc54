import { Logger } from '@nestjs/common';
import { CareerInsight } from '../../../../career-insights/entities/career-insight.entity';
import { AIService } from '../ai-service';
import { ResponseParser } from '../parsers';
import { CareerInsightHelpers } from '../helpers';

export class MarketTrendsProcessor {
  private readonly logger = new Logger(MarketTrendsProcessor.name);
  private readonly parser = new ResponseParser();

  constructor(private readonly aiService: AIService) {}

  async process(insight: CareerInsight, params: any) {
    const { industry, location, targetRole, jobSeekerProfile, formData } = params;

    // Extract AI impact concern from form data
    const aiImpactConcern = formData?.aiImpactConcern;
    const aiConcernLevel = CareerInsightHelpers.getAIConcernLevel(aiImpactConcern);

    // Infer industry from experience if not provided
    const inferredIndustry = CareerInsightHelpers.getIndustryFromProfile(
      jobSeekerProfile,
      industry,
    );

    const prompt = `Analyze job market trends based on the following profile and interests:

    Job Seeker Context:
    - Current Role: ${CareerInsightHelpers.extractCurrentRole(jobSeekerProfile.experience)}
    - Experience Level: ${jobSeekerProfile.calculatedExperienceLevel}
    - Target Role: ${targetRole || 'Career advancement'}
    - Industry Focus: ${inferredIndustry}
    - Location: ${location || jobSeekerProfile.location || 'Remote/Global'}
    - Key Skills: ${CareerInsightHelpers.formatSkills(jobSeekerProfile.skills, 10)}
    - AI Impact Concern: ${aiConcernLevel}

    Professional Background:
    - Years of Experience: ${jobSeekerProfile.experience.length} roles
    - Education Level: ${jobSeekerProfile.education[0]?.degree || 'Not specified'}
    - Certifications: ${jobSeekerProfile.certifications.length} certifications
    - Languages: ${jobSeekerProfile.languages.length} languages

    Provide comprehensive market analysis including:
    1. Current demand vs supply for professionals with this profile
    2. Industry growth projections for next 3-5 years
    3. Emerging skills and technologies gaining traction
    4. Salary trends and compensation packages
    5. Remote work opportunities and hybrid work trends
    6. Top hiring companies in their location/industry
    7. ${
      aiConcernLevel === 'Very concerned' || aiConcernLevel === 'Somewhat concerned'
        ? 'DETAILED ANALYSIS: How AI/automation will impact their specific role and industry, including mitigation strategies'
        : 'Impact of AI/automation on their role/industry'
    }
    8. ${
      aiConcernLevel === 'Optimistic' || aiConcernLevel === 'Very optimistic'
        ? 'AI-enhanced career opportunities and how to leverage AI tools'
        : 'Geographic hotspots for their skillset'
    }
    9. ${
      aiConcernLevel === 'Needs information'
        ? 'Comprehensive AI impact assessment with both risks and opportunities'
        : 'Industry-specific challenges and opportunities'
    }
    10. ${
      aiConcernLevel !== 'Neutral'
        ? 'AI-proof skills and certifications to future-proof their career'
        : 'Recommended certifications or skills to stay competitive'
    }`;

    // Use Groq for market trends as it's more factual and can be processed faster
    const response = await this.aiService.generateResponse(prompt, {
      preferGroq: true,
      maxTokens: 2500,
    });
    const trends = this.parser.parseMarketTrendsResponse(response);

    return {
      aiInsights: {
        strengths: trends.strengths || [],
        opportunities: trends.opportunities || [],
        challenges: trends.challenges || [],
        recommendations: trends.recommendations || [],
        confidenceScore: trends.confidenceScore || 85,
      },
      marketTrendAnalysis: {
        industryOverview: trends.industryOverview || inferredIndustry,
        emergingTrends: trends.emergingTrends || [],
        decliningAreas: trends.decliningAreas || [],
        opportunities: trends.marketOpportunities || [],
      },
      detailedAnalysis: trends.detailedAnalysis || trends.summary,
    };
  }
}
