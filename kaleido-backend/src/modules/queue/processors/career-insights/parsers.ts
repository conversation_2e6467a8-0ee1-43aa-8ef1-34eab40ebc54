import {
  ParsedSkillGapResponse,
  ParsedCareerPathResponse,
  ParsedMarketTrendsResponse,
  ParsedSalaryResponse,
  ParsedRoleTransitionResponse,
  ParsedIndustryOutlookResponse,
  ParsedInterviewResponse,
  ParsedResumeResponse,
  SkillGap,
  OnlineResource,
  Recommendation,
  MarketTrend,
  MarketOpportunity,
  CompensationFactor,
  TransitionPlan,
} from './types';

export class ResponseParser {
  parseSkillGapResponse(response: string): ParsedSkillGapResponse {
    try {
      const strengths = this.extractStrengths(response);
      const opportunities = this.extractOpportunities(response);
      const challenges = this.extractChallenges(response);
      const recommendations = this.extractRecommendations(response);
      const skillGaps = this.extractSkillGaps(response);
      const onlineResources = this.extractOnlineResources(response);

      const confidenceScore = this.extractConfidenceScore(response) || 75;
      const readinessScore = this.extractReadinessScore(response) || 65;
      const timeEstimate = this.extractTimeEstimate(response) || '3-6 months';

      return {
        summary: 'Skill gap analysis complete',
        detailedAnalysis: response,
        strengths,
        opportunities,
        challenges,
        recommendations,
        confidenceScore,
        skillGaps,
        onlineResources,
        readinessScore,
        timeEstimate,
      };
    } catch (error) {
      console.error('Error parsing skill gap response:', error);
      return {
        summary: 'Skill gap analysis complete',
        detailedAnalysis: response,
        strengths: ['Analysis provided in detailed section'],
        opportunities: ['Refer to detailed analysis'],
        challenges: ['See detailed analysis below'],
        recommendations: [],
        confidenceScore: 75,
        skillGaps: [],
        onlineResources: [],
        readinessScore: 65,
        timeEstimate: '3-6 months',
      };
    }
  }

  parseCareerPathResponse(response: string): ParsedCareerPathResponse {
    try {
      const strengths = this.extractStrengths(response);
      const opportunities = this.extractOpportunities(response);
      const challenges = this.extractChallenges(response);
      const recommendations = this.extractRecommendations(response);
      const careerPaths = this.extractCareerPaths(response);
      const immediateSteps = this.extractImmediateSteps(response);
      const confidenceScore = this.extractConfidenceScore(response) || 80;

      return {
        summary: 'Career path recommendations generated',
        detailedAnalysis: response,
        strengths,
        opportunities,
        challenges,
        recommendations,
        confidenceScore,
        paths: careerPaths,
        immediateSteps,
      };
    } catch (error) {
      console.error('Error parsing career path response:', error);
      return {
        summary: 'Career path recommendations generated',
        detailedAnalysis: response,
        strengths: ['Analysis provided in detailed section'],
        opportunities: ['Refer to detailed analysis'],
        challenges: ['See detailed analysis below'],
        recommendations: [],
        confidenceScore: 80,
        paths: [],
        immediateSteps: [],
      };
    }
  }

  parseMarketTrendsResponse(response: string): ParsedMarketTrendsResponse {
    try {
      const strengths = this.extractStrengths(response);
      const opportunities = this.extractOpportunities(response);
      const challenges = this.extractChallenges(response);
      const recommendations = this.extractRecommendations(response);
      const confidenceScore = this.extractConfidenceScore(response) || 85;

      const industryOverview = this.extractIndustryOverview(response);
      const emergingTrends = this.extractEmergingTrends(response);
      const decliningAreas = this.extractDecliningAreas(response);
      const marketOpportunities = this.extractMarketOpportunities(response);

      return {
        summary: 'Market trends analysis complete',
        detailedAnalysis: response,
        industryOverview,
        strengths,
        opportunities,
        challenges,
        recommendations,
        confidenceScore,
        emergingTrends,
        decliningAreas,
        marketOpportunities,
      };
    } catch (error) {
      console.error('Error parsing market trends response:', error);
      return {
        summary: 'Market trends analysis complete',
        detailedAnalysis: response,
        industryOverview: 'Detailed analysis provided below',
        strengths: ['Analysis provided in detailed section'],
        opportunities: ['Refer to detailed analysis'],
        challenges: ['See detailed analysis below'],
        recommendations: [],
        confidenceScore: 85,
        emergingTrends: [],
        decliningAreas: [],
        marketOpportunities: [],
      };
    }
  }

  parseSalaryResponse(response: string): ParsedSalaryResponse {
    try {
      const strengths = this.extractStrengths(response);
      const opportunities = this.extractOpportunities(response);
      const challenges = this.extractChallenges(response);
      const recommendations = this.extractRecommendations(response);
      const confidenceScore = this.extractConfidenceScore(response) || 85;

      const salaryRange = this.extractSalaryRange(response);
      const percentile = this.extractPercentile(response);
      const compensationFactors = this.extractCompensationFactors(response);
      const negotiationTips = this.extractNegotiationTips(response);

      return {
        summary: 'Salary insights generated',
        detailedAnalysis: response,
        strengths,
        opportunities,
        challenges,
        recommendations,
        confidenceScore,
        range: salaryRange,
        percentile,
        compensationFactors,
        tips: negotiationTips,
        benefitsComparison: {
          standard: ['Health insurance', '401k match', 'PTO'],
          premium: ['Stock options', 'Remote work', 'Education budget'],
        },
      };
    } catch (error) {
      console.error('Error parsing salary response:', error);
      return {
        summary: 'Salary insights generated',
        detailedAnalysis: response,
        strengths: ['Analysis provided in detailed section'],
        opportunities: ['Refer to detailed analysis'],
        challenges: ['See detailed analysis below'],
        recommendations: [],
        confidenceScore: 85,
        range: null,
        percentile: null,
        compensationFactors: [],
        tips: [],
        benefitsComparison: {
          standard: [],
          premium: [],
        },
      };
    }
  }

  parseRoleTransitionResponse(response: string): ParsedRoleTransitionResponse {
    try {
      const strengths = this.extractStrengths(response);
      const opportunities = this.extractOpportunities(response);
      const challenges = this.extractChallenges(response);
      const recommendations = this.extractRecommendations(response);
      const confidenceScore = this.extractConfidenceScore(response) || 80;

      const transferableSkills = this.extractTransferableSkills(response);
      const newSkills = this.extractSkillGaps(response);
      const transitionPlan = this.extractTransitionPlan(response);
      const difficulty = this.extractDifficultyLevel(response) || 'MODERATE';

      return {
        summary: 'Role transition analysis complete',
        detailedAnalysis: response,
        strengths,
        opportunities,
        challenges,
        recommendations,
        confidenceScore,
        difficulty,
        transferableSkills,
        newSkills,
        plan: transitionPlan,
      };
    } catch (error) {
      console.error('Error parsing role transition response:', error);
      return {
        summary: 'Role transition analysis complete',
        detailedAnalysis: response,
        strengths: ['Analysis provided in detailed section'],
        opportunities: ['Refer to detailed analysis'],
        challenges: ['See detailed analysis below'],
        recommendations: [],
        confidenceScore: 80,
        difficulty: 'MODERATE',
        transferableSkills: [],
        newSkills: [],
        plan: [],
      };
    }
  }

  parseIndustryOutlookResponse(response: string): ParsedIndustryOutlookResponse {
    try {
      const keyFindings = this.extractKeyFindings(response);
      const recommendations = this.extractRecommendations(response);

      return {
        summary: 'Industry outlook analysis complete',
        keyFindings,
        recommendations,
        detailedAnalysis: response,
      };
    } catch (error) {
      console.error('Error parsing industry outlook response:', error);
      return {
        summary: 'Industry outlook analysis complete',
        keyFindings: [],
        recommendations: [],
        detailedAnalysis: response,
      };
    }
  }

  parseInterviewResponse(response: string): ParsedInterviewResponse {
    try {
      const keyFindings = this.extractKeyFindings(response);
      const recommendations = this.extractRecommendations(response);
      const behavioralQuestions = this.extractBehavioralQuestions(response);
      const technicalQuestions = this.extractTechnicalQuestions(response);
      const scenarios = this.extractScenarios(response);
      const questionsToAsk = this.extractQuestionsToAsk(response);
      const tips = this.extractInterviewTips(response);

      return {
        summary: 'Interview preparation complete',
        detailedAnalysis: response,
        keyFindings,
        recommendations,
        behavioral: behavioralQuestions,
        technical: technicalQuestions,
        scenarios,
        toAsk: questionsToAsk,
        tips,
      };
    } catch (error) {
      console.error('Error parsing interview response:', error);
      return {
        summary: 'Interview preparation complete',
        detailedAnalysis: response,
        keyFindings: [],
        recommendations: [],
        behavioral: [],
        technical: [],
        scenarios: [],
        toAsk: [],
        tips: [],
      };
    }
  }

  parseResumeResponse(response: string): ParsedResumeResponse {
    try {
      const keyFindings = this.extractKeyFindings(response);
      const recommendations = this.extractRecommendations(response);
      const keywords = this.extractKeywords(response);
      const skillsToAdd = this.extractSkillsToAdd(response);
      const formatSuggestions = this.extractFormatSuggestions(response);
      const improvements = this.extractImprovements(response);

      return {
        summary: 'Resume optimization complete',
        detailedAnalysis: response,
        keyFindings,
        recommendations,
        keywords,
        skills: skillsToAdd,
        format: formatSuggestions,
        improvements,
      };
    } catch (error) {
      console.error('Error parsing resume response:', error);
      return {
        summary: 'Resume optimization complete',
        detailedAnalysis: response,
        keyFindings: [],
        recommendations: [],
        keywords: [],
        skills: [],
        format: [],
        improvements: [],
      };
    }
  }

  private extractConfidenceScore(response: string): number | null {
    try {
      const patterns = [
        /confidence.*?(\d{1,3})%/i,
        /accuracy.*?(\d{1,3})%/i,
        /certainty.*?(\d{1,3})%/i,
        /score.*?(\d{1,3})%/i,
        /(\d{1,3})%.*?confidence/i,
      ];

      for (const pattern of patterns) {
        const match = response.match(pattern);
        if (match) {
          const score = parseInt(match[1]);
          if (score >= 0 && score <= 100) {
            return score;
          }
        }
      }

      return null;
    } catch (error) {
      console.error('Error extracting confidence score:', error);
      return null;
    }
  }

  private extractReadinessScore(response: string): number | null {
    try {
      const patterns = [
        /readiness.*?(\d{1,3})%/i,
        /ready.*?(\d{1,3})%/i,
        /prepared.*?(\d{1,3})%/i,
        /(\d{1,3})%.*?ready/i,
        /(\d{1,3})%.*?readiness/i,
      ];

      for (const pattern of patterns) {
        const match = response.match(pattern);
        if (match) {
          const score = parseInt(match[1]);
          if (score >= 0 && score <= 100) {
            return score;
          }
        }
      }

      return null;
    } catch (error) {
      console.error('Error extracting readiness score:', error);
      return null;
    }
  }

  private extractTimeEstimate(response: string): string | null {
    try {
      const patterns = [
        /(?:time.*?estimate|estimate.*?time).*?(\d+(?:\.\d+)?[\s-]*(?:to[\s-]*\d+(?:\.\d+)?)?[\s-]*(?:months?|years?|weeks?))/i,
        /(?:timeline|timeframe).*?(\d+(?:\.\d+)?[\s-]*(?:to[\s-]*\d+(?:\.\d+)?)?[\s-]*(?:months?|years?|weeks?))/i,
        /(?:duration|period).*?(\d+(?:\.\d+)?[\s-]*(?:to[\s-]*\d+(?:\.\d+)?)?[\s-]*(?:months?|years?|weeks?))/i,
        /(\d+(?:\.\d+)?[\s-]*(?:to[\s-]*\d+(?:\.\d+)?)?[\s-]*(?:months?|years?|weeks?)).*?(?:to complete|to achieve|duration)/i,
      ];

      for (const pattern of patterns) {
        const match = response.match(pattern);
        if (match) {
          return match[1].trim();
        }
      }

      return null;
    } catch (error) {
      console.error('Error extracting time estimate:', error);
      return null;
    }
  }

  private extractStrengths(response: string): string[] {
    try {
      const strengths: string[] = [];
      const sections = response.split(/\n+/);

      for (const section of sections) {
        if (
          section.toUpperCase().includes('STRENGTHS') ||
          section.toLowerCase().includes('your strengths') ||
          section.toLowerCase().includes('you have')
        ) {
          const strengthMatches = section.match(/[-•]\s*(.+)/g) || [];
          strengthMatches.forEach((match) => {
            const strength = match.replace(/[-•]\s*/, '').trim();
            if (strength && strength.length > 10) {
              strengths.push(strength);
            }
          });
        }
      }

      return strengths.slice(0, 5);
    } catch (error) {
      return [];
    }
  }

  private extractOpportunities(response: string): string[] {
    try {
      const opportunities: string[] = [];
      const sections = response.split(/\n+/);

      for (const section of sections) {
        if (
          section.toUpperCase().includes('OPPORTUNITIES') ||
          section.toLowerCase().includes('there is') ||
          section.toLowerCase().includes('the market')
        ) {
          const oppMatches = section.match(/[-•]\s*(.+)/g) || [];
          oppMatches.forEach((match) => {
            const opp = match.replace(/[-•]\s*/, '').trim();
            if (opp && opp.length > 10) {
              opportunities.push(opp);
            }
          });
        }
      }

      return opportunities.slice(0, 5);
    } catch (error) {
      return [];
    }
  }

  private extractChallenges(response: string): string[] {
    try {
      const challenges: string[] = [];
      const sections = response.split(/\n+/);

      for (const section of sections) {
        if (
          section.toUpperCase().includes('CHALLENGES') ||
          section.toLowerCase().includes('challenge') ||
          section.toLowerCase().includes('difficult')
        ) {
          const challengeMatches = section.match(/[-•]\s*(.+)/g) || [];
          challengeMatches.forEach((match) => {
            const challenge = match.replace(/[-•]\s*/, '').trim();
            if (challenge && challenge.length > 10) {
              challenges.push(challenge);
            }
          });
        }
      }

      return challenges.slice(0, 5);
    } catch (error) {
      return [];
    }
  }

  private extractRecommendations(response: string): Recommendation[] {
    try {
      const recommendations: Recommendation[] = [];
      const sections = response.split(/\n+/);

      for (const section of sections) {
        if (
          section.toUpperCase().includes('RECOMMENDATIONS') ||
          section.toLowerCase().includes('you should') ||
          section.toLowerCase().includes('recommend')
        ) {
          const lines = section.split('\n');

          for (let i = 0; i < lines.length; i++) {
            const line = lines[i];
            if (line.includes('You should') || line.includes('Action:')) {
              const action = line.replace(/^.*?(You should|Action:)\s*/i, '').trim();
              let priority: 'HIGH' | 'MEDIUM' | 'LOW' = 'MEDIUM';
              let expectedOutcome = '';
              let timeframe = '3-6 months';

              for (let j = Math.max(0, i - 2); j <= Math.min(lines.length - 1, i + 2); j++) {
                if (lines[j].toLowerCase().includes('priority')) {
                  if (lines[j].toUpperCase().includes('HIGH')) priority = 'HIGH';
                  else if (lines[j].toUpperCase().includes('LOW')) priority = 'LOW';
                }
                if (lines[j].toLowerCase().includes('outcome')) {
                  expectedOutcome = lines[j].replace(/^.*?outcome[:\s]*/i, '').trim();
                }
                if (
                  lines[j].toLowerCase().includes('timeframe') ||
                  lines[j].toLowerCase().includes('timeline')
                ) {
                  timeframe = lines[j].replace(/^.*?(timeframe|timeline)[:\s]*/i, '').trim();
                }
              }

              if (action && action.length > 10) {
                recommendations.push({
                  priority,
                  action,
                  expectedOutcome: expectedOutcome || 'Improved career prospects',
                  timeframe: timeframe || '3-6 months',
                });
              }
            }
          }
        }
      }

      return recommendations.slice(0, 6);
    } catch (error) {
      return [];
    }
  }

  private extractSkillGaps(response: string): SkillGap[] {
    try {
      const skillGaps: SkillGap[] = [];
      const sections = response.split(/\n+/);

      for (const section of sections) {
        if (
          section.toUpperCase().includes('SKILL GAPS') ||
          section.toLowerCase().includes('skills to learn') ||
          section.toLowerCase().includes('skill gap')
        ) {
          const lines = section.split('\n');

          for (let i = 0; i < lines.length; i++) {
            const line = lines[i];
            if (line.includes('Skill Name:') || line.match(/^[-•]\s*\w+/)) {
              const skillName = line
                .replace(/^.*?(Skill Name:|[-•])\s*/i, '')
                .split(':')[0]
                .trim();
              let currentLevel: 'BEGINNER' | 'INTERMEDIATE' | 'ADVANCED' = 'BEGINNER';
              let targetLevel: 'BEGINNER' | 'INTERMEDIATE' | 'ADVANCED' = 'ADVANCED';
              let priority: 'HIGH' | 'MEDIUM' | 'LOW' = 'MEDIUM';
              let timeToAchieve = '3 months';
              let marketDemand = 80;

              for (let j = Math.max(0, i - 1); j <= Math.min(lines.length - 1, i + 5); j++) {
                if (lines[j].toLowerCase().includes('current level')) {
                  if (lines[j].toUpperCase().includes('INTERMEDIATE'))
                    currentLevel = 'INTERMEDIATE';
                  else if (lines[j].toUpperCase().includes('ADVANCED')) currentLevel = 'ADVANCED';
                }
                if (lines[j].toLowerCase().includes('target level')) {
                  if (lines[j].toUpperCase().includes('INTERMEDIATE')) targetLevel = 'INTERMEDIATE';
                  else if (lines[j].toUpperCase().includes('BEGINNER')) targetLevel = 'BEGINNER';
                }
                if (lines[j].toLowerCase().includes('priority')) {
                  if (lines[j].toUpperCase().includes('HIGH')) priority = 'HIGH';
                  else if (lines[j].toUpperCase().includes('LOW')) priority = 'LOW';
                }
                if (lines[j].toLowerCase().includes('time to achieve')) {
                  timeToAchieve = lines[j].replace(/^.*?time to achieve[:\s]*/i, '').trim();
                }
                if (lines[j].toLowerCase().includes('market demand')) {
                  const demandMatch = lines[j].match(/(\d+)%?/);
                  if (demandMatch) marketDemand = parseInt(demandMatch[1]);
                }
              }

              if (skillName && skillName.length > 2 && skillName.length < 50) {
                skillGaps.push({
                  skillName,
                  currentLevel,
                  targetLevel,
                  priority,
                  learningResources: [],
                  timeToAchieve,
                  marketDemand,
                });
              }
            }
          }
        }
      }

      return skillGaps;
    } catch (error) {
      return [];
    }
  }

  private extractOnlineResources(response: string): OnlineResource[] {
    try {
      const resources: OnlineResource[] = [];
      const sections = response.split(/\n+/);

      for (const section of sections) {
        if (
          section.toUpperCase().includes('ONLINE RESOURCES') ||
          section.toLowerCase().includes('learning resource') ||
          section.toLowerCase().includes('course')
        ) {
          const lines = section.split('\n');

          for (let i = 0; i < lines.length; i++) {
            const line = lines[i];
            if (
              line.includes('Title:') ||
              line.includes('http') ||
              line.match(/^[-•]\s*\w+.*course/i)
            ) {
              let title = '';
              let type: 'COURSE' | 'TUTORIAL' | 'CERTIFICATION' | 'BOOTCAMP' | 'WORKSHOP' =
                'COURSE';
              let provider = '';
              let url = '';
              let estimatedDuration = '';
              let cost = 'Free';

              const urlMatch = line.match(/https?:\/\/[^\s]+/);
              if (urlMatch) url = urlMatch[0];

              if (line.includes('Title:')) {
                title = line.replace(/^.*?Title:\s*/i, '').trim();
              } else {
                title = line
                  .replace(/^[-•]\s*/, '')
                  .replace(/https?:\/\/[^\s]+/, '')
                  .trim();
              }

              for (let j = Math.max(0, i - 1); j <= Math.min(lines.length - 1, i + 5); j++) {
                if (lines[j].toLowerCase().includes('type:')) {
                  if (lines[j].toUpperCase().includes('TUTORIAL')) type = 'TUTORIAL';
                  else if (lines[j].toUpperCase().includes('CERTIFICATION')) type = 'CERTIFICATION';
                  else if (lines[j].toUpperCase().includes('BOOTCAMP')) type = 'BOOTCAMP';
                  else if (lines[j].toUpperCase().includes('WORKSHOP')) type = 'WORKSHOP';
                }
                if (lines[j].toLowerCase().includes('provider:')) {
                  provider = lines[j].replace(/^.*?provider:\s*/i, '').trim();
                }
                if (lines[j].toLowerCase().includes('duration:')) {
                  estimatedDuration = lines[j].replace(/^.*?duration:\s*/i, '').trim();
                }
                if (lines[j].toLowerCase().includes('cost:')) {
                  const costText = lines[j].replace(/^.*?cost:\s*/i, '').trim();
                  if (costText.toLowerCase().includes('free')) cost = 'Free';
                  else if (costText.includes('$')) {
                    const priceMatch = costText.match(/\$(\d+)/);
                    if (priceMatch) {
                      const price = parseInt(priceMatch[1]);
                      if (price < 50) cost = '$';
                      else if (price <= 200) cost = '$$';
                      else cost = '$$$';
                    }
                  }
                }
                if (!url && lines[j].includes('http')) {
                  const urlMatch2 = lines[j].match(/https?:\/\/[^\s]+/);
                  if (urlMatch2) url = urlMatch2[0];
                }
              }

              if (title && title.length > 5) {
                resources.push({
                  title,
                  type,
                  provider: provider || 'Online Platform',
                  url: url || 'https://example.com',
                  estimatedDuration: estimatedDuration || '1-3 months',
                  cost,
                });
              }
            }
          }
        }
      }

      return resources;
    } catch (error) {
      return [];
    }
  }

  private extractCareerPaths(response: string): any[] {
    try {
      const paths: any[] = [];
      const sections = response.split(/\n+/);

      for (const section of sections) {
        if (
          section.toLowerCase().includes('career path') ||
          section.toLowerCase().includes('progression') ||
          section.toLowerCase().includes('career route')
        ) {
          const pathMatches = section.match(/(?:path|route|track):\s*(.+)/gi) || [];

          pathMatches.forEach((match) => {
            const pathName = match.replace(/^.*?:\s*/, '').trim();
            if (pathName) {
              paths.push({
                pathName,
                steps: [],
                totalDuration: '2-5 years',
                difficultyLevel: 'MODERATE',
                successProbability: 75,
              });
            }
          });
        }
      }

      return paths;
    } catch (error) {
      return [];
    }
  }

  private extractImmediateSteps(response: string): string[] {
    try {
      const steps: string[] = [];
      const sections = response.split(/\n+/);

      for (const section of sections) {
        if (
          section.toLowerCase().includes('immediate') ||
          section.toLowerCase().includes('start today') ||
          section.toLowerCase().includes('first step') ||
          section.toLowerCase().includes('quick win')
        ) {
          const stepMatches = section.match(/[-•]\s*(.+)/g) || [];
          stepMatches.forEach((match) => {
            const step = match.replace(/[-•]\s*/, '').trim();
            if (
              step &&
              step.length > 10 &&
              (step.includes('You should') || step.includes('should'))
            ) {
              steps.push(step);
            }
          });
        }
      }

      return steps.slice(0, 6);
    } catch (error) {
      return [];
    }
  }

  private extractIndustryOverview(response: string): string {
    try {
      const patterns = [
        /industry overview[:\s]*(.+?)(?:\n|$)/i,
        /market overview[:\s]*(.+?)(?:\n|$)/i,
        /sector analysis[:\s]*(.+?)(?:\n|$)/i,
      ];

      for (const pattern of patterns) {
        const match = response.match(pattern);
        if (match && match[1]) {
          return match[1].trim();
        }
      }

      const sentences = response.split(/[.!?]+/);
      for (const sentence of sentences) {
        if (
          sentence.toLowerCase().includes('industry') ||
          sentence.toLowerCase().includes('sector') ||
          sentence.toLowerCase().includes('market')
        ) {
          return sentence.trim() + '.';
        }
      }

      return 'Technology sector analysis';
    } catch (error) {
      return 'Industry analysis provided';
    }
  }

  private extractEmergingTrends(response: string): MarketTrend[] {
    try {
      const trends: MarketTrend[] = [];
      const sections = response.split(/\n+/);

      for (const section of sections) {
        if (
          section.toLowerCase().includes('emerging') ||
          section.toLowerCase().includes('growing') ||
          section.toLowerCase().includes('trend')
        ) {
          const trendMatches = section.match(/[-•]\s*(.+)/g) || [];
          trendMatches.forEach((match) => {
            const trend = match.replace(/[-•]\s*/, '').trim();
            if (trend) {
              trends.push({
                trend: trend.split(':')[0] || trend,
                impact: 'POSITIVE',
                relevanceScore: 85,
                description: trend,
                dataSource: 'AI Analysis',
              });
            }
          });
        }
      }

      return trends;
    } catch (error) {
      return [];
    }
  }

  private extractDecliningAreas(response: string): MarketTrend[] {
    try {
      const areas: MarketTrend[] = [];
      const sections = response.split(/\n+/);

      for (const section of sections) {
        if (
          section.toLowerCase().includes('declining') ||
          section.toLowerCase().includes('obsolete') ||
          section.toLowerCase().includes('reducing')
        ) {
          const declineMatches = section.match(/[-•]\s*(.+)/g) || [];
          declineMatches.forEach((match) => {
            const area = match.replace(/[-•]\s*/, '').trim();
            if (area) {
              areas.push({
                trend: area.split(':')[0] || area,
                impact: 'NEGATIVE',
                relevanceScore: 60,
                description: area,
                dataSource: 'AI Analysis',
              });
            }
          });
        }
      }

      return areas;
    } catch (error) {
      return [];
    }
  }

  private extractMarketOpportunities(response: string): MarketOpportunity[] {
    try {
      const opportunities: MarketOpportunity[] = [];
      const sections = response.split(/\n+/);

      for (const section of sections) {
        if (
          section.toLowerCase().includes('opportunit') ||
          section.toLowerCase().includes('demand') ||
          section.toLowerCase().includes('growth area')
        ) {
          const oppMatches = section.match(/[-•]\s*(.+)/g) || [];
          oppMatches.forEach((match) => {
            const opp = match.replace(/[-•]\s*/, '').trim();
            if (opp) {
              opportunities.push({
                title: opp.split(':')[0] || opp.substring(0, 50),
                description: opp,
                readinessScore: 75,
                requiredPreparation: [],
              });
            }
          });
        }
      }

      return opportunities;
    } catch (error) {
      return [];
    }
  }

  private extractSalaryRange(
    response: string,
  ): { min: number; max: number; median: number } | null {
    try {
      const patterns = [
        /\$(\d{1,3}(?:,\d{3})*)\s*(?:to|-)\s*\$(\d{1,3}(?:,\d{3})*)/gi,
        /salary.*?(\d{1,3}(?:,\d{3})*)\s*(?:to|-)\s*(\d{1,3}(?:,\d{3})*)/gi,
        /compensation.*?(\d{1,3}(?:,\d{3})*)\s*(?:to|-)\s*(\d{1,3}(?:,\d{3})*)/gi,
        /range.*?(\d{1,3}(?:,\d{3})*)\s*(?:to|-)\s*(\d{1,3}(?:,\d{3})*)/gi,
      ];

      for (const pattern of patterns) {
        const match = response.match(pattern);
        if (match && match.length > 0) {
          const fullMatch = match[0];
          const numbers = fullMatch.match(/\d{1,3}(?:,\d{3})*/g);
          if (numbers && numbers.length >= 2) {
            const min = parseInt(numbers[0].replace(/,/g, ''));
            const max = parseInt(numbers[1].replace(/,/g, ''));
            const median = Math.round((min + max) / 2);
            return { min, max, median };
          }
        }
      }

      return null;
    } catch (error) {
      return null;
    }
  }

  private extractPercentile(response: string): number | null {
    try {
      const patterns = [
        /(\d{1,3})(?:th|st|nd|rd)?\s*percentile/i,
        /percentile.*?(\d{1,3})/i,
        /top\s*(\d{1,3})%/i,
      ];

      for (const pattern of patterns) {
        const match = response.match(pattern);
        if (match) {
          const percentile = parseInt(match[1]);
          if (percentile >= 0 && percentile <= 100) {
            if (pattern.toString().includes('top')) {
              return 100 - percentile;
            }
            return percentile;
          }
        }
      }

      return null;
    } catch (error) {
      return null;
    }
  }

  private extractCompensationFactors(response: string): CompensationFactor[] {
    try {
      const factors: CompensationFactor[] = [];
      const sections = response.split(/\n+/);

      for (const section of sections) {
        if (
          section.toLowerCase().includes('factor') ||
          section.toLowerCase().includes('compensation') ||
          section.toLowerCase().includes('salary')
        ) {
          const factorMatches = section.match(/[-•]\s*(.+)/g) || [];
          factorMatches.forEach((match) => {
            const factor = match.replace(/[-•]\s*/, '').trim();
            if (factor) {
              factors.push({
                factor: factor.split(':')[0] || factor,
                impact: factor.toLowerCase().includes('negative') ? 'NEGATIVE' : 'POSITIVE',
                weight: 30,
              });
            }
          });
        }
      }

      return factors;
    } catch (error) {
      return [];
    }
  }

  private extractNegotiationTips(response: string): string[] {
    try {
      const tips: string[] = [];
      const sections = response.split(/\n+/);

      for (const section of sections) {
        if (
          section.toLowerCase().includes('negotiat') ||
          section.toLowerCase().includes('tip') ||
          section.toLowerCase().includes('advice')
        ) {
          const tipMatches = section.match(/[-•]\s*(.+)/g) || [];
          tipMatches.forEach((match) => {
            const tip = match.replace(/[-•]\s*/, '').trim();
            if (tip && tip.length > 10) {
              tips.push(tip);
            }
          });
        }
      }

      return tips;
    } catch (error) {
      return [];
    }
  }

  private extractKeyFindings(response: string): string[] {
    try {
      const findings: string[] = [];
      const sections = response.split(/\n+/);

      for (const section of sections) {
        if (
          section.toLowerCase().includes('finding') ||
          section.toLowerCase().includes('key point') ||
          section.toLowerCase().includes('important')
        ) {
          const findingMatches = section.match(/[-•]\s*(.+)/g) || [];
          findingMatches.forEach((match) => {
            const finding = match.replace(/[-•]\s*/, '').trim();
            if (finding && finding.length > 10) {
              findings.push(finding);
            }
          });
        }
      }

      return findings.slice(0, 5);
    } catch (error) {
      return [];
    }
  }

  private extractKeywords(response: string): string[] {
    try {
      const keywords: string[] = [];
      const sections = response.split(/\n+/);

      for (const section of sections) {
        if (
          section.toLowerCase().includes('keyword') ||
          section.toLowerCase().includes('key term') ||
          section.toLowerCase().includes('important word')
        ) {
          const keywordMatch = section.match(/[:\s](.+)/);
          if (keywordMatch) {
            const keywordString = keywordMatch[1];
            const words = keywordString
              .split(/[,;•\-\n]+/)
              .map((word) => word.trim())
              .filter((word) => word.length > 2 && word.length < 30);
            keywords.push(...words);
          }
        }
      }

      return Array.from(new Set(keywords));
    } catch (error) {
      return [];
    }
  }

  private extractSkillsToAdd(response: string): string[] {
    try {
      const skills: string[] = [];
      const sections = response.split(/\n+/);

      for (const section of sections) {
        if (
          (section.toLowerCase().includes('add') && section.toLowerCase().includes('skill')) ||
          section.toLowerCase().includes('missing skill') ||
          section.toLowerCase().includes('should include')
        ) {
          const skillMatches = section.match(/[-•]\s*(.+)/g) || [];
          skillMatches.forEach((match) => {
            const skill = match.replace(/[-•]\s*/, '').trim();
            if (skill && skill.length > 2 && skill.length < 50) {
              skills.push(skill);
            }
          });
        }
      }

      return skills;
    } catch (error) {
      return [];
    }
  }

  private extractFormatSuggestions(response: string): string[] {
    try {
      const suggestions: string[] = [];
      const sections = response.split(/\n+/);

      for (const section of sections) {
        if (
          section.toLowerCase().includes('format') ||
          section.toLowerCase().includes('layout') ||
          section.toLowerCase().includes('structure')
        ) {
          const suggestionMatches = section.match(/[-•]\s*(.+)/g) || [];
          suggestionMatches.forEach((match) => {
            const suggestion = match.replace(/[-•]\s*/, '').trim();
            if (suggestion && suggestion.length > 10) {
              suggestions.push(suggestion);
            }
          });
        }
      }

      return suggestions;
    } catch (error) {
      return [];
    }
  }

  private extractImprovements(response: string): string[] {
    try {
      const improvements: string[] = [];
      const sections = response.split(/\n+/);

      for (const section of sections) {
        if (
          section.toLowerCase().includes('improve') ||
          section.toLowerCase().includes('enhance') ||
          section.toLowerCase().includes('better')
        ) {
          const improvementMatches = section.match(/[-•]\s*(.+)/g) || [];
          improvementMatches.forEach((match) => {
            const improvement = match.replace(/[-•]\s*/, '').trim();
            if (improvement && improvement.length > 10) {
              improvements.push(improvement);
            }
          });
        }
      }

      return improvements;
    } catch (error) {
      return [];
    }
  }

  private extractBehavioralQuestions(response: string): string[] {
    try {
      const questions: string[] = [];
      const sections = response.split(/\n+/);

      for (const section of sections) {
        if (
          section.toLowerCase().includes('behavioral') ||
          section.toLowerCase().includes('situation') ||
          section.toLowerCase().includes('tell me about')
        ) {
          const questionMatches = section.match(/[-•"']([^"']+\?)/g) || [];
          questionMatches.forEach((match) => {
            const question = match.replace(/[-•"']/g, '').trim();
            if (question && question.includes('?')) {
              questions.push(question);
            }
          });
        }
      }

      return questions;
    } catch (error) {
      return [];
    }
  }

  private extractTechnicalQuestions(response: string): string[] {
    try {
      const questions: string[] = [];
      const sections = response.split(/\n+/);

      for (const section of sections) {
        if (
          section.toLowerCase().includes('technical') ||
          section.toLowerCase().includes('coding') ||
          section.toLowerCase().includes('system design')
        ) {
          const questionMatches = section.match(/[-•"']([^"']+\?)/g) || [];
          questionMatches.forEach((match) => {
            const question = match.replace(/[-•"']/g, '').trim();
            if (question && question.includes('?')) {
              questions.push(question);
            }
          });
        }
      }

      return questions;
    } catch (error) {
      return [];
    }
  }

  private extractScenarios(response: string): string[] {
    try {
      const scenarios: string[] = [];
      const sections = response.split(/\n+/);

      for (const section of sections) {
        if (
          section.toLowerCase().includes('scenario') ||
          section.toLowerCase().includes('situation') ||
          section.toLowerCase().includes('case')
        ) {
          const scenarioMatches = section.match(/[-•]\s*(.+)/g) || [];
          scenarioMatches.forEach((match) => {
            const scenario = match.replace(/[-•]\s*/, '').trim();
            if (scenario && scenario.length > 20) {
              scenarios.push(scenario);
            }
          });
        }
      }

      return scenarios;
    } catch (error) {
      return [];
    }
  }

  private extractQuestionsToAsk(response: string): string[] {
    try {
      const questions: string[] = [];
      const sections = response.split(/\n+/);

      for (const section of sections) {
        if (
          section.toLowerCase().includes('ask the interviewer') ||
          section.toLowerCase().includes('questions to ask') ||
          section.toLowerCase().includes('ask about')
        ) {
          const questionMatches = section.match(/[-•"']([^"']+\?)/g) || [];
          questionMatches.forEach((match) => {
            const question = match.replace(/[-•"']/g, '').trim();
            if (question && question.includes('?')) {
              questions.push(question);
            }
          });
        }
      }

      return questions;
    } catch (error) {
      return [];
    }
  }

  private extractInterviewTips(response: string): string[] {
    try {
      const tips: string[] = [];
      const sections = response.split(/\n+/);

      for (const section of sections) {
        if (
          section.toLowerCase().includes('tip') ||
          section.toLowerCase().includes('advice') ||
          section.toLowerCase().includes('prepare')
        ) {
          const tipMatches = section.match(/[-•]\s*(.+)/g) || [];
          tipMatches.forEach((match) => {
            const tip = match.replace(/[-•]\s*/, '').trim();
            if (tip && tip.length > 10 && tip.length < 200) {
              tips.push(tip);
            }
          });
        }
      }

      return tips;
    } catch (error) {
      return [];
    }
  }

  private extractTransferableSkills(response: string): string[] {
    try {
      const skills: string[] = [];
      const sections = response.split(/\n+/);

      for (const section of sections) {
        if (
          section.toLowerCase().includes('transferable') ||
          section.toLowerCase().includes('existing skill') ||
          section.toLowerCase().includes('can leverage')
        ) {
          const skillMatches = section.match(/[-•]\s*(.+)/g) || [];
          skillMatches.forEach((match) => {
            const skill = match.replace(/[-•]\s*/, '').trim();
            if (skill && skill.length > 2 && skill.length < 50) {
              skills.push(skill.split(':')[0] || skill);
            }
          });
        }
      }

      return skills;
    } catch (error) {
      return [];
    }
  }

  private extractTransitionPlan(response: string): TransitionPlan[] {
    try {
      const plan: TransitionPlan[] = [];
      const sections = response.split(/\n+/);

      for (const section of sections) {
        if (
          section.toLowerCase().includes('phase') ||
          section.toLowerCase().includes('step') ||
          section.toLowerCase().includes('stage')
        ) {
          const phaseMatch = section.match(/(?:phase|step|stage)\s*(\d+)?[:\s]*(.+)/i);
          if (phaseMatch) {
            const phaseName = phaseMatch[2].trim();
            const actions: string[] = [];

            const lines = section.split('\n');
            lines.forEach((line) => {
              if (line.trim().startsWith('-') || line.trim().startsWith('•')) {
                actions.push(line.replace(/[-•]\s*/, '').trim());
              }
            });

            if (phaseName && actions.length > 0) {
              plan.push({
                phase: phaseName,
                duration: '3 months',
                actions: actions.slice(0, 5),
                milestones: [],
              });
            }
          }
        }
      }

      return plan;
    } catch (error) {
      return [];
    }
  }

  private extractDifficultyLevel(response: string): string | null {
    try {
      const patterns = [
        /difficulty[:\s]*(\w+)/i,
        /challenge level[:\s]*(\w+)/i,
        /(\w+)\s*difficulty/i,
      ];

      for (const pattern of patterns) {
        const match = response.match(pattern);
        if (match) {
          const difficulty = match[1].toUpperCase();
          if (['EASY', 'MODERATE', 'DIFFICULT', 'HARD', 'CHALLENGING'].includes(difficulty)) {
            return difficulty === 'HARD' || difficulty === 'CHALLENGING' ? 'DIFFICULT' : difficulty;
          }
        }
      }

      return null;
    } catch (error) {
      return null;
    }
  }
}
