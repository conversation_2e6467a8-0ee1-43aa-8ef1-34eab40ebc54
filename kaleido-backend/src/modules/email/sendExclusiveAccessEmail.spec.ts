import { ConfigService } from '@nestjs/config';
import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { render } from '@react-email/render';

import { Candidate } from '../entities';
import { JobSeeker } from '../job-seeker/entities/job-seeker.entity';
import { EmailService } from './email.service';
import { EmailHelpersService } from './email-helpers.service';

// Mock Resend
jest.mock('resend', () => ({
  Resend: jest.fn().mockImplementation(() => ({
    emails: {
      send: jest.fn(),
    },
  })),
}));

// Mock React Email render
jest.mock('@react-email/render', () => ({
  render: jest.fn().mockReturnValue('<html>Mock Email HTML</html>'),
}));

describe('EmailService - sendExclusiveAccessEmail', () => {
  let service: EmailService;
  let mockResend: any;

  const mockConfigService = {
    get: jest.fn((key: string) => {
      switch (key) {
        case 'RESEND_API_KEY':
          return 'test-api-key';
        case 'NODE_ENV':
          return 'test';
        case 'APP_URL':
          return 'https://app.kaleidotalent.com';
        default:
          return undefined;
      }
    }),
  };

  const mockJobSeekerRepository = {
    find: jest.fn(),
    findOne: jest.fn(),
    save: jest.fn(),
    create: jest.fn(),
    update: jest.fn(),
    delete: jest.fn(),
  };

  const mockCandidateRepository = {
    find: jest.fn(),
    findOne: jest.fn(),
    save: jest.fn(),
    create: jest.fn(),
    update: jest.fn(),
    delete: jest.fn(),
  };

  const mockModuleRef = {
    get: jest.fn(),
  };

  const mockEmailHelpersService = {
    validateEmailCompanyData: jest.fn(),
    ensureCompanyName: jest.fn().mockResolvedValue('Test Company'),
    getCompanyNameForJob: jest.fn().mockResolvedValue('Test Company'),
  };

  beforeEach(async () => {
    // Reset mocks
    jest.clearAllMocks();

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        EmailService,
        {
          provide: ConfigService,
          useValue: mockConfigService,
        },
        {
          provide: EmailHelpersService,
          useValue: mockEmailHelpersService,
        },
        {
          provide: getRepositoryToken(JobSeeker),
          useValue: mockJobSeekerRepository,
        },
        {
          provide: getRepositoryToken(Candidate),
          useValue: mockCandidateRepository,
        },
        {
          provide: 'ModuleRef',
          useValue: mockModuleRef,
        },
      ],
    }).compile();

    service = module.get<EmailService>(EmailService);

    // Get the mocked Resend instance
    mockResend = (service as any).resend;
    mockResend.emails.send = jest.fn().mockResolvedValue({
      id: 'test-email-id',
      from: '<EMAIL>',
      to: '<EMAIL>',
      created_at: new Date().toISOString(),
    });
  });

  describe('sendExclusiveAccessEmail', () => {
    const testEmail = '<EMAIL>';
    const testName = 'John Doe';
    const testLoginUrl = 'https://app.kaleidotalent.com';

    it('should send exclusive access email successfully', async () => {
      const result = await service.sendExclusiveAccessEmail(testEmail, testName, testLoginUrl);

      expect(result).toBeDefined();
      expect(mockResend.emails.send).toHaveBeenCalledWith({
        from: '<EMAIL>',
        to: testEmail,
        subject: "🚀 Welcome to Kaleido Talent - You're among our exclusive early adopters!",
        html: '<html>Mock Email HTML</html>',
      });
    });

    it('should handle missing optional parameters', async () => {
      const result = await service.sendExclusiveAccessEmail(testEmail, testName);

      expect(result).toBeDefined();
      expect(mockResend.emails.send).toHaveBeenCalledWith({
        from: '<EMAIL>',
        to: testEmail,
        subject: "🚀 Welcome to Kaleido Talent - You're among our exclusive early adopters!",
        html: '<html>Mock Email HTML</html>',
      });
    });

    it('should use correct email metadata', async () => {
      const result = await service.sendExclusiveAccessEmail(testEmail, testName, testLoginUrl);

      expect(result).toBeDefined();
      // The method should call sendEmail with proper metadata
      expect(mockResend.emails.send).toHaveBeenCalled();
    });

    it('should handle email sending errors gracefully', async () => {
      const errorMessage = 'Failed to send email';
      mockResend.emails.send.mockRejectedValue(new Error(errorMessage));

      await expect(
        service.sendExclusiveAccessEmail(testEmail, testName, testLoginUrl),
      ).rejects.toThrow(errorMessage);
    });

    it('should render email template with correct props', async () => {
      await service.sendExclusiveAccessEmail(testEmail, testName, testLoginUrl);

      expect(render).toHaveBeenCalled();

      // Check that render was called (the component is dynamically imported)
      expect((render as jest.Mock).mock.calls.length).toBeGreaterThan(0);
    });

    it('should handle special characters in name', async () => {
      const specialName = "José García-O'Connor";

      const result = await service.sendExclusiveAccessEmail(testEmail, specialName, testLoginUrl);

      expect(result).toBeDefined();
      expect(mockResend.emails.send).toHaveBeenCalled();
    });

    it('should handle long names', async () => {
      const longName = 'Dr. Alexander Maximilian Christopher Wellington-Smythe III';

      const result = await service.sendExclusiveAccessEmail(testEmail, longName, testLoginUrl);

      expect(result).toBeDefined();
      expect(mockResend.emails.send).toHaveBeenCalled();
    });

    it('should handle different email formats', async () => {
      const emailFormats = [
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
      ];

      for (const email of emailFormats) {
        mockResend.emails.send.mockClear();

        const result = await service.sendExclusiveAccessEmail(email, testName, testLoginUrl);

        expect(result).toBeDefined();
        expect(mockResend.emails.send).toHaveBeenCalledWith(
          expect.objectContaining({
            to: email,
          }),
        );
      }
    });

    it('should use correct subject line', async () => {
      await service.sendExclusiveAccessEmail(testEmail, testName, testLoginUrl);

      expect(mockResend.emails.send).toHaveBeenCalledWith(
        expect.objectContaining({
          subject: "🚀 Welcome to Kaleido Talent - You're among our exclusive early adopters!",
        }),
      );
    });

    it('should use correct sender email', async () => {
      await service.sendExclusiveAccessEmail(testEmail, testName, testLoginUrl);

      expect(mockResend.emails.send).toHaveBeenCalledWith(
        expect.objectContaining({
          from: '<EMAIL>',
        }),
      );
    });
  });

  describe('generateEmailPreview - exclusive_access', () => {
    it('should generate preview for exclusive_access email type', async () => {
      (render as jest.Mock).mockReturnValue('<html>Preview HTML</html>');

      const result = await service.generateEmailPreview('exclusive_access', {
        fullName: 'Preview User',
      });

      expect(result).toBe('<html>Preview HTML</html>');
      expect(render).toHaveBeenCalled();
    });

    it('should use default name when no test data provided', async () => {
      (render as jest.Mock).mockReturnValue('<html>Preview HTML</html>');

      const result = await service.generateEmailPreview('exclusive_access');

      expect(result).toBe('<html>Preview HTML</html>');
      expect(render).toHaveBeenCalled();
    });

    it('should handle preview generation errors', async () => {
      (render as jest.Mock).mockImplementation(() => {
        throw new Error('Preview generation failed');
      });

      await expect(service.generateEmailPreview('exclusive_access')).rejects.toThrow(
        'Preview generation failed',
      );
    });
  });
});
