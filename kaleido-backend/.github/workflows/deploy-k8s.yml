name: Build and Deploy to Kubernetes

on:
  push:
    branches: [ main ]
    paths:
      - 'headstart_backend/**'
  workflow_dispatch:

env:
  REGISTRY: registry.digitalocean.com
  REGISTRY_NAME: kaleido
  IMAGE_NAME: kaleido-backend
  KUBE_CLUSTER_ID: 0baeed35-fd17-4b15-92cd-73675a74569c
  NODE_VERSION: '20.x'
  PNPM_VERSION: '8.15.1'

jobs:
  # Pre-deployment tests
  pre-deploy-tests:
    name: Pre-deployment Tests
    runs-on: ubuntu-latest
    defaults:
      run:
        working-directory: ./headstart_backend

    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_PASSWORD: test
          POSTGRES_USER: test
          POSTGRES_DB: headstart_test
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432

      redis:
        image: redis:7
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 6379:6379

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}

      - name: Setup pnpm
        uses: pnpm/action-setup@v2
        with:
          version: ${{ env.PNPM_VERSION }}

      - name: Install dependencies
        run: pnpm install --frozen-lockfile

      - name: Run linting
        run: pnpm lint

      - name: Run type checking
        run: npx tsc --noEmit

      - name: Run unit tests
        run: pnpm test:ci
        env:
          NODE_ENV: test

      - name: Run integration tests
        run: pnpm test:integration
        env:
          NODE_ENV: test
          TEST_DB_HOST: localhost
          TEST_DB_PORT: 5432
          TEST_DB_USERNAME: test
          TEST_DB_PASSWORD: test
          TEST_DB_NAME: headstart_test
          REDIS_URL: redis://localhost:6379

      - name: Build application
        run: pnpm build
        env:
          NODE_ENV: production
          SKIP_SENTRY_RELEASE: true

  build-and-deploy:
    runs-on: ubuntu-latest
    needs: pre-deploy-tests
    defaults:
      run:
        working-directory: ./headstart_backend
    steps:
      - name: Checkout code
        uses: actions/checkout@v3

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v2

      - name: Install doctl
        uses: digitalocean/action-doctl@v2
        with:
          token: ${{ secrets.DIGITALOCEAN_ACCESS_TOKEN }}

      - name: Log in to DigitalOcean Container Registry
        run: doctl registry login --expiry-seconds 1200

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v2

      - name: Extract metadata (tags, labels) for Docker
        id: meta
        uses: docker/metadata-action@v4
        with:
          images: ${{ env.REGISTRY }}/${{ env.REGISTRY_NAME }}/${{ env.IMAGE_NAME }}
          tags: |
            type=sha,format=long
            type=ref,event=branch
            latest

      - name: Build and push Docker image
        uses: docker/build-push-action@v4
        with:
          context: .
          push: true
          tags: ${{ steps.meta.outputs.tags }}
          labels: ${{ steps.meta.outputs.labels }}
          cache-from: type=gha
          cache-to: type=gha,mode=max

      - name: Save DigitalOcean kubeconfig
        run: doctl kubernetes cluster kubeconfig save ${{ env.KUBE_CLUSTER_ID }}

      - name: Update Kubernetes deployment
        run: |
          # Update the image in the deployment
          kubectl set image deployment/kaleido-backend kaleido-backend=${{ env.REGISTRY }}/${{ env.REGISTRY_NAME }}/${{ env.IMAGE_NAME }}:$(echo $GITHUB_SHA | cut -c1-7)

          # Apply the deployment
          kubectl apply -f k8s/deployment.yaml

          # Apply the service
          kubectl apply -f k8s/service.yaml

          # Apply the ingress
          kubectl apply -f k8s/ingress.yaml

          # Wait for the deployment to be ready
          kubectl rollout status deployment/kaleido-backend --timeout=300s

      - name: Verify deployment
        run: |
          # Get the pod name
          POD_NAME=$(kubectl get pods -l app=kaleido-backend -o jsonpath="{.items[0].metadata.name}")

          # Check the pod logs
          kubectl logs $POD_NAME

          # Check the pod status
          kubectl get pods -l app=kaleido-backend
