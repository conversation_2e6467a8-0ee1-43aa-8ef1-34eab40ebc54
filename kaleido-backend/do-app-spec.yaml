# DigitalOcean App Platform Specification with FFmpeg Support
# This file defines your app configuration for DigitalOcean App Platform

name: kaleido-backend-ffmpeg
region: nyc

services:
- name: backend
  # Use your custom Docker image with FFmpeg
  image:
    registry_type: DOCR  # DigitalOcean Container Registry
    repository: kaleido-backend
    tag: latest

  # Alternative: Build from source (will use Dockerfile.prod)
  # source_dir: /
  # github:
  #   repo: your-username/your-repo
  #   branch: main
  # dockerfile_path: Dockerfile.prod

  # Environment configuration
  environment_slug: node-js
  instance_count: 1
  instance_size_slug: basic-xxs  # Adjust based on your needs

  # Port configuration
  http_port: 8080

  # Environment variables
  envs:
  - key: NODE_ENV
    value: production
  - key: PORT
    value: "8080"
  - key: DATABASE_URL
    scope: RUN_AND_BUILD_TIME
    type: SECRET
  - key: REDIS_URL
    scope: RUN_AND_BUILD_TIME
    type: SECRET
  - key: AUTH0_DOMAIN
    scope: RUN_AND_BUILD_TIME
    type: SECRET
  - key: AUTH0_CLIENT_ID
    scope: RUN_AND_BUILD_TIME
    type: SECRET
  - key: AUTH0_CLIENT_SECRET
    scope: RUN_AND_BUILD_TIME
    type: SECRET
  - key: OPENAI_API_KEY
    scope: RUN_AND_BUILD_TIME
    type: SECRET
  - key: DIGITAL_OCEAN_SPACES_KEY
    scope: RUN_AND_BUILD_TIME
    type: SECRET
  - key: DIGITAL_OCEAN_SPACES_SECRET
    scope: RUN_AND_BUILD_TIME
    type: SECRET
  - key: DIGITAL_OCEAN_SPACES_ENDPOINT
    scope: RUN_AND_BUILD_TIME
    type: SECRET
  - key: DIGITAL_OCEAN_SPACES_BUCKET
    scope: RUN_AND_BUILD_TIME
    type: SECRET

  # Health check
  health_check:
    http_path: /health
    initial_delay_seconds: 60
    period_seconds: 10
    timeout_seconds: 5
    success_threshold: 1
    failure_threshold: 3

  # Resource limits
  cpu_kind: shared

  # Routes
  routes:
  - path: /
    preserve_path_prefix: true

# Database (if using DigitalOcean Managed Database)
databases:
- name: headstart-db
  engine: PG
  version: "15"
  size: db-s-1vcpu-1gb  # Adjust based on your needs

# Redis (if using DigitalOcean Managed Redis)
# Note: You might want to use managed Redis instead of the built-in one
# databases:
# - name: headstart-redis
#   engine: REDIS
#   version: "7"
#   size: db-s-1vcpu-1gb

# Static sites (if you have a frontend)
# static_sites:
# - name: frontend
#   source_dir: /kaleido-app
#   github:
#     repo: your-username/your-repo
#     branch: main
#   build_command: npm run build
#   output_dir: dist
#   routes:
#   - path: /
#     preserve_path_prefix: false
