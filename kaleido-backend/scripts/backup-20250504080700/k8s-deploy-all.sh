#!/bin/bash

# Script to build, push, and deploy to Kubernetes
# This script combines the build-do-image.sh and deploy-k8s.sh scripts

# Exit on error
set -e

echo "🚀 Starting Kubernetes deployment process..."

# Step 1: Build and push Docker image
echo "🔄 Step 1: Building and pushing Docker image..."
./scripts/build-do-image.sh

# Check if the build was successful
if [ $? -ne 0 ]; then
    echo "❌ Docker image build failed. Aborting deployment."
    exit 1
fi

# Step 2: Create registry secret for Kubernetes
echo "🔄 Step 2: Creating registry secret for Kubernetes..."
kubectl create secret docker-registry docker-registry-secret \
  --docker-server=registry.digitalocean.com \
  --docker-username=$(doctl registry docker-config --read-write | jq -r '.auths | to_entries | .[0].value.username') \
  --docker-password=$(doctl registry docker-config --read-write | jq -r '.auths | to_entries | .[0].value.password') \
  --docker-email=<EMAIL> \
  --dry-run=client -o yaml | kubectl apply -f -

# Step 3: Deploy to Kubernetes
echo "🔄 Step 3: Deploying to Kubernetes..."
./scripts/deploy-k8s.sh

# Step 4: Get service URL
echo "🔄 Step 4: Getting service URL..."
echo "📊 Service Details:"
kubectl get service kaleido-backend

echo "📊 Ingress Details:"
kubectl get ingress

echo "✅ Deployment process completed!"
echo ""
echo "To access your application, use the ADDRESS from the ingress details above."
echo "If you're using a domain name, it may take some time for DNS to propagate."
