#!/bin/bash

# <PERSON>ript to build and push multi-architecture Docker image to DigitalOcean Container Registry

# Exit on error
set -e

# Configuration
DO_REGISTRY="registry.digitalocean.com"
DO_TEAM="kaleido"  # Your DigitalOcean registry name
IMAGE_NAME="kaleido-backend"
IMAGE_TAG=${IMAGE_TAG:-$(git rev-parse --short HEAD)}

# Full image name
FULL_IMAGE_NAME="${DO_REGISTRY}/${DO_TEAM}/${IMAGE_NAME}:${IMAGE_TAG}"
LATEST_IMAGE_NAME="${DO_REGISTRY}/${DO_TEAM}/${IMAGE_NAME}:latest"

echo "🔄 Building multi-architecture Docker image: ${FULL_IMAGE_NAME}"

# Create a new builder if it doesn't exist
if ! docker buildx ls | grep -q multiarch-builder; then
    echo "🔄 Creating new buildx builder..."
    docker buildx create --name multiarch-builder --use
fi

# Use the builder
docker buildx use multiarch-builder

# Build and push the multi-architecture image
echo "🔄 Building and pushing multi-architecture image..."
docker buildx build --platform linux/amd64,linux/arm64 \
    -t ${FULL_IMAGE_NAME} \
    -t ${LATEST_IMAGE_NAME} \
    --push \
    -f Dockerfile.prod .

# Check if build was successful
if [ $? -eq 0 ]; then
    echo "✅ Multi-architecture Docker image built and pushed successfully: ${FULL_IMAGE_NAME}"

    # Update deployment file
    echo "🔄 Updating deployment file with new image..."
    sed -i.bak "s|image: .*${IMAGE_NAME}:.*|image: ${FULL_IMAGE_NAME}|g" k8s/deployment.yaml
    rm -f k8s/deployment.yaml.bak

    echo "✅ Deployment file updated"
    echo ""
    echo "Next steps:"
    echo "1. Make sure you have created the Kubernetes secret for the Docker registry:"
    echo "   kubectl create secret docker-registry registry-kaleido \\"
    echo "     --docker-server=${DO_REGISTRY} \\"
    echo "     --docker-username=\$(doctl registry docker-config --read-write | jq -r '.auths | to_entries | .[0].value.username') \\"
    echo "     --docker-password=\$(doctl registry docker-config --read-write | jq -r '.auths | to_entries | .[0].value.password') \\"
    echo "     --docker-email=<EMAIL> \\"
    echo "     --dry-run=client -o yaml | kubectl apply -f -"
    echo ""
    echo "2. Deploy to Kubernetes:"
    echo "   kubectl apply -f k8s/deployment.yaml"
else
    echo "❌ Docker image build failed"
    exit 1
fi
