#!/bin/bash

# <PERSON>ript to build and push Docker image to DigitalOcean Container Registry

# Exit on error
set -e

# Configuration
DO_REGISTRY="registry.digitalocean.com"
DO_TEAM="kaleido"  # Your DigitalOcean registry name
IMAGE_NAME="kaleido-backend"
IMAGE_TAG=${IMAGE_TAG:-$(git rev-parse --short HEAD)}

# Full image name
FULL_IMAGE_NAME="${DO_REGISTRY}/${DO_TEAM}/${IMAGE_NAME}:${IMAGE_TAG}"
LATEST_IMAGE_NAME="${DO_REGISTRY}/${DO_TEAM}/${IMAGE_NAME}:latest"

echo "🔄 Building Docker image: ${FULL_IMAGE_NAME}"

# Build the Docker image
docker build -t ${FULL_IMAGE_NAME} -t ${LATEST_IMAGE_NAME} -f Dockerfile.prod .

# Check if build was successful
if [ $? -eq 0 ]; then
    echo "✅ Docker image built successfully: ${FULL_IMAGE_NAME}"

    # Log in to DigitalOcean Container Registry
    echo "🔄 Logging in to DigitalOcean Container Registry..."
    doctl registry login

    # Push the image
    echo "🔄 Pushing Docker image to registry..."
    docker push ${FULL_IMAGE_NAME}
    docker push ${LATEST_IMAGE_NAME}

    echo "✅ Docker image pushed successfully"

    # Update deployment file
    echo "🔄 Updating deployment file with new image..."
    sed -i.bak "s|image: .*${IMAGE_NAME}:.*|image: ${FULL_IMAGE_NAME}|g" k8s/deployment.yaml
    rm -f k8s/deployment.yaml.bak

    # Uncomment imagePullSecrets
    sed -i.bak "s|# imagePullSecrets:|imagePullSecrets:|g" k8s/deployment.yaml
    sed -i.bak "s|# - name: docker-registry-secret|- name: docker-registry-secret|g" k8s/deployment.yaml
    rm -f k8s/deployment.yaml.bak

    echo "✅ Deployment file updated"
    echo ""
    echo "Next steps:"
    echo "1. Create a Kubernetes secret for the Docker registry:"
    echo "   kubectl create secret docker-registry docker-registry-secret \\"
    echo "     --docker-server=${DO_REGISTRY} \\"
    echo "     --docker-username=\$(doctl registry docker-config --read-write | jq -r '.auths | to_entries | .[0].value.username') \\"
    echo "     --docker-password=\$(doctl registry docker-config --read-write | jq -r '.auths | to_entries | .[0].value.password') \\"
    echo "     --docker-email=<EMAIL>"
    echo ""
    echo "2. Deploy to Kubernetes:"
    echo "   ./scripts/deploy-k8s.sh"
else
    echo "❌ Docker image build failed"
    exit 1
fi
