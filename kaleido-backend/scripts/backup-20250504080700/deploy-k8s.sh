#!/bin/bash

# Script to deploy Headstart Backend to Kubernetes

# Configuration
NAMESPACE=${NAMESPACE:-"default"}
DEPLOYMENT_NAME=${DEPLOYMENT_NAME:-"kaleido-backend"}
IMAGE_TAG=${IMAGE_TAG:-$(git rev-parse --short HEAD)}
DOCKER_REGISTRY=${DOCKER_REGISTRY:-"registry.digitalocean.com/kaleido"}
IMAGE_NAME=${IMAGE_NAME:-"kaleido-backend"}

# Full image name
FULL_IMAGE_NAME="${DOCKER_REGISTRY}/${IMAGE_NAME}:${IMAGE_TAG}"

echo "🔄 Deploying ${DEPLOYMENT_NAME} to Kubernetes namespace ${NAMESPACE}"

# Check if kubectl is installed
if ! command -v kubectl &> /dev/null; then
    echo "❌ kubectl is not installed. Please install kubectl first."
    exit 1
fi

# Check if namespace exists
if ! kubectl get namespace ${NAMESPACE} &> /dev/null; then
    echo "🔄 Creating namespace ${NAMESPACE}..."
    kubectl create namespace ${NAMESPACE}
fi

# Apply ConfigMap and Secrets
echo "🔄 Applying ConfigMap and Secrets..."
kubectl apply -f k8s/configmap.yaml -n ${NAMESPACE}


# Update deployment image if needed
if [ -n "${IMAGE_TAG}" ]; then
    echo "🔄 Updating deployment image to ${FULL_IMAGE_NAME}..."
    # Use sed to replace the image in the deployment file
    sed -i.bak "s|image: .*${IMAGE_NAME}:.*|image: ${FULL_IMAGE_NAME}|g" k8s/deployment.yaml
    rm -f k8s/deployment.yaml.bak
fi

# Apply Redis manifests first
echo "🔄 Deploying Redis..."
kubectl apply -f k8s/redis-deployment.yaml -n ${NAMESPACE}
kubectl apply -f k8s/redis-service.yaml -n ${NAMESPACE}

# Wait for Redis to be ready
echo "🔄 Waiting for Redis to be ready..."
kubectl rollout status statefulset/redis -n ${NAMESPACE}

# Apply application manifests
echo "🔄 Deploying application..."
kubectl apply -f k8s/deployment.yaml -n ${NAMESPACE}
kubectl apply -f k8s/service.yaml -n ${NAMESPACE}
kubectl apply -f k8s/ingress.yaml -n ${NAMESPACE}

# Check deployment status
echo "🔄 Checking deployment status..."
kubectl rollout status deployment/${DEPLOYMENT_NAME} -n ${NAMESPACE}

if [ $? -eq 0 ]; then
    echo "✅ Deployment successful!"

    # Get service details
    echo "📊 Service Details:"
    kubectl get service ${DEPLOYMENT_NAME} -n ${NAMESPACE}

    # Get ingress details
    echo "📊 Ingress Details:"
    kubectl get ingress -n ${NAMESPACE}
else
    echo "❌ Deployment failed or timed out"
    exit 1
fi
