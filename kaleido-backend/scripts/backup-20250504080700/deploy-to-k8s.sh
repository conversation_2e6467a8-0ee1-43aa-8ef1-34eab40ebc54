#!/bin/bash

# Script to deploy the application to Kubernetes
# This script handles the entire deployment process

# Exit on error
set -e

echo "🚀 Starting Kubernetes deployment process..."

# Step 1: Connect to the Kubernetes cluster
echo "🔄 Step 1: Connecting to the Kubernetes cluster..."
doctl kubernetes cluster kubeconfig save 0baeed35-fd17-4b15-92cd-73675a74569c

# Step 2: Build and push the Docker image
echo "🔄 Step 2: Building and pushing the Docker image..."
./scripts/build-k8s-amd64-image.sh

# Check if the build was successful
if [ $? -ne 0 ]; then
    echo "❌ Docker image build failed. Aborting deployment."
    exit 1
fi

# Step 3: Create the registry secret
echo "🔄 Step 3: Creating registry secret..."
doctl registry kubernetes-manifest | kubectl apply -f -

# Step 4: Apply the secrets
echo "🔄 Step 4: Applying secrets..."
./scripts/apply-k8s-secrets.sh

# Step 5: Apply the deployment
echo "🔄 Step 5: Applying deployment..."
kubectl apply -f k8s/deployment.yaml

# Step 6: Apply the service
echo "🔄 Step 6: Applying service..."
kubectl apply -f k8s/service.yaml

# Step 7: Apply the ingress
echo "🔄 Step 7: Applying ingress..."
kubectl apply -f k8s/ingress.yaml

# Step 8: Wait for the deployment to be ready
echo "🔄 Step 8: Waiting for the deployment to be ready..."
kubectl rollout status deployment/kaleido-backend --timeout=300s

# Step 9: Get service URL
echo "🔄 Step 9: Getting service URL..."
echo "📊 Service Details:"
kubectl get service kaleido-backend

echo "📊 Ingress Details:"
kubectl get ingress

echo "✅ Deployment process completed!"
echo ""
echo "To access your application, use the ADDRESS from the ingress details above."
echo "Your application should be accessible at: https://api.kaleidotalent.com"
