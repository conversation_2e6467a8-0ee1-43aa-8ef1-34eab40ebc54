#!/bin/bash

# <PERSON>ript to build and push Docker image for Headstart Backend

# Configuration
DOCKER_REGISTRY=${DOCKER_REGISTRY:-"your-registry"}
IMAGE_NAME=${IMAGE_NAME:-"kaleido-backend"}
IMAGE_TAG=${IMAGE_TAG:-$(git rev-parse --short HEAD)}

# Full image name
FULL_IMAGE_NAME="${DOCKER_REGISTRY}/${IMAGE_NAME}:${IMAGE_TAG}"
LATEST_IMAGE_NAME="${DOCKER_REGISTRY}/${IMAGE_NAME}:latest"

echo "🔄 Building Docker image: ${FULL_IMAGE_NAME}"

# Build the Docker image
docker build -t ${FULL_IMAGE_NAME} -t ${LATEST_IMAGE_NAME} -f Dockerfile.prod .

# Check if build was successful
if [ $? -eq 0 ]; then
    echo "✅ Docker image built successfully: ${FULL_IMAGE_NAME}"

    # Ask if the user wants to push the image
    read -p "Do you want to push the image to the registry? (y/n): " PUSH_IMAGE

    if [[ "${PUSH_IMAGE}" =~ ^[Yy]$ ]]; then
        echo "🔄 Pushing Docker image to registry..."

        # Push the image with the specific tag
        docker push ${FULL_IMAGE_NAME}

        # Push the latest tag
        docker push ${LATEST_IMAGE_NAME}

        echo "✅ Docker image pushed successfully"
    else
        echo "⏭️ Skipping image push"
    fi
else
    echo "❌ Docker image build failed"
    exit 1
fi
