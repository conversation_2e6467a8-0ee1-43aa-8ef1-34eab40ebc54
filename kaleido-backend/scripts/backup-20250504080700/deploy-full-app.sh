#!/bin/bash

# <PERSON>ript to build and deploy the full NestJS application to Kubernetes
# This script builds the Docker image, pushes it to the registry, and deploys it to Kubernetes

set -e

# Configuration
REGISTRY="registry.digitalocean.com"
REGISTRY_NAME="kaleido"
IMAGE_NAME="kaleido-backend"
KUBE_CLUSTER_ID="0baeed35-fd17-4b15-92cd-73675a74569c"

# Generate a tag based on the current git commit and timestamp
GIT_COMMIT=$(git rev-parse --short HEAD)
TIMESTAMP=$(date +%Y%m%d%H%M%S)
TAG="${GIT_COMMIT}-${TIMESTAMP}"
FULL_IMAGE_NAME="${REGISTRY}/${REGISTRY_NAME}/${IMAGE_NAME}:${TAG}"

echo "🔄 Building and pushing image: ${FULL_IMAGE_NAME}"

# Login to DigitalOcean Container Registry
echo "🔄 Logging in to DigitalOcean Container Registry..."
doctl registry login

# Build the Docker image
echo "🔄 Building Docker image..."
docker build -t ${FULL_IMAGE_NAME} .

# Push the Docker image to DigitalOcean Container Registry
echo "🔄 Pushing Docker image to registry..."
docker push ${FULL_IMAGE_NAME}

# Update the latest tag
LATEST_IMAGE_NAME="${REGISTRY}/${REGISTRY_NAME}/${IMAGE_NAME}:latest"
docker tag ${FULL_IMAGE_NAME} ${LATEST_IMAGE_NAME}
docker push ${LATEST_IMAGE_NAME}

# Connect to Kubernetes cluster
echo "🔄 Connecting to Kubernetes cluster..."
doctl kubernetes cluster kubeconfig save ${KUBE_CLUSTER_ID}

# Update the deployment with the new image
echo "🔄 Updating Kubernetes deployment with new image..."
kubectl set image deployment/kaleido-backend kaleido-backend=${FULL_IMAGE_NAME}

# Apply the deployment
echo "🔄 Applying Kubernetes deployment..."
kubectl apply -f k8s/deployment.yaml

# Apply the service
echo "🔄 Applying Kubernetes service..."
kubectl apply -f k8s/service.yaml

# Apply the ingress
echo "🔄 Applying Kubernetes ingress..."
kubectl apply -f k8s/ingress.yaml

# Wait for the deployment to be ready
echo "🔄 Waiting for deployment to be ready..."
kubectl rollout status deployment/kaleido-backend --timeout=300s

# Verify the deployment
echo "🔄 Verifying deployment..."
POD_NAME=$(kubectl get pods -l app=kaleido-backend -o jsonpath="{.items[0].metadata.name}")
kubectl get pods -l app=kaleido-backend

echo "✅ Deployment complete!"
echo "🌐 Application is available at: https://api.kaleidotalent.com"
echo "📋 To check logs: kubectl logs ${POD_NAME}"
echo "📋 To check Redis status: kubectl exec ${POD_NAME} -- redis-cli -a \$REDIS_PASSWORD ping"
