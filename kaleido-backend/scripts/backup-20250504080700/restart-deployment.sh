#!/bin/bash

# <PERSON><PERSON>t to apply the updated deployment configuration and force a restart of the deployment

set -e

# Configuration
KUBE_CLUSTER_ID="0baeed35-fd17-4b15-92cd-73675a74569c"

# Connect to Kubernetes cluster
echo "🔄 Connecting to Kubernetes cluster..."
doctl kubernetes cluster kubeconfig save ${KUBE_CLUSTER_ID}

# Apply the updated deployment configuration
echo "🔄 Applying updated deployment configuration..."
kubectl apply -f k8s/deployment.yaml

# Force a restart of the deployment
echo "🔄 Forcing a restart of the deployment..."
kubectl rollout restart deployment/kaleido-backend

# Wait for the deployment to be ready
echo "🔄 Waiting for deployment to be ready..."
kubectl rollout status deployment/kaleido-backend --timeout=600s || true

# Get the pod name
POD_NAME=$(kubectl get pods -l app=kaleido-backend -o jsonpath="{.items[0].metadata.name}")

# Check pod status
echo "🔄 Checking pod status..."
kubectl get pods -l app=kaleido-backend

# Check pod logs
echo "🔄 Checking pod logs..."
kubectl logs ${POD_NAME} --tail=50 || true

echo "✅ Deployment restarted!"
echo "Note: The application may still be initializing. Check the logs again in a few minutes."
echo "To check the logs: kubectl logs ${POD_NAME}"
