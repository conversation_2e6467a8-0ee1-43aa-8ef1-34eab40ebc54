#!/bin/bash

# Script to debug <PERSON><PERSON> in the Kubernetes pod

set -e

# Configuration
KUBE_CLUSTER_ID="0baeed35-fd17-4b15-92cd-73675a74569c"

# Connect to Kubernetes cluster
echo "🔄 Connecting to Kubernetes cluster..."
doctl kubernetes cluster kubeconfig save ${KUBE_CLUSTER_ID}

# Get the pod name
POD_NAME=$(kubectl get pods -l app=kaleido-backend -o jsonpath="{.items[0].metadata.name}")

# Check if Redis is running
echo "🔄 Checking if Redis is running..."
kubectl exec ${POD_NAME} -- pgrep -x redis-server || echo "Redis is not running!"

# Check Redis status
echo "🔄 Checking Redis status..."
kubectl exec ${POD_NAME} -- redis-cli -a "${REDIS_PASSWORD}" ping

# Check Redis info
echo "🔄 Checking Redis info..."
kubectl exec ${POD_NAME} -- redis-cli -a "${REDIS_PASSWORD}" info server

# Check Redis clients
echo "🔄 Checking Redis clients..."
kubectl exec ${POD_NAME} -- redis-cli -a "${REDIS_PASSWORD}" info clients

# Check Redis memory
echo "🔄 Checking Redis memory..."
kubectl exec ${POD_NAME} -- redis-cli -a "${REDIS_PASSWORD}" info memory

# Check Redis keyspace
echo "🔄 Checking Redis keyspace..."
kubectl exec ${POD_NAME} -- redis-cli -a "${REDIS_PASSWORD}" info keyspace

# Check Redis keys
echo "🔄 Checking Redis keys..."
kubectl exec ${POD_NAME} -- redis-cli -a "${REDIS_PASSWORD}" keys "*"

# Check Bull queues
echo "🔄 Checking Bull queues..."
kubectl exec ${POD_NAME} -- redis-cli -a "${REDIS_PASSWORD}" keys "bull:*"

# Check application health endpoint
echo "🔄 Checking application health endpoint..."
kubectl exec ${POD_NAME} -- curl -s http://localhost:8080/health

echo "✅ Redis debug complete!"
