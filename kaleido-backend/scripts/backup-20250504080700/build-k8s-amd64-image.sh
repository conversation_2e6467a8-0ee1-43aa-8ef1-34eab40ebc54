#!/bin/bash

# Script to build and push Docker image for Kubernetes to DigitalOcean Container Registry
# This script builds specifically for amd64 architecture

# Exit on error
set -e

# Configuration
DO_REGISTRY="registry.digitalocean.com"
DO_TEAM="kaleido"  # Your DigitalOcean registry name
IMAGE_NAME="kaleido-backend-k8s"
IMAGE_TAG=${IMAGE_TAG:-$(git rev-parse --short HEAD)}

# Full image name
FULL_IMAGE_NAME="${DO_REGISTRY}/${DO_TEAM}/${IMAGE_NAME}:${IMAGE_TAG}"
LATEST_IMAGE_NAME="${DO_REGISTRY}/${DO_TEAM}/${IMAGE_NAME}:latest"

echo "🔄 Building Docker image for Kubernetes (amd64): ${FULL_IMAGE_NAME}"

# Build the Docker image for amd64 architecture
docker build --platform linux/amd64 -t ${FULL_IMAGE_NAME} -t ${LATEST_IMAGE_NAME} -f Dockerfile.k8s .

# Check if build was successful
if [ $? -eq 0 ]; then
    echo "✅ Docker image built successfully: ${FULL_IMAGE_NAME}"

    # Log in to DigitalOcean Container Registry
    echo "🔄 Logging in to DigitalOcean Container Registry..."
    doctl registry login

    # Push the image
    echo "🔄 Pushing Docker image to registry..."
    docker push ${FULL_IMAGE_NAME}
    docker push ${LATEST_IMAGE_NAME}

    echo "✅ Docker image pushed successfully"

    # Update deployment file
    echo "🔄 Updating deployment file with new image..."
    sed -i.bak "s|image: .*|image: ${FULL_IMAGE_NAME}|g" k8s/deployment.yaml
    rm -f k8s/deployment.yaml.bak

    echo "✅ Deployment file updated"
    echo ""
    echo "Next steps:"
    echo "1. Apply the updated deployment:"
    echo "   kubectl apply -f k8s/deployment.yaml"
    echo ""
    echo "2. Check the status of the pods:"
    echo "   kubectl get pods"
else
    echo "❌ Docker image build failed"
    exit 1
fi
