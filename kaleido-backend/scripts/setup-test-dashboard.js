#!/usr/bin/env node

/**
 * Setup script for test dashboard
 * Creates necessary directories and initializes test data files
 */

const fs = require('fs');
const path = require('path');

function setupTestDashboard() {
  console.log('🔧 Setting up test dashboard...');

  // Create test-reports directory in backend
  const backendReportsDir = path.join(__dirname, '..', 'test-reports');
  if (!fs.existsSync(backendReportsDir)) {
    fs.mkdirSync(backendReportsDir, { recursive: true });
    console.log('📁 Created backend test-reports directory');
  }

  // Create test-reports directory in frontend
const frontendReportsDir = path.resolve(__dirname, '..', '..', 'kaleido-app', 'test-reports');

  if (!fs.existsSync(frontendReportsDir)) {
    fs.mkdirSync(frontendReportsDir, { recursive: true });
    console.log('📁 Created frontend test-reports directory');
  }

  // Create initial empty test data files
  const backendDataPath = path.join(backendReportsDir, 'backend-tests.json');
  const frontendDataPath = path.join(frontendReportsDir, 'frontend-tests.json');
  const combinedDataPath = path.join(frontendReportsDir, 'all-tests.json');

  const initialBackendData = {
    projectName: 'headstart_backend',
    projectType: 'backend',
    framework: 'NestJS + Jest',
    collectedAt: new Date().toISOString(),
    summary: {
      totalTests: 0,
      totalSuites: 0,
      totalFiles: 0,
      passed: 0,
      failed: 0,
      skipped: 0,
      coverage: null,
    },
    testFiles: [],
  };

  const initialFrontendData = {
    projectName: 'kaleido-app',
    projectType: 'frontend',
    framework: 'Next.js + Jest + Playwright',
    collectedAt: new Date().toISOString(),
    summary: {
      totalTests: 0,
      totalSuites: 0,
      totalFiles: 0,
      passed: 0,
      failed: 0,
      skipped: 0,
      e2eTests: 0,
      coverage: null,
    },
    testFiles: [],
    e2eTests: [],
  };

  const initialCombinedData = {
    collectedAt: new Date().toISOString(),
    projects: {
      frontend: initialFrontendData,
      backend: initialBackendData,
    },
    summary: {
      totalProjects: 2,
      totalTests: 0,
      totalSuites: 0,
      totalFiles: 0,
      passed: 0,
      failed: 0,
      skipped: 0,
      e2eTests: 0,
      overallCoverage: null,
      frontendHealth: 'no-tests',
      backendHealth: 'no-tests',
    },
    testDistribution: {
      unit: 0,
      integration: 0,
      e2e: 0,
      component: 0,
      service: 0,
      controller: 0,
      api: 0,
      utility: 0,
      other: 0,
    },
  };

  // Write initial data files if they don't exist (compressed, no spaces)
  if (!fs.existsSync(backendDataPath)) {
    fs.writeFileSync(backendDataPath, JSON.stringify(initialBackendData));
    console.log('📄 Created initial backend test data');
  }

  if (!fs.existsSync(frontendDataPath)) {
    fs.writeFileSync(frontendDataPath, JSON.stringify(initialFrontendData));
    console.log('📄 Created initial frontend test data');
  }

  if (!fs.existsSync(combinedDataPath)) {
    fs.writeFileSync(combinedDataPath, JSON.stringify(initialCombinedData));
    console.log('📄 Created initial combined test data');
  }

  console.log('✅ Test dashboard setup complete!');
  console.log('');
  console.log('📋 Next steps:');
  console.log('1. Run tests with: pnpm test (in either project)');
  console.log('2. Check dashboard at: http://localhost:8080/api/test-dashboard/data');
  console.log('3. Use pnpm test:dashboard for dashboard-optimized test runs');
}

// Run setup if this script is executed directly
if (require.main === module) {
  setupTestDashboard();
}

module.exports = setupTestDashboard;
