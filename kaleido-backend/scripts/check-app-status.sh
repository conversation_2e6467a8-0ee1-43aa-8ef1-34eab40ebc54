#!/bin/bash

# Script to check the status of the application after deployment

set -e

# Configuration
KUBE_CLUSTER_ID="0baeed35-fd17-4b15-92cd-73675a74569c"

# Connect to Kubernetes cluster
echo "🔄 Connecting to Kubernetes cluster..."
doctl kubernetes cluster kubeconfig save ${KUBE_CLUSTER_ID}

# Check pod status
echo "🔄 Checking pod status..."
kubectl get pods -l app=kaleido-backend

# Get the pod name
POD_NAME=$(kubectl get pods -l app=kaleido-backend -o jsonpath="{.items[0].metadata.name}")

# Check pod logs
echo "🔄 Checking pod logs..."
kubectl logs ${POD_NAME} --tail=50

# Check service status
echo "🔄 Checking service status..."
kubectl get service kaleido-backend

# Check ingress status
echo "🔄 Checking ingress status..."
kubectl get ingress kaleido-backend-ingress

# Check DNS resolution
echo "🔄 Checking DNS resolution..."
nslookup api.kaleidotalent.com

# Check application health
echo "🔄 Checking application health..."
curl -s https://api.kaleidotalent.com/health || echo "Health endpoint not accessible"

echo "✅ Status check complete!"
