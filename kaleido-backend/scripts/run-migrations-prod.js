#!/usr/bin/env node

/**
 * Production Migration Script with Schema Fix
 *
 * This script runs migrations in production and ensures the PostgreSQL schema is set correctly.
 */

const { execSync } = require('child_process');
const path = require('path');

function log(message, level = 'info') {
  const timestamp = new Date().toISOString();
  const prefix = `[${timestamp}] [MIGRATION] [${level.toUpperCase()}]`;
  console.log(`${prefix} ${message}`);
}

async function ensurePublicSchema () {
  const { Client } = require('pg');

  const client = new Client({
    host: process.env.DB_HOST,
    port: parseInt(process.env.DB_PORT || '5432'),
    user: process.env.DB_USERNAME,
    password: process.env.DB_PASSWORD,
    database: process.env.DB_NAME,
    ssl: process.env.DB_SSL === 'require' ? { rejectUnauthorized: false } : false,
  });

  try {
    await client.connect();

    // Create public schema if it doesn't exist
    await client.query('CREATE SCHEMA IF NOT EXISTS public');

    // Set search path to public schema
    await client.query('SET search_path TO public');

    log('Public schema ensured and search path set');
  } catch (error) {
    log(`Warning: Could not ensure public schema: ${error.message}`, 'warn');
  } finally {
    await client.end();
  }
}

async function runProductionMigrations() {
  log('Starting production migration process...');

  try {
    // Set environment variables
    process.env.NODE_ENV = 'production';
    process.env.NODE_OPTIONS = '--max-old-space-size=8192';

    // First, ensure the public schema exists
    log('Ensuring public schema exists...');
    await ensurePublicSchema();

    // Use TypeORM CLI with TypeScript source files (available in Docker)
    log('Running migrations using TypeORM CLI...');

    // Register tsconfig-paths before running migration
    const command = `npx ts-node -r tsconfig-paths/register node_modules/typeorm/cli.js migration:run -d src/config/migration.config.ts`;

    log(`Executing: ${command}`);


    execSync(command, {
      cwd: path.join(__dirname, '..'),
      encoding: 'utf8',
      stdio: 'inherit',
      env: {
        ...process.env,
        NODE_ENV: 'production',
        NODE_OPTIONS: '--max-old-space-size=8192'
      }
    });

    log('Migrations completed successfully');

  } catch (error) {
    log('================================================================================', 'error');
    log('MIGRATION FAILED', 'error');
    log('================================================================================', 'error');
    log(`Error: ${error.message}`, 'error');
    
    if (error.stack) {
      log('Stack trace:', 'error');
      console.error(error.stack);
    }
    
    if (error.stdout) {
      log('Command output:', 'error');
      console.error(error.stdout.toString());
    }
    
    if (error.stderr) {
      log('Command errors:', 'error');
      console.error(error.stderr.toString());
    }
    
    log('================================================================================', 'error');
    log('Migration debugging tips:', 'error');
    log('1. Check if all migration files are valid TypeScript', 'error');
    log('2. Verify database connection and permissions', 'error');
    log('3. Try running the migration command manually', 'error');
    log('4. Check for missing dependencies or imports', 'error');
    log('================================================================================', 'error');
    
    process.exit(1);
  }
}

// Run if this script is executed directly
if (require.main === module) {
  runProductionMigrations();
}

module.exports = { runProductionMigrations };

module.exports = { runProductionMigrations };
