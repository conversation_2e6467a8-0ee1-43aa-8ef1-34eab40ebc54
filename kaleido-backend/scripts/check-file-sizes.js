#!/usr/bin/env node

/**
 * Utility to check test dashboard file sizes
 */

const fs = require('fs');
const path = require('path');

function formatBytes(bytes) {
  if (bytes === 0) return '0 Bytes';
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

function checkFileSizes() {
  console.log('📊 Test Dashboard File Sizes:');
  console.log('================================');

  const files = [
    path.join(__dirname, '..', 'test-reports', 'backend-tests.json'),
    path.resolve(__dirname, '..', '..', 'kaleido-app', 'test-reports', 'frontend-tests.json'),
    path.resolve(__dirname, '..', '..', 'kaleido-app', 'test-reports', 'all-tests.json'),
  ];

  files.forEach(filePath => {
    if (fs.existsSync(filePath)) {
      const stats = fs.statSync(filePath);
      const fileName = path.basename(filePath);
      const size = formatBytes(stats.size);
      const modified = stats.mtime.toLocaleString();

      console.log(`📄 ${fileName}:`);
      console.log(`   Size: ${size}`);
      console.log(`   Modified: ${modified}`);
      console.log('');
    } else {
      console.log(`❌ ${path.basename(filePath)}: File not found`);
    }
  });

  // Calculate total size
  let totalSize = 0;
  files.forEach(filePath => {
    if (fs.existsSync(filePath)) {
      totalSize += fs.statSync(filePath).size;
    }
  });

  console.log(`📊 Total Size: ${formatBytes(totalSize)}`);
}

// Run if executed directly
if (require.main === module) {
  checkFileSizes();
}

module.exports = checkFileSizes;
