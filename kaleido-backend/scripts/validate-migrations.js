#!/usr/bin/env node

/**
 * Validate Migrations Script
 * This script validates all migration files before deployment
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

function log(message, level = 'info') {
  const timestamp = new Date().toISOString();
  const prefix = `[${timestamp}] [VALIDATION] [${level.toUpperCase()}]`;
  console.log(`${prefix} ${message}`);
}

function validateMigrations() {
  log('Starting migration validation...');
  
  const migrationsDir = path.join(__dirname, '..', 'src', 'migrations');
  let hasErrors = false;
  
  // Check if migrations directory exists
  if (!fs.existsSync(migrationsDir)) {
    log('❌ Migrations directory does not exist: src/migrations/', 'error');
    return false;
  }
  
  // Get all TypeScript migration files
  const migrationFiles = fs.readdirSync(migrationsDir)
    .filter(file => file.endsWith('.ts'))
    .sort();
  
  if (migrationFiles.length === 0) {
    log('⚠️  No migration files found in src/migrations/', 'warn');
    return true; // Not an error, just a warning
  }
  
  log(`Found ${migrationFiles.length} migration files`);
  
  // Validate each migration file
  migrationFiles.forEach((file, index) => {
    const filePath = path.join(migrationsDir, file);
    log(`[${index + 1}/${migrationFiles.length}] Validating: ${file}`);
    
    try {
      // Read file content
      const content = fs.readFileSync(filePath, 'utf8');
      
      // Check for basic migration structure
      if (!content.includes('MigrationInterface')) {
        log(`  ❌ Missing MigrationInterface import`, 'error');
        hasErrors = true;
      }
      
      if (!content.includes('public async up')) {
        log(`  ❌ Missing up() method`, 'error');
        hasErrors = true;
      }
      
      if (!content.includes('public async down')) {
        log(`  ❌ Missing down() method`, 'error');
        hasErrors = true;
      }
      
      // Check for class export
      const classMatch = content.match(/export\s+class\s+(\w+)\s+implements\s+MigrationInterface/);
      if (!classMatch) {
        log(`  ❌ Invalid migration class structure`, 'error');
        hasErrors = true;
      } else {
        log(`  ✅ Class: ${classMatch[1]}`);
      }
      
      // Check filename matches timestamp pattern
      const timestampMatch = file.match(/^(\d{13})-(.+)\.ts$/);
      if (!timestampMatch) {
        log(`  ❌ Invalid filename format (should be: {timestamp}-{name}.ts)`, 'error');
        hasErrors = true;
      } else {
        const timestamp = new Date(parseInt(timestampMatch[1]));
        log(`  ✅ Timestamp: ${timestamp.toISOString()}`);
      }
      
      // Try to compile the file
      try {
        execSync(`npx tsc --noEmit --skipLibCheck "${filePath}"`, {
          stdio: 'pipe',
          encoding: 'utf8'
        });
        log(`  ✅ TypeScript compilation: OK`);
      } catch (compileError) {
        log(`  ❌ TypeScript compilation failed:`, 'error');
        console.error(compileError.stdout || compileError.message);
        hasErrors = true;
      }
      
    } catch (error) {
      log(`  ❌ Failed to validate: ${error.message}`, 'error');
      hasErrors = true;
    }
    
    console.log(''); // Empty line between files
  });
  
  // Summary
  if (hasErrors) {
    log('================================================================================', 'error');
    log('❌ MIGRATION VALIDATION FAILED', 'error');
    log('================================================================================', 'error');
    log('Fix the errors above before deploying', 'error');
    return false;
  } else {
    log('================================================================================');
    log('✅ All migrations validated successfully');
    log('================================================================================');
    return true;
  }
}

// Check database connection
async function checkDatabaseConnection() {
  log('Checking database connection...');
  
  const { Client } = require('pg');
  const client = new Client({
    host: process.env.DB_HOST,
    port: parseInt(process.env.DB_PORT || '5432'),
    user: process.env.DB_USERNAME,
    password: process.env.DB_PASSWORD,
    database: process.env.DB_NAME,
    ssl: process.env.DB_SSL === 'require' ? { rejectUnauthorized: false } : false,
  });
  
  try {
    await client.connect();
    log('✅ Database connection successful');
    
    // Check permissions
    const result = await client.query(`
      SELECT has_database_privilege(current_user, current_database(), 'CREATE') as can_create,
             has_table_privilege(current_user, 'pg_catalog.pg_class', 'SELECT') as can_query
    `);
    
    const perms = result.rows[0];
    if (!perms.can_create) {
      log('⚠️  Database user lacks CREATE permission', 'warn');
    }
    
    await client.end();
    return true;
  } catch (error) {
    log(`❌ Database connection failed: ${error.message}`, 'error');
    return false;
  }
}

// Main validation
async function main() {
  log('🔍 Pre-deployment Migration Validation');
  log('=====================================');
  
  let success = true;
  
  // Validate migration files
  if (!validateMigrations()) {
    success = false;
  }
  
  // Check database connection
  if (!await checkDatabaseConnection()) {
    success = false;
  }
  
  // Exit with appropriate code
  if (success) {
    log('');
    log('✅ All checks passed - ready for deployment');
    process.exit(0);
  } else {
    log('');
    log('❌ Validation failed - do not deploy');
    process.exit(1);
  }
}

// Run validation
if (require.main === module) {
  main();
}

module.exports = { validateMigrations, checkDatabaseConnection };