#!/bin/bash

# Script to build and push Docker image to DigitalOcean Container Registry
# This script should be run from the root of the project

set -e

# Configuration
REGISTRY="registry.digitalocean.com"
REGISTRY_NAME="kaleido"
IMAGE_NAME="kaleido-backend"
TAG=$(git rev-parse --short HEAD)

echo "🔄 Building and pushing Docker image to DigitalOcean Container Registry..."
echo "Registry: $REGISTRY"
echo "Registry Name: $REGISTRY_NAME"
echo "Image Name: $IMAGE_NAME"
echo "Tag: $TAG"

# Check if doctl is installed
if ! command -v doctl &> /dev/null; then
    echo "❌ doctl is not installed. Please install it first."
    echo "https://docs.digitalocean.com/reference/doctl/how-to/install/"
    exit 1
fi

# Check if docker is installed
if ! command -v docker &> /dev/null; then
    echo "❌ docker is not installed. Please install it first."
    exit 1
fi

# Login to DigitalOcean Container Registry
echo "🔄 Logging in to DigitalOcean Container Registry..."
doctl registry login --expiry-seconds 3600

# Build the Docker image
echo "🔄 Building Docker image..."
docker build -t $REGISTRY/$REGISTRY_NAME/$IMAGE_NAME:$TAG -t $REGISTRY/$REGISTRY_NAME/$IMAGE_NAME:latest -f Dockerfile.prod .

# Push the Docker image
echo "🔄 Pushing Docker image..."
docker push $REGISTRY/$REGISTRY_NAME/$IMAGE_NAME:$TAG
docker push $REGISTRY/$REGISTRY_NAME/$IMAGE_NAME:latest

echo "✅ Docker image built and pushed successfully!"
echo "Image: $REGISTRY/$REGISTRY_NAME/$IMAGE_NAME:$TAG"
echo "Image: $REGISTRY/$REGISTRY_NAME/$IMAGE_NAME:latest"
