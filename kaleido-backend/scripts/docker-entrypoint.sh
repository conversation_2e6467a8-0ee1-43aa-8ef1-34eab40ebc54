#!/bin/sh

# Docker Entrypoint Script
# This script is the entrypoint for the Docker container
# It starts Redis, runs migrations, and then the application

set -e

echo "🚀 Starting container services..."

# Start Redis
echo "🔄 Starting Redis..."
sh /app/scripts/start-redis-docker.sh

# Check Redis status (but don't fail if check fails)
echo "🔄 Checking Redis status..."
sh /app/scripts/check-redis-docker.sh || echo "❌ Redis check failed but continuing with application startup"

# Set Node.js memory options
export NODE_OPTIONS="--max-old-space-size=8192"
echo "🔧 Set Node.js memory limit to 8GB"

# Run database migrations
echo "🔄 Running database migrations..."
if [ "$SKIP_MIGRATIONS" != "true" ]; then
  echo "📋 Starting migration process..."

  # Use the new ensure-migrations script that handles all cases
  echo "🔍 Ensuring database migrations are up to date..."
  node scripts/ensure-migrations.js
  MIGRATION_EXIT_CODE=$?

  if [ $MIGRATION_EXIT_CODE -eq 0 ]; then
    echo "✅ Migrations completed successfully"
  else
    echo ""
    echo "❌❌❌❌❌❌❌❌❌❌❌❌❌❌❌❌❌❌❌❌❌❌❌❌❌❌❌❌❌❌"
    echo "❌ MIGRATION FAILED WITH EXIT CODE: $MIGRATION_EXIT_CODE"
    echo "❌❌❌❌❌❌❌❌❌❌❌❌❌❌❌❌❌❌❌❌❌❌❌❌❌❌❌❌❌❌"
    echo ""
    echo "🛑 STOPPING DEPLOYMENT - Database migrations must succeed"
    echo ""
    echo "📋 Troubleshooting steps:"
    echo "   1. Check the logs above for specific error messages"
    echo "   2. Verify your database connection settings:"
    echo "      - DB_HOST: ${DB_HOST}"
    echo "      - DB_PORT: ${DB_PORT}"
    echo "      - DB_NAME: ${DB_NAME}"
    echo "      - DB_USERNAME: ${DB_USERNAME}"
    echo "   3. Ensure the database user has CREATE/ALTER table permissions"
    echo "   4. Check if migration files exist in src/migrations/"
    echo "   5. Try running migrations manually:"
    echo "      npm run migration:run"
    echo ""
    echo "❌❌❌❌❌❌❌❌❌❌❌❌❌❌❌❌❌❌❌❌❌❌❌❌❌❌❌❌❌❌"
    echo ""
    
    # Always exit with the migration error code
    exit $MIGRATION_EXIT_CODE
  fi
else
  echo "⚠️  Migrations skipped (SKIP_MIGRATIONS=true)"
fi

# Wait a moment to ensure everything is ready
echo "⏳ Waiting for services to stabilize..."
sleep 2

# Start the application
echo "🚀 Starting application on port ${PORT:-8080}..."
node dist/main