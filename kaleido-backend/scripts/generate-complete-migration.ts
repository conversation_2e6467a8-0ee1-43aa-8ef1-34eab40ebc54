#!/usr/bin/env ts-node

/**
 * Generate Complete Migration
 * This script generates a complete initial migration from the current entity definitions
 */

import { config } from 'dotenv';
import { DataSource } from 'typeorm';
import { execSync } from 'child_process';
import * as fs from 'fs';
import * as path from 'path';
import { getDatabaseConfig, getEntityPaths } from '../src/config/database.config';
import * as entities from '../src/entities';

// Load environment variables
config();

async function generateCompleteMigration() {
  console.log('🚀 Generating complete initial migration...');
  console.log('=========================================');
  console.log('');

  try {
    // Create a temporary database name
    const tempDbName = `temp_migration_${Date.now()}`;
    const originalDbName = process.env.DB_NAME;
    
    console.log('📋 Creating temporary database for migration generation...');
    
    // Create a client to create the temporary database
    const { Client } = require('pg');
    const client = new Client({
      host: process.env.DB_HOST,
      port: parseInt(process.env.DB_PORT || '5432'),
      user: process.env.DB_USERNAME,
      password: process.env.DB_PASSWORD,
      database: 'postgres', // Connect to postgres database to create new DB
      ssl: process.env.DB_SSL === 'require' ? { rejectUnauthorized: false } : false,
    });

    await client.connect();
    
    try {
      // Create temporary database
      await client.query(`CREATE DATABASE "${tempDbName}"`);
      console.log(`✅ Created temporary database: ${tempDbName}`);
    } catch (error) {
      console.log('⚠️  Could not create temporary database, using existing database');
    } finally {
      await client.end();
    }

    // Update environment to use temporary database
    process.env.DB_NAME = tempDbName;

    // Create data source with temporary database
    const dataSource = new DataSource({
      ...getDatabaseConfig(),
      database: tempDbName,
      entities: Object.values(entities),
      synchronize: false,
      logging: false,
    });

    // Initialize connection
    await dataSource.initialize();
    console.log('✅ Connected to temporary database');

    // Create schema using synchronize
    console.log('📝 Creating schema from entities...');
    await dataSource.synchronize();
    console.log('✅ Schema created');

    // Close connection
    await dataSource.destroy();

    // Generate migration by comparing empty database with synchronized one
    process.env.DB_NAME = tempDbName;
    console.log('🔄 Generating migration SQL...');
    
    const migrationName = 'CompleteInitialSchema';
    const timestamp = Date.now();
    
    // Use TypeORM to generate the migration
    execSync(
      `NODE_OPTIONS="--max-old-space-size=8192" npx typeorm-ts-node-commonjs migration:generate -d src/config/migration.config.ts src/migrations/${timestamp}-${migrationName} --pretty`,
      {
        cwd: path.join(__dirname, '..'),
        stdio: 'inherit',
        env: {
          ...process.env,
          DB_NAME: tempDbName,
        },
      }
    );

    console.log(`✅ Migration generated: ${timestamp}-${migrationName}.ts`);

    // Clean up temporary database
    const cleanupClient = new Client({
      host: process.env.DB_HOST,
      port: parseInt(process.env.DB_PORT || '5432'),
      user: process.env.DB_USERNAME,
      password: process.env.DB_PASSWORD,
      database: 'postgres',
      ssl: process.env.DB_SSL === 'require' ? { rejectUnauthorized: false } : false,
    });

    await cleanupClient.connect();
    try {
      await cleanupClient.query(`DROP DATABASE IF EXISTS "${tempDbName}"`);
      console.log('✅ Cleaned up temporary database');
    } catch (error) {
      console.log('⚠️  Could not clean up temporary database:', error.message);
    } finally {
      await cleanupClient.end();
    }

    // Restore original database name
    process.env.DB_NAME = originalDbName;

    console.log('');
    console.log('🎉 Complete migration generated successfully!');
    console.log('');
    console.log('Next steps:');
    console.log('1. Review the generated migration file');
    console.log('2. Test it locally with: npm run migration:run');
    console.log('3. Deploy to production');

  } catch (error) {
    console.error('❌ Failed to generate migration:', error);
    process.exit(1);
  }
}

// Run the generator
generateCompleteMigration();