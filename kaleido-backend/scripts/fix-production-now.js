#!/usr/bin/env node

/**
 * Fix Production Database NOW
 * This script adds the missing columns that are causing the application to crash
 */

const { Client } = require('pg');

async function fixProductionNow() {
  console.log('🚨 Fixing Production Database');
  console.log('============================');
  console.log('');

  const client = new Client({
    host: process.env.DB_HOST,
    port: parseInt(process.env.DB_PORT || '5432'),
    user: process.env.DB_USERNAME,
    password: process.env.DB_PASSWORD,
    database: process.env.DB_NAME,
    ssl: process.env.DB_SSL === 'require' ? { rejectUnauthorized: false } : false,
  });

  try {
    await client.connect();
    console.log('✅ Connected to database');
    console.log('');

    // 1. Add referralSettings to jobs table - THIS IS THE CRITICAL FIX
    console.log('1️⃣ Adding referralSettings column to jobs table...');
    try {
      await client.query(`ALTER TABLE jobs ADD COLUMN "referralSettings" jsonb`);
      console.log('✅ Added referralSettings column to jobs table');
    } catch (error) {
      if (error.code === '42701') { // Column already exists
        console.log('✅ referralSettings column already exists in jobs table');
      } else {
        console.log('❌ Error adding referralSettings:', error.message);
      }
    }

    // 2. Add referralProgram to companies table
    console.log('\n2️⃣ Adding referralProgram column to companies table...');
    try {
      await client.query(`ALTER TABLE companies ADD COLUMN "referralProgram" jsonb`);
      console.log('✅ Added referralProgram column to companies table');
    } catch (error) {
      if (error.code === '42701') { // Column already exists
        console.log('✅ referralProgram column already exists in companies table');
      } else {
        console.log('❌ Error adding referralProgram:', error.message);
      }
    }

    // 3. Add referral columns to candidates table
    console.log('\n3️⃣ Adding referral columns to candidates table...');
    try {
      await client.query(`ALTER TABLE candidates ADD COLUMN "referralCode" character varying`);
      console.log('✅ Added referralCode column to candidates table');
    } catch (error) {
      if (error.code === '42701') {
        console.log('✅ referralCode column already exists in candidates table');
      } else {
        console.log('❌ Error adding referralCode:', error.message);
      }
    }

    try {
      await client.query(`ALTER TABLE candidates ADD COLUMN "referralId" uuid`);
      console.log('✅ Added referralId column to candidates table');
    } catch (error) {
      if (error.code === '42701') {
        console.log('✅ referralId column already exists in candidates table');
      } else {
        console.log('❌ Error adding referralId:', error.message);
      }
    }

    // 4. Create referral tables if they don't exist
    console.log('\n4️⃣ Creating referral tables...');
    
    // Create referral_partners table
    try {
      await client.query(`
        CREATE TABLE "referral_partners" (
          "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
          "createdAt" TIMESTAMP NOT NULL DEFAULT now(),
          "updatedAt" TIMESTAMP NOT NULL DEFAULT now(),
          "referralCode" character varying NOT NULL,
          "clientId" character varying NOT NULL,
          "companyId" character varying,
          "partnerName" character varying NOT NULL,
          "contactEmail" character varying NOT NULL,
          "contactPhone" character varying,
          "totalEarnings" numeric(10,2) NOT NULL DEFAULT '0',
          "pendingEarnings" numeric(10,2) NOT NULL DEFAULT '0',
          "paidEarnings" numeric(10,2) NOT NULL DEFAULT '0',
          "isActive" boolean NOT NULL DEFAULT true,
          "settings" jsonb,
          "dashboardMetrics" jsonb,
          "role" character varying DEFAULT 'referral-partner',
          "stripeAccountId" character varying,
          "stripeAccountStatus" character varying DEFAULT 'pending',
          CONSTRAINT "UQ_referral_partners_referralCode" UNIQUE ("referralCode"),
          CONSTRAINT "UQ_referral_partners_clientId" UNIQUE ("clientId"),
          CONSTRAINT "PK_referral_partners" PRIMARY KEY ("id")
        )
      `);
      console.log('✅ Created referral_partners table');
    } catch (error) {
      if (error.code === '42P07') { // Table already exists
        console.log('✅ referral_partners table already exists');
      } else {
        console.log('⚠️  Error creating referral_partners table:', error.message);
      }
    }

    // Create referrals table
    try {
      await client.query(`
        CREATE TABLE "referrals" (
          "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
          "createdAt" TIMESTAMP NOT NULL DEFAULT now(),
          "updatedAt" TIMESTAMP NOT NULL DEFAULT now(),
          "referralPartnerId" uuid NOT NULL,
          "candidateId" character varying NOT NULL,
          "jobId" character varying NOT NULL,
          "companyId" character varying,
          "clientId" character varying,
          "referralCode" character varying NOT NULL,
          "status" character varying NOT NULL DEFAULT 'PENDING',
          "bountyAmount" numeric(10,2),
          "bountyCalculation" jsonb,
          "candidateAppliedAt" TIMESTAMP,
          "candidateHiredAt" TIMESTAMP,
          "bountyPaidAt" TIMESTAMP,
          "trackingData" jsonb,
          CONSTRAINT "PK_referrals" PRIMARY KEY ("id")
        )
      `);
      console.log('✅ Created referrals table');
    } catch (error) {
      if (error.code === '42P07') {
        console.log('✅ referrals table already exists');
      } else {
        console.log('⚠️  Error creating referrals table:', error.message);
      }
    }

    // Create bounty_configurations table
    try {
      await client.query(`
        CREATE TABLE "bounty_configurations" (
          "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
          "createdAt" TIMESTAMP NOT NULL DEFAULT now(),
          "updatedAt" TIMESTAMP NOT NULL DEFAULT now(),
          "companyId" character varying,
          "jobId" character varying,
          "referralPartnerId" character varying,
          "bountyType" character varying NOT NULL DEFAULT 'PERCENTAGE',
          "percentageValue" numeric(5,2),
          "fixedAmount" numeric(10,2),
          "tieredStructure" jsonb,
          "isActive" boolean NOT NULL DEFAULT true,
          "priority" integer,
          CONSTRAINT "PK_bounty_configurations" PRIMARY KEY ("id")
        )
      `);
      console.log('✅ Created bounty_configurations table');
    } catch (error) {
      if (error.code === '42P07') {
        console.log('✅ bounty_configurations table already exists');
      } else {
        console.log('⚠️  Error creating bounty_configurations table:', error.message);
      }
    }

    // 5. Verify the critical columns exist
    console.log('\n5️⃣ Verifying critical columns...');
    const result = await client.query(`
      SELECT 
        table_name,
        column_name,
        data_type
      FROM information_schema.columns 
      WHERE table_schema = 'public' 
      AND table_name = 'jobs' 
      AND column_name = 'referralSettings'
    `);

    if (result.rows.length > 0) {
      console.log('✅ VERIFIED: jobs.referralSettings column exists!');
      console.log('\n🎉 SUCCESS! The application should now start without errors.');
    } else {
      console.log('❌ WARNING: jobs.referralSettings column still missing!');
      console.log('Please check the database manually.');
    }

  } catch (error) {
    console.error('\n❌ Failed:', error.message);
    console.error('Full error:', error);
    process.exit(1);
  } finally {
    await client.end();
  }
}

// Run immediately
fixProductionNow();