#!/bin/bash

# Script to deploy the application to Kubernetes
# This script should be run from the root of the project

set -e

# Configuration
REGISTRY="registry.digitalocean.com"
REGISTRY_NAME="kaleido"
IMAGE_NAME="kaleido-backend"
TAG=${1:-$(git rev-parse --short HEAD)}
KUBE_CLUSTER_ID="0baeed35-fd17-4b15-92cd-73675a74569c"

echo "🔄 Deploying application to Kubernetes..."
echo "Registry: $REGISTRY"
echo "Registry Name: $REGISTRY_NAME"
echo "Image Name: $IMAGE_NAME"
echo "Tag: $TAG"
echo "Kubernetes Cluster ID: $KUBE_CLUSTER_ID"

# Check if doctl is installed
if ! command -v doctl &> /dev/null; then
    echo "❌ doctl is not installed. Please install it first."
    echo "https://docs.digitalocean.com/reference/doctl/how-to/install/"
    exit 1
fi

# Check if kubectl is installed
if ! command -v kubectl &> /dev/null; then
    echo "❌ kubectl is not installed. Please install it first."
    exit 1
fi

# Save DigitalOcean kubeconfig
echo "🔄 Saving DigitalOcean kubeconfig..."
doctl kubernetes cluster kubeconfig save $KUBE_CLUSTER_ID

# Update the image in the deployment
echo "🔄 Updating image in deployment..."
kubectl set image deployment/kaleido-backend kaleido-backend=$REGISTRY/$REGISTRY_NAME/$IMAGE_NAME:$TAG

# Apply Kubernetes configurations
echo "🔄 Applying Kubernetes configurations..."
kubectl apply -f k8s/configmap.yaml
kubectl apply -f k8s/deployment.yaml
kubectl apply -f k8s/service.yaml
kubectl apply -f k8s/ingress.yaml

# Wait for the deployment to be ready
echo "🔄 Waiting for deployment to be ready..."
kubectl rollout status deployment/kaleido-backend --timeout=300s

# Get the pod name
POD_NAME=$(kubectl get pods -l app=kaleido-backend -o jsonpath="{.items[0].metadata.name}")

# Check the pod logs
echo "🔄 Checking pod logs..."
kubectl logs $POD_NAME --tail=50

# Check the pod status
echo "🔄 Checking pod status..."
kubectl get pods -l app=kaleido-backend

echo "✅ Deployment completed successfully!"
echo "Application should be accessible at: https://api.kaleidotalent.com"
