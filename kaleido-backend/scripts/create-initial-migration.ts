#!/usr/bin/env ts-node

/**
 * Create Initial Migration
 * This script creates a proper initial migration with all tables and columns
 */

import * as fs from 'fs';
import * as path from 'path';

const timestamp = Date.now();
const migrationName = `InitialSchema`;
const className = `InitialSchema${timestamp}`;

// This is the complete SQL for all tables based on your entities
const upSQL = `
    -- Enable UUID extension
    CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

    -- Create companies table
    CREATE TABLE "companies" (
        "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
        "createdAt" TIMESTAMP NOT NULL DEFAULT now(),
        "updatedAt" TIMESTAMP NOT NULL DEFAULT now(),
        "client_id" character varying NOT NULL,
        "name" character varying NOT NULL,
        "logo" character varying,
        "website" character varying,
        "bio" text,
        "size" character varying,
        "industry" character varying,
        "foundedYear" integer,
        "headquarters" character varying,
        "description" text,
        "hasOnboarded" boolean NOT NULL DEFAULT false,
        "settings" jsonb DEFAULT '{}',
        "teamSettings" jsonb DEFAULT '{}',
        "referralProgram" jsonb,
        CONSTRAINT "UQ_companies_client_id" UNIQUE ("client_id"),
        CONSTRAINT "PK_companies" PRIMARY KEY ("id")
    );

    -- Create jobs table
    CREATE TABLE "jobs" (
        "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
        "createdAt" TIMESTAMP NOT NULL DEFAULT now(),
        "updatedAt" TIMESTAMP NOT NULL DEFAULT now(),
        "jobId" character varying NOT NULL,
        "clientId" character varying NOT NULL,
        "jobTitle" character varying NOT NULL,
        "companyName" character varying,
        "companyId" character varying,
        "location" character varying,
        "jobType" character varying,
        "salary" character varying,
        "experience" character varying,
        "description" text,
        "responsibilities" text,
        "qualifications" text,
        "benefits" text,
        "status" character varying NOT NULL DEFAULT 'active',
        "isRemote" boolean DEFAULT false,
        "postedDate" TIMESTAMP,
        "applicationDeadline" TIMESTAMP,
        "requiredSkills" text[] DEFAULT '{}',
        "niceToHaveSkills" text[] DEFAULT '{}',
        "industry" character varying,
        "department" character varying,
        "reportingTo" character varying,
        "teamSize" character varying,
        "travelRequirement" character varying,
        "educationLevel" character varying,
        "careerLevel" character varying,
        "minSalary" numeric(12,2),
        "maxSalary" numeric(12,2),
        "salaryCurrency" character varying DEFAULT 'USD',
        "salaryPeriod" character varying,
        "vacationDays" integer,
        "isUrgent" boolean DEFAULT false,
        "aiJobDescription" text,
        "candidatesCount" integer DEFAULT 0,
        "viewCount" integer DEFAULT 0,
        "applicationCount" integer DEFAULT 0,
        "shortlistedCount" integer DEFAULT 0,
        "interviewCount" integer DEFAULT 0,
        "offerCount" integer DEFAULT 0,
        "hiredCount" integer DEFAULT 0,
        "rejectedCount" integer DEFAULT 0,
        "withdrawnCount" integer DEFAULT 0,
        "lastActivityAt" TIMESTAMP DEFAULT now(),
        "externalUrl" character varying,
        "applicationEmail" character varying,
        "applicationUrl" character varying,
        "customQuestions" jsonb DEFAULT '[]',
        "hiringTeam" jsonb DEFAULT '[]',
        "jobMetadata" jsonb DEFAULT '{}',
        "matchingCriteria" jsonb,
        "screeningQuestions" jsonb DEFAULT '[]',
        "workAuthorization" character varying,
        "securityClearance" character varying,
        "isConfidential" boolean DEFAULT false,
        "targetStartDate" TIMESTAMP,
        "numberOfOpenings" integer DEFAULT 1,
        "employmentType" character varying,
        "contractDuration" character varying,
        "renewalPossibility" boolean,
        "probationPeriod" character varying,
        "bonusStructure" text,
        "equityOffered" boolean,
        "relocationAssistance" boolean,
        "visaSponsorship" boolean,
        "backgroundCheckRequired" boolean,
        "drugTestRequired" boolean,
        "referenceCheckRequired" boolean,
        "portfolioRequired" boolean,
        "certificationRequired" text[] DEFAULT '{}',
        "languageRequirements" jsonb DEFAULT '[]',
        "workSchedule" character varying,
        "overtimeExpected" boolean,
        "commissionStructure" text,
        "performanceReviewCycle" character varying,
        "promotionTrack" text,
        "trainingProvided" text,
        "mentoringAvailable" boolean,
        "diversityStatement" text,
        "accessibilityInfo" text,
        "applicationInstructions" text,
        "interviewProcess" text,
        "averageResponseTime" character varying,
        "internalJobCode" character varying,
        "requisitionNumber" character varying,
        "costCenter" character varying,
        "approvalStatus" character varying,
        "approvedBy" character varying,
        "approvedDate" TIMESTAMP,
        "publishedDate" TIMESTAMP,
        "refreshedDate" TIMESTAMP,
        "sourcingChannels" text[] DEFAULT '{}',
        "referralBonus" numeric(12,2),
        "keywords" text[] DEFAULT '{}',
        "competencies" jsonb DEFAULT '[]',
        "cultureFit" text,
        "growthOpportunities" text,
        "workEnvironment" text,
        "companyValues" text[] DEFAULT '{}',
        "tools" text[] DEFAULT '{}',
        "methodologies" text[] DEFAULT '{}',
        "projects" text,
        "clientsWorkedWith" text,
        "reasonForOpening" text,
        "reportingStructure" text,
        "kpis" jsonb DEFAULT '[]',
        "compensationPhilosophy" text,
        "retentionRate" character varying,
        "interviewerNames" text[] DEFAULT '{}',
        "timeToFill" character varying,
        "sourcer" character varying,
        "recruiter" character varying,
        "hiringManager" character varying,
        "hiringManagerEmail" character varying,
        "secondaryContacts" jsonb DEFAULT '[]',
        "eeocCategory" character varying,
        "veteranStatus" character varying,
        "disabilityStatus" character varying,
        "score" integer DEFAULT 0,
        "referralSettings" jsonb,
        CONSTRAINT "UQ_jobs_jobId" UNIQUE ("jobId"),
        CONSTRAINT "PK_jobs" PRIMARY KEY ("id")
    );

    -- Create candidates table
    CREATE TABLE "candidates" (
        "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
        "createdAt" TIMESTAMP NOT NULL DEFAULT now(),
        "updatedAt" TIMESTAMP NOT NULL DEFAULT now(),
        "candidateId" character varying NOT NULL,
        "clientId" character varying NOT NULL,
        "firstName" character varying,
        "lastName" character varying,
        "email" character varying,
        "phone" character varying,
        "location" character varying,
        "resume" text,
        "resumeUrl" character varying,
        "linkedInUrl" character varying,
        "githubUrl" character varying,
        "portfolioUrl" character varying,
        "experience" character varying,
        "education" character varying,
        "currentJobTitle" character varying,
        "currentCompany" character varying,
        "expectedSalary" character varying,
        "noticePeriod" character varying,
        "skills" text[] DEFAULT '{}',
        "languages" text[] DEFAULT '{}',
        "status" character varying DEFAULT 'active',
        "source" character varying,
        "notes" text,
        "tags" text[] DEFAULT '{}',
        "profileScore" integer DEFAULT 0,
        "lastActivityAt" TIMESTAMP DEFAULT now(),
        "isActive" boolean DEFAULT true,
        "preferredLocations" text[] DEFAULT '{}',
        "willingToRelocate" boolean,
        "workAuthorization" character varying,
        "visaStatus" character varying,
        "availability" character varying,
        "profileCompleteness" integer DEFAULT 0,
        "socialProfiles" jsonb DEFAULT '{}',
        "references" jsonb DEFAULT '[]',
        "customFields" jsonb DEFAULT '{}',
        "aiSummary" text,
        "achievements" text[] DEFAULT '{}',
        "certifications" text[] DEFAULT '{}',
        "publications" text[] DEFAULT '{}',
        "patents" text[] DEFAULT '{}',
        "projects" jsonb DEFAULT '[]',
        "interests" text[] DEFAULT '{}',
        "preferredJobTypes" text[] DEFAULT '{}',
        "preferredCompanySizes" text[] DEFAULT '{}',
        "preferredIndustries" text[] DEFAULT '{}',
        "careerObjective" text,
        "coverLetter" text,
        "videoIntroUrl" character varying,
        "assessmentScores" jsonb DEFAULT '{}',
        "backgroundCheckStatus" character varying,
        "backgroundCheckDate" TIMESTAMP,
        "referenceCheckStatus" character varying,
        "referenceCheckDate" TIMESTAMP,
        "emergencyContact" jsonb,
        "dateOfBirth" date,
        "nationality" character varying,
        "maritalStatus" character varying,
        "gender" character varying,
        "veteranStatus" character varying,
        "disabilityStatus" character varying,
        "ethnicity" character varying,
        "profileImageUrl" character varying,
        "yearsOfExperience" numeric(4,1),
        "highestDegree" character varying,
        "gpa" numeric(3,2),
        "drivingLicense" boolean,
        "hasVehicle" boolean,
        "militaryService" jsonb,
        "securityClearance" character varying,
        "competencies" jsonb DEFAULT '[]',
        "softSkills" text[] DEFAULT '{}',
        "technicalSkills" text[] DEFAULT '{}',
        "industryKnowledge" text[] DEFAULT '{}',
        "toolsProficiency" jsonb DEFAULT '[]',
        "salaryHistory" jsonb DEFAULT '[]',
        "performanceReviews" jsonb DEFAULT '[]',
        "trainingCompleted" jsonb DEFAULT '[]',
        "awardsReceived" jsonb DEFAULT '[]',
        "volunteerWork" jsonb DEFAULT '[]',
        "hobbies" text[] DEFAULT '{}',
        "professionalMemberships" text[] DEFAULT '{}',
        "desiredSalary" numeric(12,2),
        "desiredSalaryCurrency" character varying DEFAULT 'USD',
        "minimumSalary" numeric(12,2),
        "openToContract" boolean,
        "contractRate" numeric(10,2),
        "contractRatePeriod" character varying,
        "preferredBenefits" text[] DEFAULT '{}',
        "dealBreakers" text[] DEFAULT '{}',
        "whyLookingForChange" text,
        "idealNextRole" text,
        "longTermGoals" text,
        "shortTermGoals" text,
        "biggestAccomplishment" text,
        "leadershipExperience" text,
        "teamworkExperience" text,
        "challengesOvercome" text,
        "workStyle" character varying,
        "managementStyle" character varying,
        "motivators" text[] DEFAULT '{}',
        "cultureFitNotes" text,
        "interviewAvailability" jsonb DEFAULT '[]',
        "timeZone" character varying,
        "skypeId" character varying,
        "zoomId" character varying,
        "teamsId" character varying,
        "slackId" character varying,
        "telegramId" character varying,
        "whatsappNumber" character varying,
        "preferredContactMethod" character varying,
        "bestTimeToContact" character varying,
        "doNotContactUntil" TIMESTAMP,
        "communicationPreferences" jsonb DEFAULT '{}',
        "marketingOptIn" boolean DEFAULT false,
        "dataProcessingConsent" boolean DEFAULT false,
        "dataProcessingConsentDate" TIMESTAMP,
        "retentionPeriod" character varying,
        "deletionRequestDate" TIMESTAMP,
        "lastProfileUpdate" TIMESTAMP DEFAULT now(),
        "profileViews" integer DEFAULT 0,
        "searchAppearances" integer DEFAULT 0,
        "shortlistCount" integer DEFAULT 0,
        "applicationCount" integer DEFAULT 0,
        "interviewCount" integer DEFAULT 0,
        "offerCount" integer DEFAULT 0,
        "rejectionCount" integer DEFAULT 0,
        "responseRate" numeric(5,2),
        "averageResponseTime" character varying,
        "lastLoginDate" TIMESTAMP,
        "accountStatus" character varying DEFAULT 'active',
        "accountCreatedBy" character varying,
        "accountCreatedDate" TIMESTAMP DEFAULT now(),
        "accountModifiedBy" character varying,
        "accountModifiedDate" TIMESTAMP DEFAULT now(),
        "duplicateOf" character varying,
        "mergedWith" character varying,
        "importBatchId" character varying,
        "externalId" character varying,
        "syncStatus" character varying,
        "syncDate" TIMESTAMP,
        "validationStatus" character varying,
        "validationErrors" jsonb DEFAULT '[]',
        "qualityScore" integer DEFAULT 0,
        "enrichmentStatus" character varying,
        "enrichmentDate" TIMESTAMP,
        "enrichmentSource" character varying,
        "manualReviewRequired" boolean DEFAULT false,
        "manualReviewNotes" text,
        "blacklisted" boolean DEFAULT false,
        "blacklistReason" text,
        "blacklistDate" TIMESTAMP,
        "watchlist" boolean DEFAULT false,
        "watchlistReason" text,
        "watchlistDate" TIMESTAMP,
        "confidential" boolean DEFAULT false,
        "confidentialNotes" text,
        "internalNotes" text,
        "recruiterNotes" text,
        "managerNotes" text,
        "score" integer DEFAULT 0,
        "resumeText" text,
        "linkedinProfileImageUrl" character varying,
        "referralCode" character varying,
        "referralId" uuid,
        CONSTRAINT "UQ_candidates_candidateId" UNIQUE ("candidateId"),
        CONSTRAINT "PK_candidates" PRIMARY KEY ("id")
    );

    -- Create referral_partners table
    CREATE TABLE "referral_partners" (
        "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
        "createdAt" TIMESTAMP NOT NULL DEFAULT now(),
        "updatedAt" TIMESTAMP NOT NULL DEFAULT now(),
        "referralCode" character varying NOT NULL,
        "clientId" character varying NOT NULL,
        "companyId" character varying,
        "partnerName" character varying NOT NULL,
        "contactEmail" character varying NOT NULL,
        "contactPhone" character varying,
        "totalEarnings" numeric(10,2) NOT NULL DEFAULT '0',
        "pendingEarnings" numeric(10,2) NOT NULL DEFAULT '0',
        "paidEarnings" numeric(10,2) NOT NULL DEFAULT '0',
        "isActive" boolean NOT NULL DEFAULT true,
        "settings" jsonb,
        "dashboardMetrics" jsonb,
        "role" character varying DEFAULT 'referral-partner',
        "stripeAccountId" character varying,
        "stripeAccountStatus" character varying DEFAULT 'pending',
        CONSTRAINT "UQ_referral_partners_referralCode" UNIQUE ("referralCode"),
        CONSTRAINT "UQ_referral_partners_clientId" UNIQUE ("clientId"),
        CONSTRAINT "PK_referral_partners" PRIMARY KEY ("id")
    );

    -- Create referrals table
    CREATE TABLE "referrals" (
        "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
        "createdAt" TIMESTAMP NOT NULL DEFAULT now(),
        "updatedAt" TIMESTAMP NOT NULL DEFAULT now(),
        "referralPartnerId" uuid NOT NULL,
        "candidateId" character varying NOT NULL,
        "jobId" character varying NOT NULL,
        "companyId" character varying,
        "clientId" character varying,
        "referralCode" character varying NOT NULL,
        "status" character varying NOT NULL DEFAULT 'PENDING',
        "bountyAmount" numeric(10,2),
        "bountyCalculation" jsonb,
        "candidateAppliedAt" TIMESTAMP,
        "candidateHiredAt" TIMESTAMP,
        "bountyPaidAt" TIMESTAMP,
        "trackingData" jsonb,
        CONSTRAINT "PK_referrals" PRIMARY KEY ("id")
    );

    -- Create bounty_configurations table
    CREATE TABLE "bounty_configurations" (
        "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
        "createdAt" TIMESTAMP NOT NULL DEFAULT now(),
        "updatedAt" TIMESTAMP NOT NULL DEFAULT now(),
        "companyId" character varying,
        "jobId" character varying,
        "referralPartnerId" character varying,
        "bountyType" character varying NOT NULL DEFAULT 'PERCENTAGE',
        "percentageValue" numeric(5,2),
        "fixedAmount" numeric(10,2),
        "tieredStructure" jsonb,
        "isActive" boolean NOT NULL DEFAULT true,
        "priority" integer,
        CONSTRAINT "PK_bounty_configurations" PRIMARY KEY ("id")
    );

    -- Create all other tables (abbreviated for brevity, but include all your entities)
    CREATE TABLE "user_roles" (
        "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
        "createdAt" TIMESTAMP NOT NULL DEFAULT now(),
        "updatedAt" TIMESTAMP NOT NULL DEFAULT now(),
        "clientId" character varying NOT NULL,
        "role" character varying NOT NULL,
        CONSTRAINT "UQ_user_roles_clientId" UNIQUE ("clientId"),
        CONSTRAINT "PK_user_roles" PRIMARY KEY ("id")
    );

    -- Add remaining tables here...

    -- Create indexes
    CREATE INDEX "IDX_companies_client_id" ON "companies" ("client_id");
    CREATE INDEX "IDX_jobs_jobId" ON "jobs" ("jobId");
    CREATE INDEX "IDX_jobs_clientId" ON "jobs" ("clientId");
    CREATE INDEX "IDX_jobs_companyId" ON "jobs" ("companyId");
    CREATE INDEX "IDX_jobs_status" ON "jobs" ("status");
    CREATE INDEX "IDX_candidates_candidateId" ON "candidates" ("candidateId");
    CREATE INDEX "IDX_candidates_clientId" ON "candidates" ("clientId");
    CREATE INDEX "IDX_candidates_email" ON "candidates" ("email");
    CREATE INDEX "IDX_referral_partners_referralCode" ON "referral_partners" ("referralCode");
    CREATE INDEX "IDX_referral_partners_clientId" ON "referral_partners" ("clientId");
    CREATE INDEX "IDX_referrals_composite" ON "referrals" ("referralPartnerId", "candidateId", "jobId");
    CREATE INDEX "IDX_referrals_referralCode" ON "referrals" ("referralCode");
    CREATE INDEX "IDX_referrals_status" ON "referrals" ("status");
    CREATE INDEX "IDX_bounty_configurations_composite" ON "bounty_configurations" ("companyId", "jobId", "referralPartnerId");

    -- Add foreign key constraints
    ALTER TABLE "referrals" ADD CONSTRAINT "FK_referrals_referralPartnerId" 
        FOREIGN KEY ("referralPartnerId") REFERENCES "referral_partners"("id") ON DELETE NO ACTION ON UPDATE NO ACTION;
    
    ALTER TABLE "candidates" ADD CONSTRAINT "FK_candidates_referralId" 
        FOREIGN KEY ("referralId") REFERENCES "referrals"("id") ON DELETE NO ACTION ON UPDATE NO ACTION;
`;

const downSQL = `
    -- Drop foreign key constraints
    ALTER TABLE "candidates" DROP CONSTRAINT IF EXISTS "FK_candidates_referralId";
    ALTER TABLE "referrals" DROP CONSTRAINT IF EXISTS "FK_referrals_referralPartnerId";

    -- Drop indexes
    DROP INDEX IF EXISTS "IDX_bounty_configurations_composite";
    DROP INDEX IF EXISTS "IDX_referrals_status";
    DROP INDEX IF EXISTS "IDX_referrals_referralCode";
    DROP INDEX IF EXISTS "IDX_referrals_composite";
    DROP INDEX IF EXISTS "IDX_referral_partners_clientId";
    DROP INDEX IF EXISTS "IDX_referral_partners_referralCode";
    DROP INDEX IF EXISTS "IDX_candidates_email";
    DROP INDEX IF EXISTS "IDX_candidates_clientId";
    DROP INDEX IF EXISTS "IDX_candidates_candidateId";
    DROP INDEX IF EXISTS "IDX_jobs_status";
    DROP INDEX IF EXISTS "IDX_jobs_companyId";
    DROP INDEX IF EXISTS "IDX_jobs_clientId";
    DROP INDEX IF EXISTS "IDX_jobs_jobId";
    DROP INDEX IF EXISTS "IDX_companies_client_id";

    -- Drop tables
    DROP TABLE IF EXISTS "bounty_configurations";
    DROP TABLE IF EXISTS "referrals";
    DROP TABLE IF EXISTS "referral_partners";
    DROP TABLE IF EXISTS "user_roles";
    DROP TABLE IF EXISTS "candidates";
    DROP TABLE IF EXISTS "jobs";
    DROP TABLE IF EXISTS "companies";
    
    -- Drop extension
    DROP EXTENSION IF EXISTS "uuid-ossp";
`;

const migrationContent = `import { MigrationInterface, QueryRunner } from 'typeorm';

export class ${className} implements MigrationInterface {
    name = '${className}';

    public async up(queryRunner: QueryRunner): Promise<void> {
        ${upSQL.split('\n').map(line => 
            line.trim() ? `await queryRunner.query(\`${line.trim()}\`);` : ''
        ).filter(Boolean).join('\n        ')}
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        ${downSQL.split('\n').map(line => 
            line.trim() ? `await queryRunner.query(\`${line.trim()}\`);` : ''
        ).filter(Boolean).join('\n        ')}
    }
}
`;

// Write the migration file
const migrationPath = path.join(__dirname, '..', 'src', 'migrations', `${timestamp}-${migrationName}.ts`);
fs.writeFileSync(migrationPath, migrationContent);

console.log(`✅ Created migration: ${migrationPath}`);
console.log('');
console.log('Note: This is a simplified version focusing on the critical tables.');
console.log('You should generate a complete migration using: npm run migration:generate');