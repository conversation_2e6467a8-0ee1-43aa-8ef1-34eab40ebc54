#!/usr/bin/env node

/**
 * Production Database Schema Synchronization Script
 *
 * This script synchronizes the database schema with TypeORM entities in production.
 * It will create missing tables and columns but won't drop existing data.
 *
 * Usage:
 *   node scripts/sync-database-schema-prod.js
 *
 * Environment Variables Required:
 *   - DB_HOST
 *   - DB_PORT
 *   - DB_USERNAME
 *   - DB_PASSWORD
 *   - DB_NAME
 *   - DB_SSL (optional)
 *
 * This is safer than init-fresh-database.js as it preserves existing data.
 * This version works by running the TypeScript sync script with ts-node.
 */

const { spawn } = require('child_process');

const path = require('path');
require('dotenv').config();

// Console colors for better output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
};

function log(message, color = colors.reset) {
  console.log(`${color}${message}${colors.reset}`);
}

async function runSyncScript () {
  log('🔄 Starting production database schema synchronization...', colors.cyan);
  log('📝 This will create missing tables/columns but preserve existing data.', colors.blue);

// Check required environment variables
const requiredEnvVars = ['DB_HOST', 'DB_NAME', 'DB_USERNAME', 'DB_PASSWORD'];
const missingVars = requiredEnvVars.filter(varName => !process.env[varName]);


  if (missingVars.length > 0) {
    log('❌ Missing required environment variables:', colors.red);
log(`   Required: ${missingVars.join(', ')}`, colors.red);

    process.exit(1);
  }

log('📡 Environment variables validated', colors.green);
log(
  `📡 Connecting to database: ${process.env.DB_HOST}:${process.env.DB_PORT || '5432'}/${
    process.env.DB_NAME
  }`,
  colors.blue,
);


// Run the TypeScript sync script using ts-node
const scriptPath = path.join(__dirname, '..', 'src', 'scripts', 'sync-schema-production.ts');


  log('🚀 Running TypeScript sync script with ts-node...', colors.yellow);

  return new Promise((resolve, reject) => {
    const child = spawn('npx', ['ts-node', scriptPath], {
      stdio: 'inherit',
      env: { ...process.env },
      cwd: process.cwd(),
    });

child.on('close', code => {
  if (code === 0) {
    log('✅ Schema synchronization completed successfully!', colors.green);
    resolve();
  } else {
    log(`❌ Schema synchronization failed with exit code ${code}`, colors.red);
    reject(new Error(`Process exited with code ${code}`));
  }
});


    child.on('error', error => {
      log(`❌ Error running sync script: ${error.message}`, colors.red);
      reject(error);
    });
  });
}




// Run the script
runSyncScript().catch(error => {
  log(`❌ Fatal error: ${error.message}`, colors.red);
process.exit(1);

});
