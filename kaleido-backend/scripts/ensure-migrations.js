#!/usr/bin/env node

/**
 * Ensure Migrations Script
 * This script ensures all migrations are applied, handling both new deployments and updates
 */

const { execSync } = require('child_process');
const { Client } = require('pg');
const path = require('path');

function log(message, level = 'info') {
  const timestamp = new Date().toISOString();
  const prefix = `[${timestamp}] [MIGRATION] [${level.toUpperCase()}]`;
  console.log(`${prefix} ${message}`);
}

async function checkDatabaseState() {
  const client = new Client({
    host: process.env.DB_HOST,
    port: parseInt(process.env.DB_PORT || '5432'),
    user: process.env.DB_USERNAME,
    password: process.env.DB_PASSWORD,
    database: process.env.DB_NAME,
    ssl: process.env.DB_SSL === 'require' ? { rejectUnauthorized: false } : false,
  });

  try {
    await client.connect();
    
    // Check if migrations table exists
    const migrationsTableResult = await client.query(`
      SELECT EXISTS (
        SELECT FROM information_schema.tables 
        WHERE table_schema = 'public' 
        AND table_name = 'migrations'
      );
    `);
    
    const migrationsTableExists = migrationsTableResult.rows[0].exists;
    
    // Check if critical tables exist
    const jobsTableResult = await client.query(`
      SELECT EXISTS (
        SELECT FROM information_schema.tables 
        WHERE table_schema = 'public' 
        AND table_name = 'jobs'
      );
    `);
    
    const jobsTableExists = jobsTableResult.rows[0].exists;
    
    // Check if referralSettings column exists
    let referralSettingsExists = false;
    if (jobsTableExists) {
      const columnResult = await client.query(`
        SELECT EXISTS (
          SELECT FROM information_schema.columns 
          WHERE table_schema = 'public' 
          AND table_name = 'jobs'
          AND column_name = 'referralSettings'
        );
      `);
      referralSettingsExists = columnResult.rows[0].exists;
    }
    
    return {
      migrationsTableExists,
      jobsTableExists,
      referralSettingsExists,
      needsInitialSetup: !migrationsTableExists || !jobsTableExists,
      needsReferralFix: jobsTableExists && !referralSettingsExists
    };
    
  } finally {
    await client.end();
  }
}

async function applyEmergencyFixes() {
  log('Applying emergency fixes for missing columns...');
  
  const client = new Client({
    host: process.env.DB_HOST,
    port: parseInt(process.env.DB_PORT || '5432'),
    user: process.env.DB_USERNAME,
    password: process.env.DB_PASSWORD,
    database: process.env.DB_NAME,
    ssl: process.env.DB_SSL === 'require' ? { rejectUnauthorized: false } : false,
  });

  try {
    await client.connect();
    
    // Add referralSettings column if missing
    try {
      await client.query(`ALTER TABLE jobs ADD COLUMN IF NOT EXISTS "referralSettings" jsonb`);
      log('Added referralSettings column to jobs table');
    } catch (error) {
      log(`Column might already exist: ${error.message}`, 'warn');
    }
    
    // Add other critical columns
    try {
      await client.query(`ALTER TABLE companies ADD COLUMN IF NOT EXISTS "referralProgram" jsonb`);
      log('Added referralProgram column to companies table');
    } catch (error) {
      log(`Column might already exist: ${error.message}`, 'warn');
    }
    
  } finally {
    await client.end();
  }
}

async function runMigrations() {
  try {
    // Check database state
    log('Checking database state...');
    const dbState = await checkDatabaseState();
    
    log('Database state:');
    log(`  - Migrations table exists: ${dbState.migrationsTableExists}`);
    log(`  - Jobs table exists: ${dbState.jobsTableExists}`);
    log(`  - ReferralSettings column exists: ${dbState.referralSettingsExists}`);
    log(`  - Needs initial setup: ${dbState.needsInitialSetup}`);
    log(`  - Needs referral fix: ${dbState.needsReferralFix}`);
    
    // Run TypeORM migrations
    log('Running TypeORM migrations...');
    
    const command = process.env.NODE_ENV === 'production'
      ? 'npx ts-node -r tsconfig-paths/register node_modules/typeorm/cli.js migration:run -d src/config/migration.config.ts'
      : 'npm run migration:run';
    
    log(`Executing command: ${command}`);
    
    try {
      execSync(command, {
        cwd: path.join(__dirname, '..'),
        stdio: 'inherit',
        env: {
          ...process.env,
          NODE_OPTIONS: '--max-old-space-size=8192'
        }
      });
      
      log('✅ Migrations completed successfully');
    } catch (migrationError) {
      // Capture and log the full error
      log('❌ MIGRATION FAILED!', 'error');
      log('='.repeat(80), 'error');
      log('Error details:', 'error');
      log(`Command: ${command}`, 'error');
      log(`Exit code: ${migrationError.status || 'unknown'}`, 'error');
      log(`Signal: ${migrationError.signal || 'none'}`, 'error');
      log(`Error message: ${migrationError.message}`, 'error');
      
      if (migrationError.stderr) {
        log('STDERR:', 'error');
        log(migrationError.stderr.toString(), 'error');
      }
      
      if (migrationError.stdout) {
        log('STDOUT:', 'error');
        log(migrationError.stdout.toString(), 'error');
      }
      
      log('='.repeat(80), 'error');
      
      // Always fail the deployment on migration errors
      log('🛑 Stopping deployment due to migration failure', 'error');
      log('To debug:', 'error');
      log('  1. Check the migration files in src/migrations/', 'error');
      log('  2. Verify database connection settings', 'error');
      log('  3. Run migrations manually to see detailed errors', 'error');
      log('  4. Check if the database user has proper permissions', 'error');
      
      throw migrationError;
    }
    
  } catch (error) {
    log('='.repeat(80), 'error');
    log('💥 CRITICAL MIGRATION ERROR', 'error');
    log('='.repeat(80), 'error');
    log(`Error type: ${error.constructor.name}`, 'error');
    log(`Error message: ${error.message}`, 'error');
    
    if (error.stack) {
      log('Stack trace:', 'error');
      log(error.stack, 'error');
    }
    
    log('='.repeat(80), 'error');
    log('🛑 DEPLOYMENT FAILED - Migrations could not be applied', 'error');
    log('='.repeat(80), 'error');
    
    // Re-throw to ensure the process exits with error
    throw error;
  }
}

// Run if executed directly
if (require.main === module) {
  runMigrations().catch(error => {
    console.error('Migration failed:', error);
    process.exit(1);
  });
}

module.exports = { runMigrations };