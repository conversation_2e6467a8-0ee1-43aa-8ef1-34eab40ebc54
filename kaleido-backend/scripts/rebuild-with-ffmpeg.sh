#!/bin/bash

# Docker Rebuild Script with FFmpeg Support
# This script rebuilds your Docker image with FFmpeg included

set -e  # Exit on any error

echo "🚀 Rebuilding Docker image with FFmpeg support..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

# Configuration
IMAGE_NAME="kaleido-backend"
TAG="latest"
DOCKERFILE="Dockerfile.prod"

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        --image-name)
            IMAGE_NAME="$2"
            shift 2
            ;;
        --tag)
            TAG="$2"
            shift 2
            ;;
        --dockerfile)
            DOCKERFILE="$2"
            shift 2
            ;;
        --help)
            echo "Usage: $0 [OPTIONS]"
            echo "Options:"
            echo "  --image-name NAME    Docker image name (default: kaleido-backend)"
            echo "  --tag TAG           Docker image tag (default: latest)"
            echo "  --dockerfile FILE   Dockerfile to use (default: Dockerfile.prod)"
            echo "  --help              Show this help message"
            exit 0
            ;;
        *)
            print_error "Unknown option: $1"
            echo "Use --help for usage information"
            exit 1
            ;;
    esac
done

# Check if Docker is installed
if ! command -v docker &> /dev/null; then
    print_error "Docker is not installed or not in PATH"
    exit 1
fi

# Check if Dockerfile exists
if [[ ! -f "$DOCKERFILE" ]]; then
    print_error "Dockerfile not found: $DOCKERFILE"
    exit 1
fi

print_status "Configuration:"
echo "  - Image name: $IMAGE_NAME"
echo "  - Tag: $TAG"
echo "  - Dockerfile: $DOCKERFILE"
echo ""

# Step 1: Clean up old images (optional)
print_step "1. Cleaning up old Docker images..."
docker image prune -f || print_warning "Could not clean up old images"

# Step 2: Build new image
print_step "2. Building Docker image with FFmpeg..."
docker build -f "$DOCKERFILE" -t "$IMAGE_NAME:$TAG" . || {
    print_error "Docker build failed!"
    exit 1
}

# Step 3: Verify FFmpeg is installed in the image
print_step "3. Verifying FFmpeg installation in Docker image..."
if docker run --rm "$IMAGE_NAME:$TAG" ffmpeg -version &> /dev/null; then
    print_status "✅ FFmpeg is successfully installed in the Docker image!"

    # Show FFmpeg version
    FFMPEG_VERSION=$(docker run --rm "$IMAGE_NAME:$TAG" ffmpeg -version | head -n 1)
    print_status "FFmpeg version: $FFMPEG_VERSION"
else
    print_error "❌ FFmpeg verification failed!"
    exit 1
fi

# Step 4: Show image size
print_step "4. Docker image information:"
docker images "$IMAGE_NAME:$TAG" --format "table {{.Repository}}\t{{.Tag}}\t{{.Size}}\t{{.CreatedAt}}"

print_status "🎉 Docker image rebuilt successfully with FFmpeg support!"
print_status "Image: $IMAGE_NAME:$TAG"

echo ""
print_status "Next steps for DigitalOcean deployment:"
echo "  1. Push the image to your container registry:"
echo "     docker tag $IMAGE_NAME:$TAG your-registry.com/$IMAGE_NAME:$TAG"
echo "     docker push your-registry.com/$IMAGE_NAME:$TAG"
echo ""
echo "  2. Update your DigitalOcean App Platform configuration to use the new image"
echo ""
echo "  3. Or if using DigitalOcean Container Registry:"
echo "     doctl registry login"
echo "     docker tag $IMAGE_NAME:$TAG registry.digitalocean.com/your-registry/$IMAGE_NAME:$TAG"
echo "     docker push registry.digitalocean.com/your-registry/$IMAGE_NAME:$TAG"
