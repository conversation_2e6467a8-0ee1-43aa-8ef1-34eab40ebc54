# Environment Configuration
NODE_ENV=development

# Auth0 Configuration
AUTH0_ISSUER=your-auth0-domain.auth0.com
AUTH0_AUDIENCE=your-auth0-audience

# Offline Development Mode
# Set to 'true' to disable JWT validation in development
# This allows you to work offline without Auth0 connectivity
# WARNING: Only use in development environment!
AUTH_OFFLINE_DEV=true

# Database Configuration
DB_HOST=localhost
DB_PORT=5432
DB_USERNAME=your_db_user
DB_PASSWORD=your_db_password
DB_NAME=your_db_name
DB_SSL=false

SYNTHESIA_API_KEY=
SYNTHESIA_CALLBACK_URL=
AWS_S3_BUCKET_NAME=
AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
AWS_REGION=
OPENAI_API_KEY=
APOLLO_API_KEY=your_apollo_api_key_here
PDL_API_KEY=your_pdl_api_key_here
REDIS_URL=
API_PORT=3001

# Digital Ocean Spaces Configuration
DO_SPACES_ENDPOINT=your-region.digitaloceanspaces.com
DO_SPACES_ACCESS_KEY_ID=your_access_key_id
DO_SPACES_SECRET_ACCESS_KEY=your_secret_access_key
DO_SPACES_REGION=your-region
DO_SPACES_BUCKET=your-bucket-name

# Email Service
RESEND_API_KEY=your_resend_api_key_here

# User limits
MAX_CANDIDATE_UPLOADS=100

# Scout Service Configuration
DEFAULT_SCOUT_SERVICE=apollo  # Options: apollo, pdl, linkedin (default: apollo)

# SLM Configuration
SLM_API_URL=http://your-hostinger-vps-ip:11434/api/generate
SLM_MODEL=llama3:8b
SLM_MAX_TOKENS=1024
SLM_TEMPERATURE=0.7

# Google Wallet Configuration
GOOGLE_WALLET_ISSUER_ID=your_google_wallet_issuer_id
GOOGLE_WALLET_SERVICE_ACCOUNT_EMAIL=<EMAIL>
GOOGLE_WALLET_PRIVATE_KEY="-----BEGIN PRIVATE KEY-----\nYOUR_PRIVATE_KEY_HERE\n-----END PRIVATE KEY-----"
FRONTEND_URL=http://localhost:3000

# Stripe Configuration
STRIPE_SECRET_KEY=sk_test_your_stripe_secret_key
STRIPE_WEBHOOK_SECRET=whsec_your_webhook_secret
STRIPE_CONNECT_CLIENT_ID=ca_your_connect_client_id
