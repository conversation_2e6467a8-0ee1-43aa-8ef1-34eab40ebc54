"use client";

import { motion } from "framer-motion";
import {
  ArrowRight,
  BarChart3,
  Check,
  CreditCard,
  DollarSign,
  LineChart,
  Send,
  Sparkles,
  TrendingUp,
  UserPlus,
  Users,
  Rocket,
  Trophy,
  Zap,
  Target,
} from "lucide-react";
import Image from "next/image";
import Link from "next/link";
import { useSearchParams } from "next/navigation";
import React, { useState } from "react";

const ReferralProgramPage: React.FC = () => {
  const [hoveredCard, setHoveredCard] = useState<number | null>(null);
  const searchParams = useSearchParams();
  const isAuthenticated = searchParams.get("auth") === "true";

  // If user is authenticated, show dashboard link instead of login
  const dashboardUrl = isAuthenticated
    ? "/referral-dashboard"
    : "https://app.kaleidotalent.com/api/auth/login?returnTo=/referral-partner/onboarding";
  const buttonText = isAuthenticated ? "Go to Dashboard" : "Login";

  const benefits = [
    {
      icon: TrendingUp,
      title: "Earn Competitive Commissions",
      description:
        "Get rewarded with attractive bounties for successful placements",
      highlight: "Up to 20% commission",
    },
    {
      icon: Users,
      title: "Exclusive Partner Network",
      description:
        "Join a community of top recruiters and talent acquisition professionals",
      highlight: "Premium access",
    },
    {
      icon: BarChart3,
      title: "Real-time Analytics",
      description:
        "Track your referrals, conversions, and earnings with our advanced dashboard",
      highlight: "Live tracking",
    },
    {
      icon: CreditCard,
      title: "Fast Payouts",
      description:
        "Receive your earnings quickly with our streamlined payment process",
      highlight: "Monthly payments",
    },
  ];

  const steps = [
    {
      number: "01",
      title: "Sign Up",
      description: "Create your referral partner account in minutes",
      icon: Rocket,
      color: "from-purple-500 to-purple-600",
    },
    {
      number: "02",
      title: "Refer Candidates",
      description: "Share job opportunities with qualified candidates",
      icon: Target,
      color: "from-blue-500 to-blue-600",
    },
    {
      number: "03",
      title: "Track Progress",
      description: "Monitor your referrals through our dashboard",
      icon: BarChart3,
      color: "from-teal-500 to-teal-600",
    },
    {
      number: "04",
      title: "Earn Rewards",
      description: "Get paid when your referrals are successfully placed",
      icon: Trophy,
      color: "from-green-500 to-green-600",
    },
  ];

  return (
    <div className="min-h-screen relative overflow-hidden">
      {/* Background Image */}
      <div className="absolute inset-0 z-0">
        <Image
          src="/images/landing/exclusive-2.webp"
          alt="Background"
          fill
          className="object-cover"
          priority
          quality={100}
        />
        {/* Gradient overlay - darker at bottom to blend with footer */}
        <div
          className="absolute inset-0"
          style={{
            background:
              "linear-gradient(to bottom, rgba(88, 28, 135, 0.1) 0%, rgba(88, 28, 135, 0.2) 20%, rgba(67, 56, 202, 0.4) 40%, rgba(49, 46, 129, 0.6) 60%, rgba(30, 27, 75, 0.8) 80%, rgba(15, 23, 42, 0.95) 90%, #0f172a 100%)",
          }}
        />
      </div>

      {/* Content */}
      <div className="relative z-10">
        {/* Hero Section - Adjusted padding */}
        <section className="pt-40 pb-24 px-6 lg:px-12">
          <div className="max-w-7xl mx-auto text-center">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}>
              <div className="inline-flex items-center bg-white/10 backdrop-blur-xl rounded-full px-4 py-2 mb-8 border border-white/20">
                <Sparkles className="w-4 h-4 text-yellow-400 mr-2" />
                <span className="text-white text-sm font-medium">
                  Join Our Referral Program
                </span>
              </div>

              <h1 className="text-5xl lg:text-7xl font-bold text-white mb-6">
                Partner with{" "}
                <span className="bg-gradient-to-r from-purple-400 to-pink-400 bg-clip-text text-transparent">
                  Kaleido Talent
                </span>
              </h1>

              <p className="text-xl lg:text-2xl text-white/80 max-w-3xl mx-auto mb-12">
                Turn your network into earnings. Refer top talent to exciting opportunities and earn competitive commissions.
              </p>

              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Link
                  href={dashboardUrl}
                  className="inline-flex items-center justify-center px-8 py-4 bg-gradient-to-r from-purple-500 to-pink-500 text-white text-lg font-semibold rounded-xl hover:from-purple-600 hover:to-pink-600 transform hover:scale-105 transition-all shadow-2xl">
                  {isAuthenticated ? "Go to Dashboard" : "Get Started Now"}
                  <ArrowRight className="w-5 h-5 ml-2" />
                </Link>
                <Link
                  href="#how-it-works"
                  className="inline-flex items-center justify-center px-8 py-4 bg-white/10 backdrop-blur-xl text-white text-lg font-semibold rounded-xl hover:bg-white/20 transition-all border border-white/20">
                  Learn More
                </Link>
              </div>
            </motion.div>
          </div>
        </section>

        {/* Benefits Section */}
        {/* <section className="py-20 px-6 lg:px-12">
          <div className="max-w-7xl mx-auto">
            <motion.div
              initial={{ opacity: 0 }}
              whileInView={{ opacity: 1 }}
              transition={{ duration: 0.5 }}
              viewport={{ once: true }}
              className="text-center mb-16"
            >
              <h2 className="text-4xl lg:text-5xl font-bold text-white mb-4">
                Why Partner with Us?
              </h2>
              <p className="text-xl text-white/70">
                Unlock exclusive benefits and grow your income
              </p>
            </motion.div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              {benefits.map((benefit, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: index * 0.1 }}
                  viewport={{ once: true }}
                  onMouseEnter={() => setHoveredCard(index)}
                  onMouseLeave={() => setHoveredCard(null)}
                  className="relative"
                >
                  <div className={`bg-white/10 backdrop-blur-xl rounded-2xl p-8 border border-white/20 h-full transition-all duration-300 ${
                    hoveredCard === index ? 'transform scale-105 bg-white/15' : ''
                  }`}>
                    <div className="flex items-start mb-4">
                      <div className="bg-gradient-to-br from-purple-500 to-pink-500 p-3 rounded-xl">
                        <benefit.icon className="w-6 h-6 text-white" />
                      </div>
                      <span className="ml-auto text-sm font-medium text-purple-300 bg-purple-500/20 px-3 py-1 rounded-full">
                        {benefit.highlight}
                      </span>
                    </div>
                    <h3 className="text-2xl font-semibold text-white mb-3">
                      {benefit.title}
                    </h3>
                    <p className="text-white/70">
                      {benefit.description}
                    </p>
                  </div>
                </motion.div>
              ))}
            </div>
          </div>
        </section> */}

        {/* How It Works Section */}
        <section id="how-it-works" className="py-20 px-6 lg:px-12">
          <div className="max-w-7xl mx-auto">
            <motion.div
              initial={{ opacity: 0 }}
              whileInView={{ opacity: 1 }}
              transition={{ duration: 0.5 }}
              viewport={{ once: true }}
              className="text-center mb-16">
              <h2 className="text-4xl lg:text-5xl font-bold text-white mb-4">
                How It Works
              </h2>
              <p className="text-xl text-white/70">
                Start earning in four simple steps
              </p>
            </motion.div>

            <div className="relative">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
                {steps.map((step, index) => (
                  <motion.div
                    key={index}
                    initial={{ opacity: 0, y: 20 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.5, delay: index * 0.1 }}
                    viewport={{ once: true }}
                    className="text-center relative">
                    <div className="relative mb-6">
                      {/* Icon Circle - Enhanced design */}
                      <div className="relative mx-auto w-28 h-28 mb-4">
                        <div
                          className={`absolute inset-0 bg-gradient-to-r ${step.color} rounded-2xl opacity-20 blur-2xl transform rotate-6`}
                        />
                        <div
                          className={`relative bg-gradient-to-br ${step.color} rounded-2xl w-28 h-28 flex items-center justify-center shadow-2xl transform transition-all duration-300 hover:scale-110 hover:rotate-3`}>
                          <step.icon className="w-12 h-12 text-white" />
                        </div>
                      </div>

                      {/* Step Number */}
                      <div className="absolute -top-3 -right-3 bg-gradient-to-br from-white/20 to-white/10 backdrop-blur-xl rounded-full w-12 h-12 flex items-center justify-center border border-white/30 shadow-lg">
                        <span className="text-sm font-bold text-white">
                          {step.number}
                        </span>
                      </div>

                      {/* Connecting Line - Improved design */}
                      {index < steps.length - 1 && (
                        <div className="hidden lg:block absolute top-12 left-[calc(50%+48px)] w-[calc(100%+8px)]">
                          <svg
                            className="w-full h-16"
                            viewBox="0 0 200 64"
                            fill="none"
                            preserveAspectRatio="none"
                            xmlns="http://www.w3.org/2000/svg">
                            <path
                              d="M0 32 C 50 32, 50 8, 100 8 C 150 8, 150 32, 200 32"
                              stroke="url(#gradient-line-${index})"
                              strokeWidth="3"
                              strokeLinecap="round"
                              fill="none"
                              className="opacity-50"
                            />
                            <path
                              d="M0 32 C 50 32, 50 8, 100 8 C 150 8, 150 32, 200 32"
                              stroke="url(#gradient-line-${index})"
                              strokeWidth="3"
                              strokeLinecap="round"
                              fill="none"
                              strokeDasharray="8 8"
                              className="animate-[dash_3s_linear_infinite]"
                            />
                            <defs>
                              <linearGradient
                                id={`gradient-line-${index}`}
                                x1="0%"
                                y1="0%"
                                x2="100%"
                                y2="0%">
                                <stop
                                  offset="0%"
                                  stopColor={index === 0 ? "#a855f7" : index === 1 ? "#3b82f6" : "#14b8a6"}
                                  stopOpacity="0.8"
                                />
                                <stop
                                  offset="50%"
                                  stopColor={index === 0 ? "#a855f7" : index === 1 ? "#3b82f6" : "#14b8a6"}
                                  stopOpacity="1"
                                />
                                <stop
                                  offset="100%"
                                  stopColor={index === 0 ? "#3b82f6" : index === 1 ? "#14b8a6" : "#10b981"}
                                  stopOpacity="0.8"
                                />
                              </linearGradient>
                            </defs>
                          </svg>
                        </div>
                      )}
                    </div>
                    <h3 className="text-xl font-semibold text-white mb-3">
                      {step.title}
                    </h3>
                    <p className="text-white/70">{step.description}</p>
                  </motion.div>
                ))}
              </div>
            </div>
          </div>
        </section>

        {/* CTA Section */}
        <section className="py-20 px-6 lg:px-12">
          <div className="max-w-4xl mx-auto">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
              viewport={{ once: true }}
              className="bg-gradient-to-r from-purple-600/30 to-pink-600/30 backdrop-blur-xl rounded-3xl p-12 border border-white/20 text-center">
              <h2 className="text-3xl lg:text-4xl font-bold text-white mb-4">
                Ready to Start Earning?
              </h2>
              <p className="text-xl text-white/80 mb-8">
                Join our referral program today and start earning commissions on successful placements.
              </p>
              <Link
                href={dashboardUrl}
                className="inline-flex items-center justify-center px-8 py-4 bg-white text-purple-600 text-lg font-semibold rounded-xl hover:bg-gray-100 transform hover:scale-105 transition-all shadow-2xl">
                {isAuthenticated ? "View Dashboard" : "Become a Partner"}
                <ArrowRight className="w-5 h-5 ml-2" />
              </Link>

              <div className="mt-8 flex items-center justify-center space-x-8 text-white/70">
                <div className="flex items-center">
                  <Check className="w-5 h-5 mr-2 text-green-400" />
                  <span>No sign-up fees</span>
                </div>
                <div className="flex items-center">
                  <Check className="w-5 h-5 mr-2 text-green-400" />
                  <span>Instant approval</span>
                </div>
                <div className="flex items-center">
                  <Check className="w-5 h-5 mr-2 text-green-400" />
                  <span>24/7 support</span>
                </div>
              </div>
            </motion.div>
          </div>
        </section>
      </div>
    </div>
  );
};

export default ReferralProgramPage;
