'use client';

import React, { useEffect, useState } from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { motion } from 'framer-motion';
import { 
  Users, 
  TrendingUp, 
  DollarSign, 
  Calendar,
  Copy,
  Check,
  ExternalLink,
  FileText,
  Download,
  LogOut
} from 'lucide-react';

interface ReferralRecord {
  id: string;
  candidateName: string;
  candidateEmail: string;
  jobTitle: string;
  companyName: string;
  status: string;
  dateReferred: string;
  bountyAmount: number;
  isPaid: boolean;
}

interface DashboardStats {
  totalReferrals: number;
  successfulPlacements: number;
  totalEarnings: number;
  pendingEarnings: number;
  conversionRate: number;
}

const ReferralDashboardPage: React.FC = () => {
  const [copied, setCopied] = useState(false);
  const [referralCode] = useState('REF-ABC12345'); // This would come from the API
  const [stats] = useState<DashboardStats>({
    totalReferrals: 24,
    successfulPlacements: 8,
    totalEarnings: 12500,
    pendingEarnings: 3200,
    conversionRate: 33.3
  });

  const [referrals] = useState<ReferralRecord[]>([
    {
      id: '1',
      candidateName: '<PERSON>',
      candidateEmail: '<EMAIL>',
      jobTitle: 'Senior Software Engineer',
      companyName: 'Tech Corp',
      status: 'Placed',
      dateReferred: '2024-01-15',
      bountyAmount: 2500,
      isPaid: true
    },
    {
      id: '2',
      candidateName: 'Jane Smith',
      candidateEmail: '<EMAIL>',
      jobTitle: 'Product Manager',
      companyName: 'Innovation Inc',
      status: 'Interviewing',
      dateReferred: '2024-01-20',
      bountyAmount: 3200,
      isPaid: false
    },
    {
      id: '3',
      candidateName: 'Mike Johnson',
      candidateEmail: '<EMAIL>',
      jobTitle: 'UX Designer',
      companyName: 'Design Studio',
      status: 'Applied',
      dateReferred: '2024-01-25',
      bountyAmount: 2000,
      isPaid: false
    }
  ]);

  const copyReferralCode = () => {
    navigator.clipboard.writeText(`https://kaleidotalent.com/jobs?ref=${referralCode}`);
    setCopied(true);
    setTimeout(() => setCopied(false), 2000);
  };

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'placed':
        return 'bg-green-100 text-green-800';
      case 'interviewing':
        return 'bg-blue-100 text-blue-800';
      case 'applied':
        return 'bg-yellow-100 text-yellow-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-50 via-white to-pink-50">
      {/* Navigation */}
      <nav className="bg-white shadow-sm border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <Image
                src="/images/logos/kaleido-logo-full.webp"
                alt="Kaleido Talent"
                width={140}
                height={35}
                className="h-8 w-auto"
              />
              <span className="ml-4 text-sm font-medium text-gray-600">
                Referral Partner Dashboard
              </span>
            </div>
            <div className="flex items-center space-x-4">
              <Link
                href="/referral-program"
                className="text-gray-600 hover:text-gray-900 text-sm font-medium">
                Program Info
              </Link>
              <button
                className="text-gray-600 hover:text-gray-900 text-sm font-medium flex items-center">
                <LogOut className="w-4 h-4 mr-1" />
                Logout
              </button>
            </div>
          </div>
        </div>
      </nav>

      {/* Dashboard Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Welcome Section */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900">Welcome back, Partner!</h1>
          <p className="text-gray-600 mt-2">
            Track your referrals and earnings from your dashboard
          </p>
        </div>

        {/* Stats Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.1 }}
            className="bg-white rounded-xl shadow-sm p-6 border border-gray-200"
          >
            <div className="flex items-center justify-between mb-4">
              <Users className="w-8 h-8 text-purple-600" />
              <span className="text-2xl font-bold text-gray-900">{stats.totalReferrals}</span>
            </div>
            <h3 className="text-sm font-medium text-gray-600">Total Referrals</h3>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.2 }}
            className="bg-white rounded-xl shadow-sm p-6 border border-gray-200"
          >
            <div className="flex items-center justify-between mb-4">
              <TrendingUp className="w-8 h-8 text-green-600" />
              <span className="text-2xl font-bold text-gray-900">{stats.conversionRate}%</span>
            </div>
            <h3 className="text-sm font-medium text-gray-600">Conversion Rate</h3>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.3 }}
            className="bg-white rounded-xl shadow-sm p-6 border border-gray-200"
          >
            <div className="flex items-center justify-between mb-4">
              <DollarSign className="w-8 h-8 text-blue-600" />
              <span className="text-2xl font-bold text-gray-900">
                ${stats.totalEarnings.toLocaleString()}
              </span>
            </div>
            <h3 className="text-sm font-medium text-gray-600">Total Earnings</h3>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.4 }}
            className="bg-white rounded-xl shadow-sm p-6 border border-gray-200"
          >
            <div className="flex items-center justify-between mb-4">
              <Calendar className="w-8 h-8 text-orange-600" />
              <span className="text-2xl font-bold text-gray-900">
                ${stats.pendingEarnings.toLocaleString()}
              </span>
            </div>
            <h3 className="text-sm font-medium text-gray-600">Pending Earnings</h3>
          </motion.div>
        </div>

        {/* Referral Link Section */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.5 }}
          className="bg-gradient-to-r from-purple-600 to-pink-600 rounded-xl p-6 text-white mb-8"
        >
          <h2 className="text-xl font-semibold mb-4">Your Referral Link</h2>
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="flex-1 bg-white/20 backdrop-blur rounded-lg px-4 py-3">
              <code className="text-sm">
                https://kaleidotalent.com/jobs?ref={referralCode}
              </code>
            </div>
            <button
              onClick={copyReferralCode}
              className="flex items-center justify-center px-6 py-3 bg-white text-purple-600 rounded-lg hover:bg-gray-100 transition-colors font-medium"
            >
              {copied ? (
                <>
                  <Check className="w-4 h-4 mr-2" />
                  Copied!
                </>
              ) : (
                <>
                  <Copy className="w-4 h-4 mr-2" />
                  Copy Link
                </>
              )}
            </button>
          </div>
        </motion.div>

        {/* Referrals Table */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.6 }}
          className="bg-white rounded-xl shadow-sm border border-gray-200"
        >
          <div className="px-6 py-4 border-b border-gray-200 flex justify-between items-center">
            <h2 className="text-xl font-semibold text-gray-900">Your Referrals</h2>
            <div className="flex gap-2">
              <button className="flex items-center px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors">
                <FileText className="w-4 h-4 mr-2" />
                Export CSV
              </button>
              <button className="flex items-center px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors">
                <Download className="w-4 h-4 mr-2" />
                Download Report
              </button>
            </div>
          </div>
          
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="bg-gray-50 border-b border-gray-200">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Candidate
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Position
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Company
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Status
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Date
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Bounty
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="divide-y divide-gray-200">
                {referrals.map((referral) => (
                  <tr key={referral.id} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div>
                        <div className="text-sm font-medium text-gray-900">
                          {referral.candidateName}
                        </div>
                        <div className="text-sm text-gray-500">
                          {referral.candidateEmail}
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-900">{referral.jobTitle}</div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-900">{referral.companyName}</div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${getStatusColor(referral.status)}`}>
                        {referral.status}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {new Date(referral.dateReferred).toLocaleDateString()}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-900">
                        ${referral.bountyAmount.toLocaleString()}
                      </div>
                      {referral.isPaid && (
                        <span className="text-xs text-green-600">Paid</span>
                      )}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                      <button className="text-purple-600 hover:text-purple-900">
                        <ExternalLink className="w-4 h-4" />
                      </button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </motion.div>

        {/* Help Section */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.7 }}
          className="mt-8 bg-gray-50 rounded-xl p-6 border border-gray-200"
        >
          <h3 className="text-lg font-semibold text-gray-900 mb-2">Need Help?</h3>
          <p className="text-gray-600 mb-4">
            Have questions about the referral program or need assistance?
          </p>
          <div className="flex flex-col sm:flex-row gap-4">
            <Link
              href="/referral-program#how-it-works"
              className="text-purple-600 hover:text-purple-700 font-medium">
              View Program Guide
            </Link>
            <a
              href="mailto:<EMAIL>"
              className="text-purple-600 hover:text-purple-700 font-medium">
              Contact Support
            </a>
          </div>
        </motion.div>
      </div>
    </div>
  );
};

export default ReferralDashboardPage;