@import "tailwindcss";

:root {
  --background: #ffffff;
  --foreground: #171717;
  --primary: #3b82f6;
  --primary-dark: #1d4ed8;
  --secondary: #8b5cf6;
  --accent: #ec4899;
  --accent-dark: #be185d;
  --success: #10b981;
  --warning: #f59e0b;
  --danger: #ef4444;
  --gray-light: #f3f4f6;
  --gray: #9ca3af;
  --gray-dark: #4b5563;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0f172a;
    --foreground: #f8fafc;
    --gray-light: #1e293b;
    --gray: #64748b;
    --gray-dark: #94a3b8;
  }
}

html {
  scroll-behavior: smooth;
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: var(--font-sans), system-ui, sans-serif;
}

/* Custom Gradient Backgrounds */
.bg-gradient-primary {
  background: linear-gradient(135deg, var(--primary) 0%, var(--secondary) 100%);
}

.bg-gradient-accent {
  background: linear-gradient(135deg, var(--accent) 0%, var(--secondary) 100%);
}

.bg-gradient-cool {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.bg-gradient-warm {
  background: linear-gradient(135deg, #f6d365 0%, #fda085 100%);
}

.bg-gradient-dark {
  background: linear-gradient(135deg, #0f172a 0%, #334155 100%);
}

/* Text Gradients */
.text-gradient-primary {
  background: linear-gradient(135deg, var(--primary) 0%, var(--secondary) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-fill-color: transparent;
}

/* Custom Shadows */
.shadow-glow {
  box-shadow: 0 0 8px 2px rgba(255, 255, 255, 0.5);
}

.text-gradient-accent {
  background: linear-gradient(135deg, var(--accent) 0%, var(--secondary) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-fill-color: transparent;
}

/* Animation Utilities */
.animate-float {
  animation: float 6s ease-in-out infinite;
}

@keyframes float {
  0% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-20px);
  }
  100% {
    transform: translateY(0px);
  }
}

.animate-rotate-words {
  animation: rotateWords 9s cubic-bezier(0.23, 1, 0.32, 1) infinite;
}

@keyframes rotateWords {
  0%, 25% {
    transform: translateY(0%);
  }
  33%, 58% {
    transform: translateY(-100%);
  }
  66%, 91% {
    transform: translateY(-200%);
  }
  100% {
    transform: translateY(0%);
  }
}

/* Dash animation for connecting lines */
@keyframes dash {
  to {
    stroke-dashoffset: -16;
  }
}

/* Background Patterns */
.bg-grid-pattern {
  background-image:
    linear-gradient(to right, rgba(0, 0, 0, 0.1) 1px, transparent 1px),
    linear-gradient(to bottom, rgba(0, 0, 0, 0.1) 1px, transparent 1px);
  background-size: 20px 20px;
}
