'use client';

import React from 'react';

import Image from 'next/image';
import Link from 'next/link';
import { usePathname } from "next/navigation";

import { useTranslation } from "@/hooks/useTranslation";

// Smooth scroll utility function
const scrollToSection = (sectionId: string, pathname?: string) => {
  // If we're not on the home page, redirect to home page with the section
  if (pathname && pathname !== "/") {
    window.location.href = `/#${sectionId}`;
    return;
  }

  console.log("Attempting to scroll to section:", sectionId);
  const element = document.getElementById(sectionId);
  if (element) {
    console.log("Element found:", element);
    const headerOffset = 80; // Account for fixed header height
    const elementPosition = element.getBoundingClientRect().top;
    const offsetPosition = elementPosition + window.scrollY - headerOffset;

    console.log("Scrolling to position:", offsetPosition);
    window.scrollTo({
      top: offsetPosition,
      behavior: "smooth",
    });
  } else {
    console.error("Element not found with ID:", sectionId);
  }
};

interface FooterProps {
  className?: string;
}

export const Footer: React.FC<FooterProps> = ({ className = "" }) => {
  const { t } = useTranslation();
  const pathname = usePathname();
  const currentYear = new Date().getFullYear();

  return (
    <footer className={`bg-gradient-dark text-white py-16 ${className}`}>
      <div className="container mx-auto px-6">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-12">
          <div>
            <div className="mb-6">
              <Image
                src="/images/logos/kaleido-logo-full-white.webp"
                alt={t("common.ui.logo.alt")}
                width={240}
                height={60}
                className="h-14 w-auto"
              />
            </div>
            <p className="text-gray-300 mb-6">{t("about.description")}</p>
            <p className="text-gray-300">
              {t("about.address")}
              <br />
              {t("about.email")}
            </p>
          </div>

          <div>
            <h3 className="text-xl font-semibold mb-6">
              {t("common.footer.quickLinks")}
            </h3>
            <ul className="space-y-3">
              <li>
                <button
                  type="button"
                  onClick={() => scrollToSection("home", pathname)}
                  className="text-gray-300 hover:text-white transition-colors text-left cursor-pointer">
                  {t("common.navigation.home")}
                </button>
              </li>
              <li>
                <button
                  type="button"
                  onClick={() => scrollToSection("problem", pathname)}
                  className="text-gray-300 hover:text-white transition-colors text-left cursor-pointer">
                  {t("common.navigation.problem")}
                </button>
              </li>
              <li>
                <button
                  type="button"
                  onClick={() => scrollToSection("solutions", pathname)}
                  className="text-gray-300 hover:text-white transition-colors text-left cursor-pointer">
                  {t("common.navigation.solutions")}
                </button>
              </li>
              <li>
                <button
                  type="button"
                  onClick={() => scrollToSection("mission", pathname)}
                  className="text-gray-300 hover:text-white transition-colors text-left cursor-pointer">
                  {t("common.navigation.mission")}
                </button>
              </li>
              <li>
                <button
                  type="button"
                  onClick={() => scrollToSection("contact", pathname)}
                  className="text-gray-300 hover:text-white transition-colors text-left cursor-pointer">
                  {t("common.navigation.contact")}
                </button>
              </li>
              <li>
                <a
                  href={`${process.env.NEXT_PUBLIC_APP_URL}/referral-partner/onboarding`}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-gray-300 hover:text-white transition-colors inline-flex items-center">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    className="h-4 w-4 mr-1"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor">
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z"
                    />
                  </svg>
                  Referral Program
                </a>
              </li>
            </ul>
          </div>

          <div>
            <h3 className="text-xl font-semibold mb-6">
              {t("common.footer.contact")}
            </h3>
            <div className="flex space-x-4 rtl:space-x-reverse mb-6">
              <a
                href="https://www.linkedin.com/company/kaleido-talent/"
                target="_blank"
                rel="noopener noreferrer"
                className="text-gray-300 hover:text-white transition-colors"
                aria-label="LinkedIn"
                title="LinkedIn">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  className="h-6 w-6"
                  fill="currentColor"
                  viewBox="0 0 24 24"
                  aria-hidden="true">
                  <path d="M4.98 3.5c0 1.381-1.11 2.5-2.48 2.5s-2.48-1.119-2.48-2.5c0-1.38 1.11-2.5 2.48-2.5s2.48 1.12 2.48 2.5zm.02 4.5h-5v16h5v-16zm7.982 0h-4.968v16h4.969v-8.399c0-4.67 6.029-5.052 6.029 0v8.399h4.988v-10.131c0-7.88-8.922-7.593-11.018-3.714v-2.155z" />
                </svg>
                <span className="sr-only">LinkedIn</span>
              </a>
              <a
                href="https://twitter.com/KaleidoTalent"
                target="_blank"
                rel="noopener noreferrer"
                className="text-gray-300 hover:text-white transition-colors"
                aria-label="Twitter"
                title="Twitter">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  className="h-6 w-6"
                  fill="currentColor"
                  viewBox="0 0 24 24"
                  aria-hidden="true">
                  <path d="M24 4.557c-.883.392-1.832.656-2.828.775 1.017-.609 1.798-1.574 2.165-2.724-.951.564-2.005.974-3.127 1.195-.897-.957-2.178-1.555-3.594-1.555-3.179 0-5.515 2.966-4.797 6.045-4.091-.205-7.719-2.165-10.148-5.144-1.29 2.213-.669 5.108 1.523 6.574-.806-.026-1.566-.247-2.229-.616-.054 2.281 1.581 4.415 3.949 4.89-.693.188-1.452.232-2.224.084.626 1.956 2.444 3.379 4.6 3.419-2.07 1.623-4.678 2.348-7.29 2.04 2.179 1.397 4.768 2.212 7.548 2.212 9.142 0 14.307-7.721 13.995-14.646.962-.695 1.797-1.562 2.457-2.549z" />
                </svg>
                <span className="sr-only">Twitter</span>
              </a>
            </div>
            <p className="text-gray-300">
              <a
                href="mailto:<EMAIL>"
                className="hover:text-white transition-colors">
                {t("contact.email")}
              </a>
            </p>
            <p className="text-gray-300 mt-2">{t("contact.location")}</p>
          </div>
        </div>

        <div className="border-t border-gray-700 mt-12 pt-8 flex flex-col md:flex-row justify-between items-center text-gray-400">
          <p>{t("common.footer.copyright")}</p>
          <p className="mt-2 md:mt-0">{t("common.footer.basedIn")}</p>
          <div className="mt-4 md:mt-0 flex space-x-4 rtl:space-x-reverse">
            <Link href="/terms" className="hover:text-white transition-colors">
              {t("common.footer.termsConditions")}
            </Link>
            <Link
              href="/privacy"
              className="hover:text-white transition-colors">
              {t("common.footer.privacyPolicy")}
            </Link>
            <Link
              href="/cookie-policy"
              className="hover:text-white transition-colors">
              {t("common.footer.cookiePolicy")}
            </Link>
          </div>
          <button
            type="button"
            onClick={() => window.scrollTo({ top: 0, behavior: "smooth" })}
            className="mt-6 md:mt-0 flex items-center text-gray-300 hover:text-white transition-colors"
            aria-label={t("common.footer.scrollToTop")}>
            {t("common.footer.scrollToTop")}
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="h-5 w-5 ms-1"
              viewBox="0 0 20 20"
              fill="currentColor"
              aria-hidden="true">
              <path
                fillRule="evenodd"
                d="M14.707 12.707a1 1 0 01-1.414 0L10 9.414l-3.293 3.293a1 1 0 01-1.414-1.414l4-4a1 1 0 011.414 0l4 4a1 1 0 010 1.414z"
                clipRule="evenodd"
              />
            </svg>
          </button>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
