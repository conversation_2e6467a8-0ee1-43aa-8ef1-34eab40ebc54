# Kaleido Website Pricing Synchronization Update

## Overview

Updated the kaleido-website locales (`src/locales/en.json`) to align with the current backend and frontend pricing structure from the main application.

## Key Changes Made

### 1. **Plan Structure Alignment**
- **Added STARTUP Plan**: Added the missing "Trailblazer" (Startup) plan that exists in the backend
- **Updated Plan Names**: Aligned creative names with backend plan structure:
  - FREE → "Explorer" (Test the waters)
  - STARTUP → "Trailblazer" (For solo pioneers)
  - STARTER → "Growth Hacker" (Scale your team)
  - PROFESSIONAL → "Power Player" (Dominate your market)
  - ENTERPRISE → "Enterprise Elite" (Unlimited possibilities)

### 2. **Pricing Updates**
- **FREE**: $0 (unchanged)
- **STARTUP**: $40/month (added)
- **STARTER**: $150/month (unchanged)
- **PROFESSIONAL**: $360/month (unchanged)
- **ENTERPRISE**: Contact us (unchanged)

### 3. **Credit Allocations**
Updated to match backend configuration:

| Plan | Monthly Credits | Yearly Credits |
|------|----------------|----------------|
| Free | 5 credits | 60 credits |
| Startup | 92 credits | 1,104 credits |
| Starter | 359 credits | 4,308 credits |
| Professional | 885 credits | 10,620 credits |
| Enterprise | Unlimited | Unlimited |

### 4. **New Features Added**
- **Video JD Monthly Limit**: Added this feature to all plans
  - Free: 0 videos/month
  - Startup: 1 video/month
  - Starter: 3 videos/month
  - Professional: 10 videos/month
  - Enterprise: 20 videos/month

### 5. **Feature Values Updated**
All feature values now match the backend configuration in `headstart_backend/src/shared/constants/subscription-plans.ts`:

- **Video JD Max Duration**: 60s → 60s → 90s → 3min → 5min
- **Database Retention**: 1 → 1 → 3 → 12 → 24 months
- **ATS Integration**: Basic → Basic → Basic → Advanced → Custom
- **All other features**: Aligned with backend feature flags

### 6. **FAQ Updates**
- Updated pricing references in FAQ answers
- Changed plan names from old structure to new creative names
- Updated free plan description from "Freemium" to "Explorer"

### 7. **Yearly Feature Values**
- Added complete `featureValuesYearly` section that was missing
- Includes all plans with yearly credit allocations
- Maintains same feature structure as monthly plans

## Backend Source Configuration

The updates are based on the authoritative backend configuration:
- **Plans**: `headstart_backend/src/shared/constants/subscription-plans.ts`
- **Credit Costs**: `headstart_backend/src/shared/enums/subscription-limit-type.enum.ts`
- **Frontend Types**: `kaleido-app/src/types/subscription.ts`

## Current Credit Costs (for reference)

| Action | Cost | Description |
|--------|------|-------------|
| Resume Upload | 1 credit | Per resume uploaded |
| Scout | 2 credits | Per candidate scouted |
| Match & Rank | 1 credit | Per candidate matched/ranked |
| Video JD | 8-25 credits | Variable based on length (short/medium/long) |
| Culture Fit Questions | 1 credit | Per question set |
| Unlock Candidate Details | 2 credits | Per candidate detail unlock |

## Validation

The website pricing now accurately reflects:
✅ Current backend plan structure
✅ Correct credit allocations per plan
✅ Accurate feature limitations
✅ Proper video JD monthly limits
✅ Aligned database retention periods
✅ Consistent ATS integration levels
✅ Updated FAQ information

## Next Steps

1. **Review**: Verify the updated pricing displays correctly on the website
2. **Test**: Ensure all plan comparisons work with the new structure
3. **Monitor**: Check that the startup plan is properly handled in website components
4. **Sync**: Keep this configuration in sync with any future backend pricing changes

## Files Modified

- `kaleido-website/src/locales/en.json` - Complete pricing structure update
- `kaleido-website/PRICING_SYNC_UPDATE.md` - This documentation file

The website pricing is now fully synchronized with the backend application! 🎉
