{"collectedAt": "2025-08-03T19:10:46.336Z", "projects": {"frontend": {"projectName": "kaleido-app", "projectType": "frontend", "framework": "Next.js + <PERSON><PERSON> + Playwright", "collectedAt": "2025-08-03T19:10:46.336Z", "summary": {"totalTests": 0, "totalSuites": 0, "totalFiles": 0, "passed": 0, "failed": 0, "skipped": 0, "coverage": null, "e2eTests": 0}, "testFiles": [], "e2eTests": []}, "backend": {"projectName": "headstart_backend", "projectType": "backend", "framework": "NestJS + Jest", "collectedAt": "2025-08-02T10:40:59.062Z", "testRun": {"success": false, "numTotalTests": 5, "numPassedTests": 5, "numFailedTests": 0, "numPendingTests": 0, "numTotalTestSuites": 1, "numPassedTestSuites": 1, "numFailedTestSuites": 0, "startTime": 1754131231117}, "summary": {"totalTests": 5, "totalSuites": 1, "totalFiles": 1, "passed": 5, "failed": 0, "skipped": 0, "coverage": null, "success": false, "testDistribution": {"unit": 0, "integration": 0, "e2e": 0, "service": 5, "controller": 0, "api": 0, "utility": 0, "other": 0}}, "testFiles": [{"name": "test-dashboard.service.spec.ts", "relativePath": "src/modules/test-dashboard/test-dashboard.service.spec.ts", "size": 4644, "lastModified": "2025-08-02T10:39:34.713Z", "type": "service", "testCount": 5, "suiteCount": 2, "status": "passed", "duration": 27755, "tests": [{"title": "should be defined", "status": "passed", "duration": 15}, {"title": "should handle undefined testResults gracefully", "status": "passed", "duration": 32}, {"title": "should handle null testResults gracefully", "status": "passed", "duration": 1}, {"title": "should handle testResults without results property", "status": "passed", "duration": 1}, {"title": "should handle testResults with proper structure", "status": "passed", "duration": 2}], "suites": [{"name": "TestDashboardService", "tests": [{"title": "should be defined", "status": "passed", "duration": 15}], "status": "passed", "duration": 15}, {"name": "updateTestDataWithResults", "tests": [{"title": "should handle undefined testResults gracefully", "status": "passed", "duration": 32}, {"title": "should handle null testResults gracefully", "status": "passed", "duration": 1}, {"title": "should handle testResults without results property", "status": "passed", "duration": 1}, {"title": "should handle testResults with proper structure", "status": "passed", "duration": 2}], "status": "passed", "duration": 36}], "passed": 5, "failed": 0, "skipped": 0}], "coverage": null}}, "summary": {"totalProjects": 2, "totalTests": 5, "totalSuites": 1, "totalFiles": 1, "passed": 5, "failed": 0, "skipped": 0, "e2eTests": 0, "overallCoverage": null, "frontendHealth": "unknown", "backendHealth": "healthy"}, "testDistribution": {"unit": 0, "integration": 0, "e2e": 0, "component": 0, "service": 0, "controller": 0, "api": 0, "utility": 0, "other": 0}}