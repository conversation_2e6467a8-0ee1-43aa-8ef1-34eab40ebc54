{"permissions": {"allow": ["<PERSON><PERSON>(mkdir:*)", "Bash(npm run typecheck:*)", "<PERSON><PERSON>(chmod:*)", "Bash(grep:*)", "Bash(pnpm build:*)", "Bash(rm:*)", "<PERSON><PERSON>(mv:*)", "Bash(npx eslint:*)", "Bash(npx tsc:*)", "Bash(pnpm lint:fix:*)", "Bash(find:*)", "Bash(npm run lint)", "Bash(npm run:*)", "Bash(pnpm add:*)", "Bash(npm install:*)", "mcp__ide__getDiagnostics", "Bash(lsof:*)", "<PERSON><PERSON>(touch:*)", "Bash(npx ts-node:*)", "<PERSON><PERSON>(curl:*)", "<PERSON><PERSON>(pkill:*)", "Bash(npm test:*)", "Bash(pnpm test:*)", "Bash(ls:*)"], "deny": []}, "enabledMcpjsonServers": ["n8n-mcp"], "enableAllProjectMcpServers": true}