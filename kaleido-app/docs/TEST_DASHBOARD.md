# Test Dashboard

A comprehensive test monitoring and management dashboard for the Kaleido Talent platform, providing real-time insights into test execution across both frontend and backend projects.

## Features

### 🎯 **Comprehensive Test Monitoring**
- **Unified View**: Monitor tests from both `kaleido-app` (frontend) and `headstart_backend` (backend) in a single dashboard
- **Real-time Data**: Live test execution results with automatic refresh capabilities
- **Test Classification**: Categorizes tests by type (unit, integration, e2e, component, service, controller)
- **Status Tracking**: Visual indicators for passed, failed, pending, and skipped tests

### 📊 **Advanced Analytics**
- **Coverage Reporting**: Combined code coverage metrics from both projects
- **Test Distribution**: Visual breakdown of test types across projects
- **Performance Metrics**: Test execution duration and performance tracking
- **Health Monitoring**: Environment health status for both frontend and backend

### 🔍 **Powerful Filtering & Search**
- **Smart Search**: Search tests by name, file path, or content
- **Multi-dimensional Filtering**: Filter by project, test type, status, and more
- **Real-time Results**: Instant filtering with no page reloads

### ⚡ **Test Execution**
- **Remote Execution**: Run tests directly from the dashboard
- **Selective Running**: Execute specific test types or projects
- **Live Output**: Real-time terminal output during test execution
- **Execution History**: Track test run history and results

### 🎨 **Modern UI/UX**
- **Glassmorphic Design**: Beautiful transparent backgrounds with blur effects
- **Responsive Layout**: Works seamlessly on desktop, tablet, and mobile
- **Interactive Components**: Hover effects, animations, and smooth transitions
- **Dark Theme**: Consistent with the application's design system

## Architecture

### Frontend Components

#### Core Components
- **`TestCard`**: Individual test file display with status, metrics, and actions
- **`TestSummaryCard`**: Project-level summary with coverage and statistics
- **`TestFilter`**: Advanced filtering and search interface
- **`TestExecutionModal`**: Real-time test execution with live output

#### Services
- **`testRunnerService`**: Handles API communication and data processing
- **API Integration**: Seamless integration with backend test dashboard endpoints

### Backend API

#### Endpoints
- **`GET /test-dashboard/data`**: Comprehensive test data from both projects
- **`GET /test-dashboard/frontend`**: Frontend-specific test data
- **`GET /test-dashboard/backend`**: Backend-specific test data
- **`POST /test-dashboard/run-tests`**: Execute tests with specified parameters
- **`GET /test-dashboard/coverage`**: Coverage information from both projects
- **`GET /test-dashboard/health`**: Test environment health status

#### Data Collection Scripts
- **`collect-test-data.js`**: Individual project test data collection
- **`collect-all-tests.js`**: Combined data collection from both projects

## Installation & Setup

### Prerequisites
- Node.js 18+ and pnpm
- Both `kaleido-app` and `headstart_backend` projects set up
- Test frameworks configured (Jest, Playwright)

### Backend Setup

1. **Install Dependencies**
   ```bash
   cd headstart_backend
   pnpm install
   ```

2. **Test Data Collection Scripts**
   The scripts are automatically installed in `headstart_backend/scripts/`

3. **API Module**
   The `TestDashboardModule` is automatically registered in the main app module

### Frontend Setup

1. **Install Dependencies**
   ```bash
   cd kaleido-app
   pnpm install
   ```

2. **Test Data Collection Scripts**
   The scripts are automatically installed in `kaleido-app/scripts/`

3. **UI Components**
   All dashboard components are available in `src/components/ui/`

### Access the Dashboard

1. **Start the Development Server**
   ```bash
   cd kaleido-app
   pnpm run dev
   ```

2. **Navigate to Dashboard**
   - URL: `http://localhost:3001/test-dashboard`
   - Available to Admin users only (configurable in navigation.ts)

## Usage

### Viewing Test Data

1. **Dashboard Overview**
   - View combined statistics from both projects
   - Monitor overall test health and coverage
   - See test distribution by type and project

2. **Filtering Tests**
   - Use the search bar to find specific tests
   - Apply filters by project, type, or status
   - Combine multiple filters for precise results

3. **Test Details**
   - Click on test cards to view detailed information
   - See individual test cases and suites
   - View file paths and last modification dates

### Running Tests

1. **Individual Test Execution**
   - Click the play button on any test card
   - Select project and test type in the modal
   - Monitor real-time execution output

2. **Bulk Test Execution**
   - Use the test execution modal
   - Choose from multiple execution options:
     - All projects or specific project
     - All test types or specific type
   - View comprehensive execution results

### Data Export

- **Export Test Data**: Download complete test data as JSON
- **Coverage Reports**: Access detailed coverage information
- **Execution History**: Track test run history and trends

## Configuration

### Navigation Access

Edit `src/lib/navigation.ts` to control dashboard access:

```typescript
{
  label: 'Test Dashboard',
  href: '/test-dashboard',
  icon: TestTube,
  roles: [UserRole.ADMIN], // Customize access roles
  gradient: themeGradients.dashboard,
  description: 'Monitor and manage tests across your entire application',
  group: 'admin',
}
```

### API Configuration

The backend API is automatically configured with proper authentication guards and Swagger documentation.

## Troubleshooting

### Common Issues

1. **Test Data Not Loading**
   - Ensure both projects have test files
   - Check that test collection scripts have execute permissions
   - Verify API endpoints are accessible

2. **Test Execution Fails**
   - Confirm test frameworks are properly installed
   - Check that test scripts exist in package.json
   - Verify project paths in the service configuration

3. **Coverage Data Missing**
   - Ensure coverage is enabled in test configurations
   - Check that coverage reports are generated
   - Verify coverage file paths in the collection scripts

### Debug Mode

Enable debug logging by setting environment variables:
```bash
DEBUG=test-dashboard:* pnpm run dev
```

## Contributing

### Adding New Test Types

1. Update the test type detection in collection scripts
2. Add new type icons and colors in UI components
3. Update filtering options in the TestFilter component

### Extending Analytics

1. Modify data collection scripts to gather additional metrics
2. Update the TestSummaryCard component for new visualizations
3. Add new API endpoints for specific analytics needs

## Future Enhancements

- **Test Trends**: Historical test performance tracking
- **Automated Alerts**: Notifications for test failures
- **Integration Testing**: Cross-project integration test support
- **Performance Benchmarks**: Test execution performance tracking
- **CI/CD Integration**: Integration with continuous integration pipelines

## Support

For issues or questions about the test dashboard:
1. Check the troubleshooting section above
2. Review the component documentation
3. Examine the API endpoint responses
4. Contact the development team for additional support
