# Kaleido App Banner Setup Guide

This guide will help you set up the beautiful terminal banner for the kaleido-app Next.js application.

## Installation

1. Install the required dependencies:

```bash
pnpm add -D chalk@4 boxen@5 gradient-string@2 figlet cli-table3
```

## Usage

### Option 1: Using the Banner Script (Recommended)

Add this script to your `package.json`:

```json
"scripts": {
  "dev:banner": "node scripts/dev-with-banner.js",
  "dev:banner:turbo": "node scripts/dev-with-banner.js --turbo",
  "dev:banner:webpack": "node scripts/dev-with-banner.js --webpack"
}
```

Then run:
```bash
pnpm dev:banner
```

### Option 2: Using the Custom Server

Add this script to your `package.json`:

```json
"scripts": {
  "dev:custom": "node scripts/next-server-wrapper.js"
}
```

Then run:
```bash
pnpm dev:custom
```

### Option 3: Integrate into Existing Dev Script

Modify your existing dev script in `package.json` to show the banner before starting:

```json
"scripts": {
  "dev": "node scripts/startup-logger.js && NEXT_FAST_REFRESH=true NODE_ENV=development NODE_OPTIONS='--inspect' next dev --turbo"
}
```

## Features

The banner displays:
- Beautiful gradient "KALEIDO" ASCII art logo
- Colorful table with clickable URLs for:
  - Main app URL
  - Dashboard
  - Job Search
  - Talent Hub
  - Profile
- Environment information
- Build mode (Turbopack/Webpack)
- Helpful tips for developers

## Customization

You can customize the banner by editing `scripts/startup-logger.js`:
- Change colors in the gradient arrays
- Add/remove table rows
- Modify the ASCII art font
- Update the helpful tips

## Troubleshooting

If you encounter any issues:
1. Make sure all dependencies are installed
2. Check that the scripts have execute permissions: `chmod +x scripts/*.js`
3. Ensure you're using compatible versions of the packages

## Preview

When you run the app, you'll see a beautiful startup banner similar to the one in kaleido-backend, with:
- Gradient colored "KALEIDO" text
- Professional table layout
- Clickable URLs
- Helpful development tips