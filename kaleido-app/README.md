# Kaleido Talent

A comprehensive talent management and recruitment platform consisting of multiple applications.

## Project Structure

This monorepo contains three main applications:

### 1. Headstart App (`/kaleido-app`)
The main talent management application built with Next.js, featuring:
- Candidate profile management and screening
- Job posting and matching
- Video job descriptions
- ATS (Applicant Tracking System) integration
- Company onboarding and settings
- Subscription and credit management

### 2. Headstart Backend (`/headstart_backend`)
NestJS-based API backend providing:
- RESTful API endpoints
- Authentication via Auth0
- Database management with TypeORM
- Redis caching
- Email services
- Video processing capabilities
- Apollo (LinkedIn Sales Navigator) integration

### 3. Kaleido Website (`/kaleido-website`)
The public-facing website built with Next.js.

## Tech Stack

- **Frontend**: Next.js, React, TypeScript, Tailwind CSS, Zustand
- **Backend**: NestJS, TypeScript, PostgreSQL, Redis, TypeORM
- **Authentication**: Auth0
- **Testing**: Jest, Playwright
- **Deployment**: <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, DigitalOcean

## Getting Started

### Prerequisites
- Node.js 18+ and pnpm
- PostgreSQL
- Redis
- Docker (for containerized development)

### Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd kaleido-talent
```

2. Install dependencies for each project:
```bash
# Frontend app
cd kaleido-app
pnpm install

# Backend
cd ../headstart_backend
pnpm install

# Website
cd ../kaleido-website
pnpm install
```

3. Set up environment variables (see Environment Setup section below)

4. Start the development servers:

**Frontend:**
```bash
cd kaleido-app
pnpm dev
```

**Backend:**
```bash
cd headstart_backend
pnpm start:dev
```

**Website:**
```bash
cd kaleido-website
pnpm dev
```

## Environment Setup

Each project requires its own environment configuration. Copy the example files and update with your values:

### Headstart App
Create `.env.local` in `/kaleido-app`:
```
# See kaleido-app/.env.example for required variables
```

### Headstart Backend
Create `.env` in `/headstart_backend`:
```
# See headstart_backend/.env.example for required variables
```

### Kaleido Website
Create `.env.local` in `/kaleido-website`:
```
# See kaleido-website/.env.example for required variables
```

## Development Workflow

### Running Tests

**Frontend tests:**
```bash
cd kaleido-app
pnpm test
pnpm test:e2e  # End-to-end tests
```

**Backend tests:**
```bash
cd headstart_backend
pnpm test
pnpm test:e2e  # End-to-end tests
```

### Code Quality

The project uses ESLint and Prettier for code quality. Run linting:

```bash
pnpm lint
```

## Deployment

The applications can be deployed using Docker and Kubernetes. See the deployment documentation in each project:
- Frontend: `/kaleido-app/CI_CD_SETUP.md`
- Backend: `/headstart_backend/DEPLOYMENT_GUIDE.md`

## Contributing

1. Create a feature branch from `main`
2. Make your changes
3. Write/update tests
4. Ensure all tests pass
5. Submit a pull request

## License

[License information]

## Support

For support and questions, please [contact information]
