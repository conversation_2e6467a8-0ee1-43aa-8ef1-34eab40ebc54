#!/usr/bin/env node

const { spawn } = require('child_process');
const { showStartupBanner } = require('./startup-logger');

// Parse command line arguments
const args = process.argv.slice(2);
const port = process.env.PORT || 3000;
const useTurbo = args.includes('--turbo');

// Show the startup banner
showStartupBanner(port, true);

// Set environment variables
const env = {
  ...process.env,
  NEXT_FAST_REFRESH: 'true',
  NODE_ENV: 'development',
  NODE_OPTIONS: '--inspect'
};

// Build the command arguments
const commandArgs = ['dev'];
if (useTurbo) {
  commandArgs.push('--turbo');
}

// Start the Next.js dev server
const child = spawn('next', commandArgs, {
  stdio: 'inherit',
  env,
  shell: true
});

// Handle process termination
process.on('SIGINT', () => {
  console.log('\n' + require('chalk').yellow('👋 Shutting down gracefully...'));
  child.kill('SIGINT');
  process.exit(0);
});

child.on('error', (error) => {
  console.error(require('chalk').red('Failed to start dev server:'), error);
  process.exit(1);
});

child.on('exit', (code) => {
  if (code !== 0 && code !== null) {
    console.error(require('chalk').red(`Dev server exited with code ${code}`));
  }
  process.exit(code || 0);
});