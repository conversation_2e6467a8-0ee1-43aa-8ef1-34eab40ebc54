const { createServer } = require('http');
const { parse } = require('url');
const next = require('next');
const chalk = require('chalk');
const boxen = require('boxen');
const gradient = require('gradient-string');
const figlet = require('figlet');
const Table = require('cli-table3');

const dev = process.env.NODE_ENV !== 'production';
const hostname = 'localhost';
const port = parseInt(process.env.PORT || '3000', 10);

// Create Kaleido gradient based on logo colors
const kaleidoGradient = gradient([
  '#FFE66D', // Yellow
  '#FF6B6B', // Pink/Red
  '#C06FBB', // Purple
  '#4ECDC4', // Teal
  '#45B7D1', // Blue
  '#96CEB4', // Green
  '#FFD93D', // Orange/Yellow
]);

// Custom gradient for different elements
const titleGradient = gradient(['#FF6B6B', '#C06FBB', '#4ECDC4']);
const infoGradient = gradient(['#45B7D1', '#96CEB4']);
const urlGradient = gradient(['#FFE66D', '#FFD93D']);

// Create the Next.js app
const app = next({ dev, hostname, port });
const handle = app.getRequestHandler();

function showStartupBanner() {
  console.clear();
  
  // Create Kaleido ASCII art
  const kaleidoArt = figlet.textSync('KALEIDO', {
    font: 'ANSI Shadow',
    horizontalLayout: 'default',
    verticalLayout: 'default',
    width: 80,
    whitespaceBreak: true
  });

  // Display gradient Kaleido logo
  console.log('\n' + kaleidoGradient(kaleidoArt) + '\n');
  
  // Create subtitle
  const subtitle = titleGradient('✨ Next.js App - Talent Management Platform ✨');
  console.log(chalk.bold(subtitle.padStart(55)) + '\n');

  // Create a beautiful table for app details
  const table = new Table({
    style: {
      head: [],
      border: []
    },
    chars: {
      'top': '═', 'top-mid': '╤', 'top-left': '╔', 'top-right': '╗',
      'bottom': '═', 'bottom-mid': '╧', 'bottom-left': '╚', 'bottom-right': '╝',
      'left': '║', 'left-mid': '╟', 'mid': '─', 'mid-mid': '┼',
      'right': '║', 'right-mid': '╢', 'middle': '│'
    }
  });

  const appUrl = `http://${hostname}:${port}`;
  
  // Add rows to table
  const appDetails = [
    ['App URL', urlGradient(appUrl)],
    ['Dashboard', urlGradient(`${appUrl}/dashboard`)],
    ['Job Search', urlGradient(`${appUrl}/job-search`)],
    ['Talent Hub', urlGradient(`${appUrl}/talent-hub`)],
    ['Profile', urlGradient(`${appUrl}/profile`)],
    ['Environment', infoGradient(dev ? 'development' : 'production')],
    ['Node Version', infoGradient(process.version)]
  ];

  appDetails.forEach(([label, value]) => {
    table.push([
      chalk.bold(infoGradient(label)),
      value
    ]);
  });

  // Create the final box with success message
  const successBox = boxen(
    chalk.bold(kaleidoGradient('🚀 Application Successfully Started')), 
    {
      padding: 1,
      margin: 1,
      borderStyle: 'double',
      borderColor: 'cyan',
      textAlignment: 'center'
    }
  );

  console.log(successBox);
  console.log(table.toString());
  
  // Add a decorative footer
  const footer = kaleidoGradient('═'.repeat(60));
  console.log('\n' + footer + '\n');

  // Show helpful tips
  console.log(chalk.dim('  📝 Tips:'));
  console.log(chalk.dim('  • Press Ctrl+C to stop the server'));
  console.log(chalk.dim('  • Edit pages to see hot-reload in action'));
  console.log(chalk.dim('  • Check console for compilation status\n'));
}

app.prepare().then(() => {
  createServer(async (req, res) => {
    try {
      const parsedUrl = parse(req.url, true);
      await handle(req, res, parsedUrl);
    } catch (err) {
      console.error('Error occurred handling', req.url, err);
      res.statusCode = 500;
      res.end('internal server error');
    }
  })
    .once('error', (err) => {
      console.error(err);
      process.exit(1);
    })
    .listen(port, () => {
      showStartupBanner();
    });
});