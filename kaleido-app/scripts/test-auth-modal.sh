#!/bin/bash

# Test script for AuthModal functionality
# This script runs all tests related to the AuthModal fix

echo "🧪 Running AuthModal Test Suite"
echo "================================"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    local status=$1
    local message=$2
    case $status in
        "info")
            echo -e "${BLUE}ℹ️  $message${NC}"
            ;;
        "success")
            echo -e "${GREEN}✅ $message${NC}"
            ;;
        "warning")
            echo -e "${YELLOW}⚠️  $message${NC}"
            ;;
        "error")
            echo -e "${RED}❌ $message${NC}"
            ;;
    esac
}

# Function to run a test and capture results
run_test() {
    local test_name=$1
    local test_path=$2

    print_status "info" "Running $test_name..."

    if pnpm test "$test_path" --passWithNoTests --silent; then
        print_status "success" "$test_name passed"
        return 0
    else
        print_status "error" "$test_name failed"
        return 1
    fi
}

# Check if we're in the right directory
if [ ! -f "package.json" ]; then
    print_status "error" "Please run this script from the kaleido-app directory"
    exit 1
fi

# Check if pnpm is available
if ! command -v pnpm &> /dev/null; then
    print_status "error" "pnpm is not installed or not in PATH"
    exit 1
fi

print_status "info" "Starting AuthModal test suite..."
echo ""

# Initialize test results
total_tests=0
passed_tests=0

# Test 1: AuthModal Component Tests
print_status "info" "Test 1: AuthModal Component"
if run_test "AuthModal Component" "src/components/__tests__/AuthModal.test.tsx"; then
    ((passed_tests++))
fi
((total_tests++))
echo ""

# Test 2: AuthModalContext Tests
print_status "info" "Test 2: AuthModalContext"
if run_test "AuthModalContext" "src/contexts/__tests__/AuthModalContext.test.tsx"; then
    ((passed_tests++))
fi
((total_tests++))
echo ""

# Test 3: ApiHelper AuthModal Integration Tests
print_status "info" "Test 3: ApiHelper AuthModal Integration"
if run_test "ApiHelper AuthModal Integration" "src/lib/__tests__/apiHelper.authModal.test.ts"; then
    ((passed_tests++))
fi
((total_tests++))
echo ""

# Test 4: Integration Tests
print_status "info" "Test 4: AuthModal Integration Tests"
if run_test "AuthModal Integration" "src/__tests__/integration/authModal.integration.test.tsx"; then
    ((passed_tests++))
fi
((total_tests++))
echo ""

# Test 5: Layout Integration Tests
print_status "info" "Test 5: Layout AuthModal Integration"
if run_test "Layout AuthModal Integration" "src/app/__tests__/layout.authModal.test.tsx"; then
    ((passed_tests++))
fi
((total_tests++))
echo ""

# Print summary
echo "================================"
print_status "info" "Test Summary"
echo "Total tests: $total_tests"
echo "Passed: $passed_tests"
echo "Failed: $((total_tests - passed_tests))"

if [ $passed_tests -eq $total_tests ]; then
    print_status "success" "All AuthModal tests passed! 🎉"
    echo ""
    print_status "info" "The AuthModal fix is working correctly and handles:"
    echo "  • 401 Unauthorized errors with auto-logout"
    echo "  • 403 Forbidden errors with auto-logout"
    echo "  • Missing auth session scenarios"
    echo "  • User interaction canceling auto-logout"
    echo "  • Proper fallback to error modal"
    echo "  • Correct provider hierarchy in layout"
    exit 0
else
    print_status "error" "Some tests failed. Please check the output above."
    echo ""
    print_status "warning" "Common issues to check:"
    echo "  • Ensure all dependencies are installed (pnpm install)"
    echo "  • Check that Jest configuration is correct"
    echo "  • Verify mock implementations match actual components"
    echo "  • Ensure test environment has proper DOM setup"
    exit 1
fi
