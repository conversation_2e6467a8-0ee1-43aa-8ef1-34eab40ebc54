#!/bin/bash

# Test script for logout functionality
# This script runs all logout-related tests

echo "🧪 Running Logout Functionality Tests"
echo "======================================"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    local status=$1
    local message=$2
    case $status in
        "info")
            echo -e "${BLUE}ℹ️  $message${NC}"
            ;;
        "success")
            echo -e "${GREEN}✅ $message${NC}"
            ;;
        "warning")
            echo -e "${YELLOW}⚠️  $message${NC}"
            ;;
        "error")
            echo -e "${RED}❌ $message${NC}"
            ;;
    esac
}

# Check if we're in the right directory
if [ ! -f "package.json" ]; then
    print_status "error" "Please run this script from the kaleido-app directory"
    exit 1
fi

print_status "info" "Starting logout functionality tests..."

# Test 1: API Helper Authentication Tests
print_status "info" "Running API Helper authentication tests..."
if npm test -- src/lib/__tests__/apiHelper.auth.test.ts --verbose; then
    print_status "success" "API Helper authentication tests passed"
else
    print_status "error" "API Helper authentication tests failed"
    exit 1
fi

echo ""

# Test 2: SidebarProfile Component Tests
print_status "info" "Running SidebarProfile component tests..."
if npm test -- src/components/layout/__tests__/SidebarProfile.test.tsx --verbose; then
    print_status "success" "SidebarProfile component tests passed"
else
    print_status "error" "SidebarProfile component tests failed"
    exit 1
fi

echo ""

# Test 3: MobileMenu Component Tests
print_status "info" "Running MobileMenu component tests..."
if npm test -- src/components/layout/__tests__/MobileMenu.test.tsx --verbose; then
    print_status "success" "MobileMenu component tests passed"
else
    print_status "error" "MobileMenu component tests failed"
    exit 1
fi

echo ""

# Test 4: LogoutModal Component Tests
print_status "info" "Running LogoutModal component tests..."
if npm test -- src/components/__tests__/LogoutModal.test.tsx --verbose; then
    print_status "success" "LogoutModal component tests passed"
else
    print_status "error" "LogoutModal component tests failed"
    exit 1
fi

echo ""

# Test 5: Integration Tests
print_status "info" "Running logout integration tests..."
if npm test -- src/__tests__/logout.integration.test.ts --verbose; then
    print_status "success" "Logout integration tests passed"
else
    print_status "error" "Logout integration tests failed"
    exit 1
fi

echo ""

# Test 6: Run all logout-related tests together
print_status "info" "Running all logout tests together..."
if npm test -- --testPathPattern="(logout|auth|SidebarProfile|MobileMenu|LogoutModal)" --verbose; then
    print_status "success" "All logout tests passed!"
else
    print_status "error" "Some logout tests failed"
    exit 1
fi

echo ""
print_status "success" "🎉 All logout functionality tests completed successfully!"

echo ""
echo "📋 Test Summary:"
echo "=================="
echo "✅ API Helper authentication logic"
echo "✅ Token expiration and grace period handling"
echo "✅ Error handling without auto-logout"
echo "✅ SidebarProfile logout functionality"
echo "✅ MobileMenu logout functionality"
echo "✅ LogoutModal logout functionality"
echo "✅ Integration between components and API helper"
echo "✅ Cookie and storage cleanup"
echo "✅ Admin user special handling"
echo "✅ Browser environment compatibility"

echo ""
print_status "info" "To run individual test suites:"
echo "  npm test src/lib/__tests__/apiHelper.auth.test.ts"
echo "  npm test src/components/layout/__tests__/SidebarProfile.test.tsx"
echo "  npm test src/components/layout/__tests__/MobileMenu.test.tsx"
echo "  npm test src/components/__tests__/LogoutModal.test.tsx"
echo "  npm test src/__tests__/logout.integration.test.ts"

echo ""
print_status "info" "To run tests in watch mode:"
echo "  npm test -- --watch --testPathPattern=\"logout|auth|SidebarProfile|MobileMenu|LogoutModal\""
