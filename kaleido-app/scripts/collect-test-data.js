#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

/**
 * Collect comprehensive test data from the frontend
 */
class FrontendTestCollector {
  constructor() {
    this.testData = {
      projectName: 'kaleido-app',
      projectType: 'frontend',
      framework: 'Next.js + Jest + Playwright',
      collectedAt: new Date().toISOString(),
      summary: {
        totalTests: 0,
        totalSuites: 0,
        passed: 0,
        failed: 0,
        skipped: 0,
        coverage: null,
        e2eTests: 0
      },
      testSuites: [],
      testFiles: [],
      e2eTests: []
    };
  }

  /**
   * Find all test files in the project
   */
  findTestFiles(dir = './src', testFiles = []) {
    try {
      const items = fs.readdirSync(dir);

      for (const item of items) {
        const fullPath = path.join(dir, item);
        const stat = fs.statSync(fullPath);

        if (stat.isDirectory() && !item.includes('node_modules') && !item.includes('.git')) {
          this.findTestFiles(fullPath, testFiles);
        } else if (item.endsWith('.test.ts') || item.endsWith('.test.tsx') || item.endsWith('.spec.ts')) {
          testFiles.push({
            path: fullPath,
            name: item,
            directory: dir,
            relativePath: path.relative('.', fullPath),
            size: stat.size,
            lastModified: stat.mtime.toISOString(),
            type: this.getTestType(fullPath)
          });
        }
      }

      return testFiles;
    } catch (error) {
      console.error(`Error scanning directory ${dir}:`, error.message);
      return testFiles;
    }
  }

  /**
   * Find E2E test files
   */
  findE2ETests() {
    const e2eDir = './e2e';
    const e2eTests = [];

    if (!fs.existsSync(e2eDir)) {
      return e2eTests;
    }

    try {
      this.findTestFiles(e2eDir, e2eTests);
      return e2eTests.map(test => ({
        ...test,
        type: 'e2e'
      }));
    } catch (error) {
      console.error('Error scanning E2E directory:', error.message);
      return e2eTests;
    }
  }

  /**
   * Determine test type based on file path and name
   */
  getTestType(filePath) {
    if (filePath.includes('e2e')) return 'e2e';
    if (filePath.includes('__tests__')) return 'unit';
    if (filePath.includes('integration')) return 'integration';
    if (filePath.includes('component')) return 'component';
    if (filePath.includes('hook')) return 'hook';
    if (filePath.includes('util')) return 'utility';
    if (filePath.includes('api')) return 'api';
    return 'unit';
  }

  /**
   * Parse test file content to extract test information
   */
  parseTestFile(filePath) {
    try {
      const content = fs.readFileSync(filePath, 'utf8');
      const tests = [];
      const suites = [];

      // Extract describe blocks (test suites)
      const describeRegex = /describe\s*\(\s*['"`]([^'"`]+)['"`]/g;
      let match;
      while ((match = describeRegex.exec(content)) !== null) {
        suites.push({
          name: match[1],
          type: 'describe'
        });
      }

      // Extract test blocks (it, test)
      const testRegex = /(it|test)\s*\(\s*['"`]([^'"`]+)['"`]/g;
      while ((match = testRegex.exec(content)) !== null) {
        tests.push({
          name: match[2],
          type: match[1]
        });
      }

      // Extract Playwright test blocks
      const playwrightRegex = /test\s*\(\s*['"`]([^'"`]+)['"`]/g;
      while ((match = playwrightRegex.exec(content)) !== null) {
        tests.push({
          name: match[1],
          type: 'playwright'
        });
      }

      return { tests, suites, linesOfCode: content.split('\n').length };
    } catch (error) {
      console.error(`Error parsing test file ${filePath}:`, error.message);
      return { tests: [], suites: [], linesOfCode: 0 };
    }
  }

  /**
   * Run Jest tests and collect results
   */
  async runJestTests() {
    try {

      const result = execSync('pnpm test --passWithNoTests --json --coverage', {
        encoding: 'utf8',
        timeout: 120000,
        maxBuffer: 1024 * 1024 * 10
      });

      return JSON.parse(result);
    } catch (error) {
      console.warn('Jest test execution failed, collecting static data only:', error.message);
      return null;
    }
  }

  /**
   * Run Playwright tests and collect results
   */
  async runPlaywrightTests() {
    try {

      const result = execSync('pnpm test:e2e --reporter=json', {
        encoding: 'utf8',
        timeout: 180000,
        maxBuffer: 1024 * 1024 * 10
      });

      return JSON.parse(result);
    } catch (error) {
      console.warn('Playwright test execution failed, collecting static data only:', error.message);
      return null;
    }
  }

  /**
   * Get coverage information
   */
  getCoverageInfo() {
    try {
      const coveragePath = './coverage/coverage-summary.json';
      if (fs.existsSync(coveragePath)) {
        return JSON.parse(fs.readFileSync(coveragePath, 'utf8'));
      }
    } catch (error) {
      console.warn('Could not read coverage data:', error.message);
    }
    return null;
  }

  /**
   * Collect all test data
   */
  async collect() {

    // Find all test files
    const testFiles = this.findTestFiles();
    const e2eTests = this.findE2ETests();

    // Parse each test file
    const allTestFiles = [...testFiles, ...e2eTests];
    for (const testFile of allTestFiles) {
      const parsed = this.parseTestFile(testFile.path);
      testFile.tests = parsed.tests;
      testFile.suites = parsed.suites;
      testFile.linesOfCode = parsed.linesOfCode;
      testFile.testCount = parsed.tests.length;
      testFile.suiteCount = parsed.suites.length;

      this.testData.summary.totalTests += parsed.tests.length;
      this.testData.summary.totalSuites += parsed.suites.length;

      if (testFile.type === 'e2e') {
        this.testData.summary.e2eTests += parsed.tests.length;
      }
    }

    this.testData.testFiles = testFiles;
    this.testData.e2eTests = e2eTests;

    // Try to run tests and get results
    const jestResults = await this.runJestTests();
    if (jestResults) {
      this.processJestResults(jestResults);
    }

    const playwrightResults = await this.runPlaywrightTests();
    if (playwrightResults) {
      this.processPlaywrightResults(playwrightResults);
    }

    // Get coverage information
    this.testData.summary.coverage = this.getCoverageInfo();

    return this.testData;
  }

  /**
   * Process Jest test execution results
   */
  processJestResults(results) {
    if (results.testResults) {
      for (const testResult of results.testResults) {
        const testFile = this.testData.testFiles.find(f =>
          testResult.name.includes(f.relativePath.replace(/\\/g, '/'))
        );

        if (testFile) {
          testFile.status = testResult.status;
          testFile.duration = testResult.endTime - testResult.startTime;
          testFile.assertionResults = testResult.assertionResults;
        }
      }
    }

    if (results.numTotalTests) {
      this.testData.summary.totalTests = results.numTotalTests;
      this.testData.summary.passed = results.numPassedTests || 0;
      this.testData.summary.failed = results.numFailedTests || 0;
      this.testData.summary.skipped = results.numPendingTests || 0;
    }
  }

  /**
   * Process Playwright test execution results
   */
  processPlaywrightResults(results) {
    if (results.suites) {
      for (const suite of results.suites) {
        this.processPlaywrightSuite(suite);
      }
    }
  }

  processPlaywrightSuite(suite) {
    if (suite.specs) {
      for (const spec of suite.specs) {
        const testFile = this.testData.e2eTests.find(f =>
          spec.file && f.relativePath.includes(spec.file)
        );

        if (testFile && spec.tests) {
          testFile.playwrightResults = spec.tests;
          testFile.status = spec.tests.every(t => t.outcome === 'expected') ? 'passed' : 'failed';
        }
      }
    }

    if (suite.suites) {
      for (const subSuite of suite.suites) {
        this.processPlaywrightSuite(subSuite);
      }
    }
  }
}

// Main execution
async function main() {
  try {
    const collector = new FrontendTestCollector();
    const testData = await collector.collect();

    // Ensure output directory exists
    const outputDir = './test-reports';
    if (!fs.existsSync(outputDir)) {
      fs.mkdirSync(outputDir, { recursive: true });
    }

    // Write test data to JSON file
    const outputPath = path.join(outputDir, 'frontend-tests.json');
    fs.writeFileSync(outputPath, JSON.stringify(testData, null, 2));

    return testData;
  } catch (error) {
    console.error('❌ Error collecting frontend test data:', error);
    process.exit(1);
  }
}

if (require.main === module) {
  main();
}

module.exports = { FrontendTestCollector };
