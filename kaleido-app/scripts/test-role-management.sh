#!/bin/bash

# Test script for role management system
# This script runs all tests related to the new role management implementation

echo "🧪 Running Role Management Tests"
echo "================================"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    if [ $1 -eq 0 ]; then
        echo -e "${GREEN}✅ $2${NC}"
    else
        echo -e "${RED}❌ $2${NC}"
    fi
}

# Function to run test and capture result
run_test() {
    local test_name="$1"
    local test_command="$2"

    echo -e "${YELLOW}Running: $test_name${NC}"

    if eval "$test_command"; then
        print_status 0 "$test_name passed"
        return 0
    else
        print_status 1 "$test_name failed"
        return 1
    fi
}

# Initialize counters
total_tests=0
passed_tests=0

echo ""
echo "📋 Test Plan:"
echo "1. Enhanced User Data Hook Tests"
echo "2. Fallback User Data Hook Tests"
echo "3. Role Management Integration Tests"
echo "4. Backend Dashboard Service Tests"
echo ""

# Test 1: Enhanced User Data Hook
echo "🔍 Testing Enhanced User Data Hook..."
if run_test "Enhanced User Data Hook" "npm test -- src/hooks/__tests__/useEnhancedUserData.test.ts --passWithNoTests"; then
    ((passed_tests++))
fi
((total_tests++))

echo ""

# Test 2: Fallback User Data Hook
echo "🔍 Testing Fallback User Data Hook..."
if run_test "Fallback User Data Hook" "npm test -- src/hooks/__tests__/useFallbackUserData.test.ts --passWithNoTests"; then
    ((passed_tests++))
fi
((total_tests++))

echo ""

# Test 3: Role Management Integration Tests
echo "🔍 Testing Role Management Integration..."
if run_test "Role Management Integration" "npm test -- src/__tests__/integration/roleManagement.test.tsx --passWithNoTests"; then
    ((passed_tests++))
fi
((total_tests++))

echo ""

# Test 4: Backend Dashboard Service Tests (if backend is available)
if [ -d "../headstart_backend" ]; then
    echo "🔍 Testing Backend Dashboard Service..."
    cd ../headstart_backend
    if run_test "Backend Dashboard Service" "npm test -- src/modules/dashboard/__tests__/dashboard.service.spec.ts --passWithNoTests"; then
        ((passed_tests++))
    fi
    cd ../kaleido-app
    ((total_tests++))
else
    echo -e "${YELLOW}⚠️  Backend tests skipped (backend directory not found)${NC}"
fi

echo ""
echo "📊 Test Results Summary"
echo "======================="
echo "Total Tests: $total_tests"
echo "Passed: $passed_tests"
echo "Failed: $((total_tests - passed_tests))"

if [ $passed_tests -eq $total_tests ]; then
    echo -e "${GREEN}🎉 All tests passed!${NC}"
    exit 0
else
    echo -e "${RED}💥 Some tests failed!${NC}"
    exit 1
fi
