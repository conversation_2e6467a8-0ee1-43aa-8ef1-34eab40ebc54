const fs = require('fs');
const path = require('path');

/**
 * Custom Jest reporter that generates test data for the dashboard
 */
class TestDashboardReporter {
  constructor(globalConfig, options) {
    this.globalConfig = globalConfig;
    this.options = options || {};
    this.projectPath = process.cwd();
    this.reportsDir = path.join(this.projectPath, 'test-reports');
    this.outputPath = path.join(this.reportsDir, 'frontend-tests.json');

    // Ensure test-reports directory exists
    if (!fs.existsSync(this.reportsDir)) {
      fs.mkdirSync(this.reportsDir, { recursive: true });
    }
  }

  onRunComplete(contexts, results) {
    try {
      const testData = this.generateTestData(results);

      // Write the test data to JSON file (compressed, no spaces)
      fs.writeFileSync(this.outputPath, JSON.stringify(testData));

      // Also notify the dashboard service if it's running
      this.notifyDashboardService(testData);
    } catch (error) {
      console.error('Error generating test dashboard data:', error);
    }
  }

  generateTestData(results) {
    const testFiles = this.processTestResults(results);
    const e2eTests = this.processE2ETests(testFiles);
    const unitTests = testFiles.filter(file => file.type !== 'e2e');
    const summary = this.generateSummary(results, testFiles);

    return {
      projectName: 'kaleido-app',
      projectType: 'frontend',
      framework: 'Next.js + Jest + Playwright',
      collectedAt: new Date().toISOString(),
      testRun: {
        success: results.success,
        numTotalTests: results.numTotalTests,
        numPassedTests: results.numPassedTests,
        numFailedTests: results.numFailedTests,
        numPendingTests: results.numPendingTests,
        numTotalTestSuites: results.numTotalTestSuites,
        numPassedTestSuites: results.numPassedTestSuites,
        numFailedTestSuites: results.numFailedTestSuites,
        startTime: results.startTime,
        runTime: results.runTime,
      },
      summary,
      testFiles: unitTests,
      e2eTests,
      coverage: this.getCoverageData(),
    };
  }

  processTestResults(results) {
    const testFiles = [];

    results.testResults.forEach(testResult => {
      const relativePath = path.relative(this.projectPath, testResult.testFilePath);
      const testType = this.getTestTypeFromPath(relativePath);

      // Only store essential test information to reduce file size
      const tests = testResult.testResults.map(test => ({
        title: test.title,
        status: test.status,
        duration: test.duration,
        // Skip fullName, location, ancestorTitles, failureMessages for size
      }));

      const suites = this.extractSuites(testResult.testResults);

      testFiles.push({
        name: path.basename(testResult.testFilePath),
        relativePath,
        // Skip full path to reduce size
        size: this.getFileSize(testResult.testFilePath),
        lastModified: this.getFileModifiedTime(testResult.testFilePath),
        type: testType,
        testCount: tests.length,
        suiteCount: suites.length,
        status: testResult.numFailingTests > 0 ? 'failed' : 'passed',
        duration: testResult.perfStats.end - testResult.perfStats.start,
        // Only include test details for failed tests or if count is small
        tests: testResult.numFailingTests > 0 || tests.length <= 10 ? tests : [],
        suites: suites.length <= 5 ? suites : [],
        passed: testResult.numPassingTests,
        failed: testResult.numFailingTests,
        skipped: testResult.numPendingTests,
        // Skip individual file coverage to reduce size
      });
    });

    return testFiles;
  }

  processE2ETests(testFiles) {
    const e2eTests = testFiles.filter(file => file.type === 'e2e');

    // Also check for Playwright test results if available
    const playwrightResultsPath = path.join(this.projectPath, 'playwright-report', 'results.json');
    if (fs.existsSync(playwrightResultsPath)) {
      try {
        const playwrightResults = JSON.parse(fs.readFileSync(playwrightResultsPath, 'utf8'));
        // Process Playwright results and add to e2eTests
        // This is a simplified implementation - you might want to enhance this
        playwrightResults.suites?.forEach(suite => {
          suite.specs?.forEach(spec => {
            const e2eTest = {
              name: spec.title,
              path: spec.file,
              relativePath: path.relative(this.projectPath, spec.file),
              type: 'e2e',
              browser: spec.tests?.[0]?.projectName || 'chromium',
              status: spec.tests?.[0]?.outcome || 'unknown',
              duration: spec.tests?.[0]?.duration || 0,
              tests: spec.tests?.map(test => ({
                title: test.title,
                status: test.outcome,
                duration: test.duration,
                annotations: test.annotations,
              })) || [],
            };
            e2eTests.push(e2eTest);
          });
        });
      } catch (error) {
        console.warn('Could not process Playwright results:', error.message);
      }
    }

    return e2eTests;
  }

  extractSuites(testResults) {
    const suites = new Map();

    testResults.forEach(test => {
      test.ancestorTitles.forEach(ancestorTitle => {
        if (!suites.has(ancestorTitle)) {
          suites.set(ancestorTitle, {
            name: ancestorTitle,
            tests: [],
            status: 'passed',
            duration: 0,
          });
        }
      });

      const suiteName = test.ancestorTitles[test.ancestorTitles.length - 1] || 'default';
      if (suites.has(suiteName)) {
        const suite = suites.get(suiteName);
        suite.tests.push({
          title: test.title,
          status: test.status,
          duration: test.duration,
        });
        suite.duration += test.duration || 0;

        if (test.status === 'failed') {
          suite.status = 'failed';
        } else if (test.status === 'pending' && suite.status !== 'failed') {
          suite.status = 'skipped';
        }
      }
    });

    return Array.from(suites.values());
  }

  generateSummary(results, testFiles) {
    const e2eCount = testFiles.filter(file => file.type === 'e2e').reduce((sum, file) => sum + file.testCount, 0);
    const testDistribution = this.calculateTestDistribution(testFiles);

    return {
      totalTests: results.numTotalTests,
      totalSuites: results.numTotalTestSuites,
      totalFiles: testFiles.length,
      passed: results.numPassedTests,
      failed: results.numFailedTests,
      skipped: results.numPendingTests,
      e2eTests: e2eCount,
      coverage: this.getCoverageData(),
      success: results.success,
      duration: results.runTime,
      testDistribution,
    };
  }

  calculateTestDistribution(testFiles) {
    const distribution = {
      unit: 0,
      integration: 0,
      e2e: 0,
      component: 0,
      service: 0,
      api: 0,
      utility: 0,
      other: 0,
    };

    testFiles.forEach(file => {
      const type = file.type;
      if (distribution.hasOwnProperty(type)) {
        distribution[type] += file.testCount;
      } else {
        distribution.other += file.testCount;
      }
    });

    return distribution;
  }

  getTestTypeFromPath(relativePath) {
    const pathLower = relativePath.toLowerCase();

    if (pathLower.includes('e2e') || pathLower.includes('playwright')) {
      return 'e2e';
    }
    if (pathLower.includes('integration')) {
      return 'integration';
    }
    if (pathLower.includes('component')) {
      return 'component';
    }
    if (pathLower.includes('service')) {
      return 'service';
    }
    if (pathLower.includes('api')) {
      return 'api';
    }
    if (pathLower.includes('util')) {
      return 'utility';
    }

    return 'unit';
  }

  getCoverageData() {
    try {
      const coveragePath = path.join(this.projectPath, 'coverage', 'coverage-summary.json');
      if (fs.existsSync(coveragePath)) {
        const coverage = JSON.parse(fs.readFileSync(coveragePath, 'utf8'));
        return coverage.total || null;
      }
    } catch (error) {
      console.warn('Could not read coverage data:', error.message);
    }
    return null;
  }

  getFileCoverage(testFilePath) {
    try {
      const coveragePath = path.join(this.projectPath, 'coverage', 'coverage-summary.json');
      if (fs.existsSync(coveragePath)) {
        const coverage = JSON.parse(fs.readFileSync(coveragePath, 'utf8'));
        const sourceFilePath = testFilePath.replace(/\.spec\.ts$/, '.ts').replace(/\.test\.ts$/, '.ts').replace(/\.spec\.tsx$/, '.tsx').replace(/\.test\.tsx$/, '.tsx');
        const relativePath = path.relative(this.projectPath, sourceFilePath);
        return coverage[relativePath] || null;
      }
    } catch (error) {
      // Ignore errors for file-specific coverage
    }
    return null;
  }

  getFileSize(filePath) {
    try {
      const stats = fs.statSync(filePath);
      return stats.size;
    } catch (error) {
      return 0;
    }
  }

  getFileModifiedTime(filePath) {
    try {
      const stats = fs.statSync(filePath);
      return stats.mtime.toISOString();
    } catch (error) {
      return new Date().toISOString();
    }
  }

  estimateLines(filePath) {
    try {
      const content = fs.readFileSync(filePath, 'utf8');
      return content.split('\n').length;
    } catch (error) {
      return 0;
    }
  }

  async notifyDashboardService(testData) {
    // Try to notify dashboard service if it's running
    try {
      const axios = require('axios');
      await axios.post('http://localhost:8080/api/test-dashboard/update', testData, {
        timeout: 1000,
      });
    } catch (error) {
      // Silently ignore if dashboard service is not running
    }
  }
}

module.exports = TestDashboardReporter;
