#!/usr/bin/env node

/**
 * <PERSON><PERSON>t to collect and combine test data from both frontend and backend projects
 * This script is called by the test dashboard service for detailed test collection
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

class TestCollector {
  constructor() {
    this.projectRoot = path.resolve(__dirname, '..');
    this.backendPath = path.resolve(this.projectRoot, '..', 'headstart_backend');
    this.reportsDir = path.join(this.projectRoot, 'test-reports');

    // Ensure reports directory exists
    if (!fs.existsSync(this.reportsDir)) {
      fs.mkdirSync(this.reportsDir, { recursive: true });
    }
  }

  async collectAllTests() {

    const results = {
      collectedAt: new Date().toISOString(),
      projects: {
        frontend: null,
        backend: null,
      },
      summary: {
        totalProjects: 2,
        totalTests: 0,
        totalSuites: 0,
        totalFiles: 0,
        passed: 0,
        failed: 0,
        skipped: 0,
        e2eTests: 0,
        overallCoverage: null,
        frontendHealth: 'unknown',
        backendHealth: 'unknown',
      },
      testDistribution: {
        unit: 0,
        integration: 0,
        e2e: 0,
        component: 0,
        service: 0,
        controller: 0,
        api: 0,
        utility: 0,
        other: 0,
      },
    };

    try {
      // Try to collect frontend data
      results.projects.frontend = await this.collectFrontendData();

      // Try to collect backend data
      results.projects.backend = await this.collectBackendData();

      // Calculate combined summary
      this.calculateCombinedSummary(results);

      // Save combined results (compressed, no spaces)
      const outputPath = path.join(this.reportsDir, 'all-tests.json');
      fs.writeFileSync(outputPath, JSON.stringify(results));

      return results;

    } catch (error) {
      console.error('❌ Error collecting test data:', error);
      throw error;
    }
  }

  async collectFrontendData() {
    const frontendDataPath = path.join(this.reportsDir, 'frontend-tests.json');

    if (fs.existsSync(frontendDataPath)) {
      return JSON.parse(fs.readFileSync(frontendDataPath, 'utf8'));
    }

    // If no existing data, create a basic structure
    return {
      projectName: 'kaleido-app',
      projectType: 'frontend',
      framework: 'Next.js + Jest + Playwright',
      collectedAt: new Date().toISOString(),
      summary: {
        totalTests: 0,
        totalSuites: 0,
        totalFiles: 0,
        passed: 0,
        failed: 0,
        skipped: 0,
        e2eTests: 0,
        coverage: null,
      },
      testFiles: [],
      e2eTests: [],
    };
  }

  async collectBackendData() {
    const backendDataPath = path.join(this.backendPath, 'test-reports', 'backend-tests.json');

    if (fs.existsSync(backendDataPath)) {
      return JSON.parse(fs.readFileSync(backendDataPath, 'utf8'));
    }

    // If no existing data, create a basic structure
    return {
      projectName: 'headstart_backend',
      projectType: 'backend',
      framework: 'NestJS + Jest',
      collectedAt: new Date().toISOString(),
      summary: {
        totalTests: 0,
        totalSuites: 0,
        totalFiles: 0,
        passed: 0,
        failed: 0,
        skipped: 0,
        coverage: null,
      },
      testFiles: [],
    };
  }

  calculateCombinedSummary(results) {
    const { frontend, backend } = results.projects;

    // Calculate totals
    if (frontend) {
      results.summary.totalTests += frontend.summary?.totalTests || 0;
      results.summary.totalSuites += frontend.summary?.totalSuites || 0;
      results.summary.totalFiles += frontend.summary?.totalFiles || 0;
      results.summary.passed += frontend.summary?.passed || 0;
      results.summary.failed += frontend.summary?.failed || 0;
      results.summary.skipped += frontend.summary?.skipped || 0;
      results.summary.e2eTests += frontend.summary?.e2eTests || 0;

      results.summary.frontendHealth = frontend.summary?.totalTests > 0 ? 'healthy' : 'no-tests';

      // Add to test distribution
      if (frontend.summary?.testDistribution) {
        Object.keys(frontend.summary.testDistribution).forEach(key => {
          if (results.testDistribution[key] !== undefined) {
            results.testDistribution[key] += frontend.summary.testDistribution[key];
          }
        });
      }
    }

    if (backend) {
      results.summary.totalTests += backend.summary?.totalTests || 0;
      results.summary.totalSuites += backend.summary?.totalSuites || 0;
      results.summary.totalFiles += backend.summary?.totalFiles || 0;
      results.summary.passed += backend.summary?.passed || 0;
      results.summary.failed += backend.summary?.failed || 0;
      results.summary.skipped += backend.summary?.skipped || 0;

      results.summary.backendHealth = backend.summary?.totalTests > 0 ? 'healthy' : 'no-tests';

      // Add to test distribution
      if (backend.summary?.testDistribution) {
        Object.keys(backend.summary.testDistribution).forEach(key => {
          if (results.testDistribution[key] !== undefined) {
            results.testDistribution[key] += backend.summary.testDistribution[key];
          }
        });
      }
    }

    // Calculate combined coverage
    results.summary.overallCoverage = this.calculateCombinedCoverage(
      frontend?.coverage,
      backend?.coverage
    );
  }

  calculateCombinedCoverage(frontendCoverage, backendCoverage) {
    if (!frontendCoverage && !backendCoverage) return null;
    if (!frontendCoverage) return backendCoverage;
    if (!backendCoverage) return frontendCoverage;

    const combined = {};

    ['lines', 'statements', 'functions', 'branches'].forEach(metric => {
      const frontend = frontendCoverage[metric] || { total: 0, covered: 0 };
      const backend = backendCoverage[metric] || { total: 0, covered: 0 };

      combined[metric] = {
        total: frontend.total + backend.total,
        covered: frontend.covered + backend.covered,
        skipped: (frontend.skipped || 0) + (backend.skipped || 0),
        pct: 0,
      };

      if (combined[metric].total > 0) {
        combined[metric].pct = Math.round(
          (combined[metric].covered / combined[metric].total) * 100 * 100
        ) / 100;
      }
    });

    return combined;
  }
}

// Run the collector if this script is executed directly
if (require.main === module) {
  const collector = new TestCollector();
  collector.collectAllTests()
    .then(() => {
      process.exit(0);
    })
    .catch(error => {
      console.error('❌ Test collection failed:', error);
      process.exit(1);
    });
}

module.exports = TestCollector;
