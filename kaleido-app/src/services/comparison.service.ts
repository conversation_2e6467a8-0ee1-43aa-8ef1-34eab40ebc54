import apiHelper from '@/lib/apiHelper';
import {
  ComparisonR<PERSON>ult,
  CreateComparisonDto,
  ScenarioComparisonDto,
} from '@/types/comparison.types';

export const comparisonService = {
  /**
   * Create a new candidate comparison
   */
  async createComparison(
    data: CreateComparisonDto
  ): Promise<{ id: string; jobId: string; status: string; message: string }> {
    try {
      const response = await apiHelper.post('/comparisons', data);
      return response;
    } catch (error) {
      console.error('Error creating comparison:', error);
      throw new Error('Failed to create comparison');
    }
  },

  /**
   * Create a scenario-based comparison
   */
  async createScenarioComparison(
    data: ScenarioComparisonDto
  ): Promise<{ id: string; jobId: string; status: string; message: string }> {
    try {
      const response = await apiHelper.post('/comparisons/scenario', data);
      return response;
    } catch (error) {
      console.error('Error creating scenario comparison:', error);
      throw new Error('Failed to create scenario comparison');
    }
  },

  /**
   * Get comparison results
   */
  async getComparison(id: string): Promise<ComparisonResult> {
    try {
      const response = await apiHelper.get(`/comparisons/${id}`);
      return response;
    } catch (error) {
      console.error('Error fetching comparison:', error);
      throw new Error('Failed to fetch comparison');
    }
  },

  /**
   * Get comparison entity status
   */
  async getComparisonStatus(id: string): Promise<{ status: string; progress?: number }> {
    try {
      const response = await apiHelper.get(`/comparisons/${id}/status`);
      return response;
    } catch (error) {
      console.error('Error fetching comparison status:', error);
      throw new Error('Failed to fetch comparison status');
    }
  },

  /**
   * Get comparison queue job status (for polling)
   */
  async getComparisonJobStatus(jobId: string): Promise<any> {
    try {
      const response = await apiHelper.get(`/comparisons/status/${jobId}`);
      return response;
    } catch (error) {
      console.error('Error fetching comparison job status:', error);
      throw new Error('Failed to fetch comparison job status');
    }
  },

  /**
   * Get all comparisons for a job
   */
  async getJobComparisons(jobId: string): Promise<ComparisonResult[]> {
    try {
      const response = await apiHelper.get(`/comparisons/jobs/${jobId}`);
      return response;
    } catch (error) {
      console.error('Error fetching job comparisons:', error);
      throw new Error('Failed to fetch job comparisons');
    }
  },

  /**
   * Generate comparison report
   */
  async generateReport(id: string): Promise<any> {
    try {
      const response = await apiHelper.get(`/comparisons/${id}/report`);
      return response;
    } catch (error) {
      console.error('Error generating report:', error);
      throw new Error('Failed to generate report');
    }
  },

  /**
   * Delete a comparison
   */
  async deleteComparison(id: string): Promise<void> {
    try {
      await apiHelper.delete(`/comparisons/${id}`);
    } catch (error) {
      console.error('Error deleting comparison:', error);
      throw new Error('Failed to delete comparison');
    }
  },

  /**
   * Get comparison configuration options
   */
  async getComparisonOptions(): Promise<any> {
    try {
      const response = await apiHelper.get('/comparisons/config/options');
      return response;
    } catch (error) {
      console.error('Error fetching comparison options:', error);
      throw new Error('Failed to fetch comparison options');
    }
  },

  /**
   * Poll for comparison completion
   */
  async pollForCompletion(
    id: string,
    maxAttempts = 60,
    interval = 2000
  ): Promise<ComparisonResult> {
    let attempts = 0;

    while (attempts < maxAttempts) {
      const status = await this.getComparisonStatus(id);

      if (status.status === 'completed') {
        return await this.getComparison(id);
      } else if (status.status === 'failed') {
        throw new Error('Comparison failed to process');
      }

      attempts++;
      await new Promise(resolve => setTimeout(resolve, interval));
    }

    throw new Error('Comparison timed out');
  },
};
