import {
  getSession as getServerSession,
  getAccessToken as getServerAccessToken,
} from '@auth0/nextjs-auth0';
import type { Session } from '@auth0/nextjs-auth0';
import { UserRole } from '@/types/roles';
import { AuthCacheManager } from '@/lib/auth-cache-manager';

/**
 * Industry-standard centralized authentication service
 * Handles token management, refresh, and role validation
 */
export class AuthService {
  private static instance: AuthService;
  private refreshPromise: Promise<string> | null = null;
  private cacheManager: AuthCacheManager;

  private constructor() {
    this.cacheManager = AuthCacheManager.getInstance();
  }

  static getInstance(): AuthService {
    if (!AuthService.instance) {
      AuthService.instance = new AuthService();
    }
    return AuthService.instance;
  }

  /**
   * Get a valid access token, refreshing if necessary
   * This prevents multiple simultaneous refresh attempts
   */
  async getValidAccessToken(): Promise<string | null> {
    try {
      // For client-side, get token from Auth0 React SDK
      if (typeof window !== 'undefined') {
        return await this.getClientAccessToken();
      }

      // For server-side, this would need request context
      console.warn('getValidAccessToken called server-side without request context');
      return null;
    } catch (error) {
      console.error('Failed to get valid access token:', error);
      this.refreshPromise = null;
      return null;
    }
  }

  /**
   * Client-side token retrieval
   */
  private async getClientAccessToken(): Promise<string | null> {
    try {
      // Check if we're already refreshing
      if (this.refreshPromise) {
        return await this.refreshPromise;
      }

      // Try to get token from Auth0
      const response = await fetch('/api/auth/me', {
        credentials: 'include',
      });

      const data = await response.json();

      // Check if session expired and requires logout
      if (!response.ok && data.requiresLogout) {
        console.error('Session expired, clearing all data and redirecting to logout');
        // Clear all auth data
        this.clearAllAuthData();
        // Redirect to logout
        window.location.href = '/api/auth/logout';
        return null;
      }

      if (response.ok && data.accessToken) {
        return data.accessToken;
      }

      // Token is invalid/expired but session might be valid, try refresh
      if (response.ok && !data.accessToken && !data.tokenError) {
        this.refreshPromise = this.refreshAccessToken();
        const newToken = await this.refreshPromise;
        this.refreshPromise = null;
        return newToken;
      }

      // If we have a token error but session is valid, still try to refresh
      if (data.tokenError && data.user) {
        this.refreshPromise = this.refreshAccessToken();
        const newToken = await this.refreshPromise;
        this.refreshPromise = null;
        return newToken;
      }

      return null;
    } catch (error) {
      console.error('Failed to get client access token:', error);
      this.refreshPromise = null;
      return null;
    }
  }

  /**
   * Refresh the access token using Auth0's refresh token
   */
  private async refreshAccessToken(): Promise<string> {
    try {
      const response = await fetch('/api/auth/refresh', {
        method: 'POST',
        credentials: 'include', // Include HTTP-only cookies
      });

      if (!response.ok) {
        throw new Error('Failed to refresh token');
      }

      const { accessToken } = await response.json();
      return accessToken;
    } catch (error) {
      console.error('Token refresh failed:', error);
      throw error;
    }
  }

  /**
   * Get the current user session with roles from JWT claims
   */
  async getSession(): Promise<SessionWithRole | null> {
    try {
      // For client-side, check cache first
      if (typeof window !== 'undefined') {
        // Check centralized cache
        const cachedSession = this.cacheManager.getCachedSession();
        if (cachedSession && !this.cacheManager.shouldRefresh(cachedSession)) {
          return {
            user: cachedSession.user,
            accessToken: cachedSession.accessToken || '',
            accessTokenScope: '',
            accessTokenExpiresAt: cachedSession.expiresAt,
            idToken: '',
            refreshToken: '',
            role: cachedSession.role,
          };
        }

        const response = await fetch('/api/auth/me', {
          credentials: 'include',
        });

        const data = await response.json();

        // Check if session expired and requires logout
        if (!response.ok && data.requiresLogout) {
          console.error(
            'Session expired in getSession, clearing all data and redirecting to logout'
          );
          this.clearAllAuthData();
          // Redirect to logout
          window.location.href = '/api/auth/logout';
          return null;
        }

        if (!response.ok || !data.user) {
          this.cacheManager.clearCache();
          return null;
        }

        // Create session object
        const session: Session = {
          user: data.user,
          accessToken: data.accessToken,
          accessTokenScope: data.scope,
          accessTokenExpiresAt: data.expiresAt,
          idToken: data.idToken,
        };

        // Extract role from JWT claims
        const role = this.extractRoleFromClaims(session);

        // Cache the session
        this.cacheManager.cacheSession(data.user, role, data.accessToken);

        const sessionWithRole = {
          ...session,
          role,
        };

        return sessionWithRole;
      }

      // Server-side would need request context
      console.warn('getSession called server-side without request context');
      return null;
    } catch (error) {
      console.error('Failed to get session:', error);
      this.cacheManager.clearCache();
      return null;
    }
  }

  /**
   * Clear the session cache (useful after role updates)
   */
  clearSessionCache(): void {
    this.cacheManager.clearCache();
  }

  /**
   * Clear all authentication data from browser storage
   */
  private clearAllAuthData(): void {
    // Clear cache manager
    this.cacheManager.clearCache();

    if (typeof window === 'undefined') return;

    // Clear localStorage auth keys
    const keysToRemove: string[] = [];
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i);
      if (
        key &&
        (key.includes('auth') ||
          key.includes('user') ||
          key.includes('role') ||
          key.includes('token') ||
          key.includes('session') ||
          key.includes('@@auth0'))
      ) {
        keysToRemove.push(key);
      }
    }
    keysToRemove.forEach(key => localStorage.removeItem(key));

    // Clear sessionStorage
    sessionStorage.clear();

    // Clear all cookies
    document.cookie.split(';').forEach(cookie => {
      const eqPos = cookie.indexOf('=');
      const name = eqPos > -1 ? cookie.substr(0, eqPos).trim() : cookie.trim();
      document.cookie = `${name}=;expires=Thu, 01 Jan 1970 00:00:00 GMT;path=/`;
      document.cookie = `${name}=;expires=Thu, 01 Jan 1970 00:00:00 GMT;path=/;domain=${window.location.hostname}`;
      document.cookie = `${name}=;expires=Thu, 01 Jan 1970 00:00:00 GMT;path=/;domain=.${window.location.hostname}`;
    });
  }

  /**
   * Extract user role from JWT claims
   * Following Auth0 RBAC best practices
   */
  private extractRoleFromClaims(session: Session): UserRole | null {
    const { user } = session;

    // Standard Auth0 namespace for custom claims
    const namespace = process.env.NEXT_PUBLIC_AUTH0_NAMESPACE || 'https://kaleidotalent.com';

    // Check multiple possible claim locations (Auth0 flexibility)
    const roleClaim =
      user[`${namespace}/role`] ||
      user['role'] ||
      user['pendingRole'] || // Check pendingRole field
      user['https://kaleidotalent.com/role'] ||
      null;

    // Validate role is valid
    if (roleClaim && Object.values(UserRole).includes(roleClaim as UserRole)) {
      return roleClaim as UserRole;
    }

    return null;
  }

  /**
   * Check if user has required role(s)
   */
  async hasRole(requiredRoles: UserRole | UserRole[]): Promise<boolean> {
    const session = await this.getSession();

    if (!session?.role) {
      return false;
    }

    const roles = Array.isArray(requiredRoles) ? requiredRoles : [requiredRoles];
    return roles.includes(session.role);
  }

  /**
   * Check if user has required permission(s)
   * More granular than roles - industry best practice
   */
  async hasPermission(permission: string): Promise<boolean> {
    const session = await this.getSession();

    if (!session?.user) {
      return false;
    }

    // Get permissions from JWT claims
    const namespace = process.env.NEXT_PUBLIC_AUTH0_NAMESPACE || 'https://kaleidotalent.com';
    const permissions = session.user[`${namespace}/permissions`] || [];

    return Array.isArray(permissions) && permissions.includes(permission);
  }

  /**
   * Logout user and clear all auth data
   */
  async logout(): Promise<void> {
    try {
      // Clear all auth data
      this.clearAllAuthData();

      // Redirect to Auth0 logout
      window.location.href = '/api/auth/logout';
    } catch (error) {
      console.error('Logout failed:', error);
      // Force logout anyway
      window.location.href = '/api/auth/logout';
    }
  }

  /**
   * Force token refresh (useful after role changes)
   */
  async forceRefresh(): Promise<void> {
    this.refreshPromise = null;
    await this.getValidAccessToken();
  }
}

// Export singleton instance
export const authService = AuthService.getInstance();

// Type definitions
export interface SessionWithRole extends Session {
  role: UserRole | null;
}

// Permissions enum for fine-grained access control
export enum Permission {
  // Job permissions
  VIEW_JOBS = 'view:jobs',
  CREATE_JOBS = 'create:jobs',
  EDIT_JOBS = 'edit:jobs',
  DELETE_JOBS = 'delete:jobs',

  // Candidate permissions
  VIEW_CANDIDATES = 'view:candidates',
  MANAGE_CANDIDATES = 'manage:candidates',

  // Company permissions
  VIEW_COMPANIES = 'view:companies',
  MANAGE_COMPANIES = 'manage:companies',

  // Admin permissions
  MANAGE_USERS = 'manage:users',
  VIEW_ANALYTICS = 'view:analytics',
  MANAGE_SYSTEM = 'manage:system',
}

// Role to permissions mapping
export const rolePermissions: Record<UserRole, Permission[]> = {
  [UserRole.SUPER_ADMIN]: Object.values(Permission), // All permissions

  [UserRole.ADMIN]: [
    Permission.VIEW_JOBS,
    Permission.CREATE_JOBS,
    Permission.EDIT_JOBS,
    Permission.DELETE_JOBS,
    Permission.VIEW_CANDIDATES,
    Permission.MANAGE_CANDIDATES,
    Permission.VIEW_COMPANIES,
    Permission.MANAGE_COMPANIES,
    Permission.VIEW_ANALYTICS,
  ],

  [UserRole.EMPLOYER]: [
    Permission.VIEW_JOBS,
    Permission.CREATE_JOBS,
    Permission.EDIT_JOBS,
    Permission.VIEW_CANDIDATES,
    Permission.MANAGE_CANDIDATES,
  ],

  [UserRole.JOB_SEEKER]: [Permission.VIEW_JOBS, Permission.VIEW_COMPANIES],

  [UserRole.GRADUATE]: [Permission.VIEW_JOBS, Permission.VIEW_COMPANIES],

  [UserRole.REFERRAL_PARTNER]: [Permission.VIEW_JOBS, Permission.VIEW_CANDIDATES],
};
