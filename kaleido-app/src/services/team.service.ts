import apiHelper from '@/lib/apiHelper';
import {
  CompanyMember,
  CompanyInvitation,
  InviteTeamMemberDto,
  UpdateTeamMemberDto,
  CompanyTeamSettings,
} from '@/types/team.types';

export const teamService = {
  // Get all company team members
  async getCompanyMembers(companyId: string): Promise<CompanyMember[]> {
    return apiHelper.get<CompanyMember[]>(`/company/${companyId}/members`);
  },

  // Invite a new team member
  async inviteTeamMember(companyId: string, data: InviteTeamMemberDto): Promise<CompanyInvitation> {
    return apiHelper.post<CompanyInvitation>(`/company/${companyId}/members/invite`, data);
  },

  // Accept an invitation
  async acceptInvitation(token: string): Promise<CompanyMember> {
    return apiHelper.post<CompanyMember>('/company/accept-invitation', { token });
  },

  // Update team member
  async updateTeamMember(
    companyId: string,
    clientId: string,
    data: UpdateTeamMemberDto
  ): Promise<CompanyMember> {
    return apiHelper.put<CompanyMember>(`/company/${companyId}/members/${clientId}`, data);
  },

  // Remove team member
  async removeTeamMember(companyId: string, clientId: string): Promise<void> {
    return apiHelper.delete(`/company/${companyId}/members/${clientId}`);
  },

  // Get user's companies
  async getUserCompanies(): Promise<CompanyMember[]> {
    return apiHelper.get<CompanyMember[]>('/company/my-companies');
  },

  // Get company invitations
  async getCompanyInvitations(companyId: string): Promise<CompanyInvitation[]> {
    return apiHelper.get<CompanyInvitation[]>(`/company/${companyId}/invitations`);
  },

  // Cancel invitation
  async cancelInvitation(companyId: string, invitationId: string): Promise<void> {
    return apiHelper.delete(`/company/${companyId}/invitations/${invitationId}`);
  },

  // Update team settings
  async updateTeamSettings(
    companyId: string,
    settings: Partial<CompanyTeamSettings>
  ): Promise<void> {
    return apiHelper.put(`/company/${companyId}/team-settings`, settings);
  },

  // Resend invitation email
  async resendInvitation(companyId: string, invitationId: string): Promise<void> {
    return apiHelper.post(`/company/${companyId}/invitations/${invitationId}/resend`, {});
  },
};
