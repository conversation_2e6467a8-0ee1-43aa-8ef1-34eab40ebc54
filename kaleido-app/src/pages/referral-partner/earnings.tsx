'use client';

import { referralApi } from '@/app/referral-partner/services/referralApi';
import {
  Referral as APIReferral,
  ReferralPartner,
  ReferralStatus,
} from '@/app/referral-partner/types';
import ReferralPageHeader from '@/components/common/ReferralPageHeader';
import StripeConnectBanner from '@/components/referral-partner/StripeConnectBanner';
import AppLayout from '@/components/steps/layout/AppLayout';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { useUser } from '@auth0/nextjs-auth0/client';
import { format, parseISO, subMonths } from 'date-fns';
import { motion } from 'framer-motion';
import {
  AlertCircle,
  CheckCircle,
  ChevronDown,
  Clock,
  CreditCard,
  DollarSign,
  Download,
  Eye,
  FileText,
  Filter,
  TrendingUp,
  XCircle,
} from 'lucide-react';
import React, { useCallback, useEffect, useState } from 'react';

// Local types for this component
interface LocalReferral {
  id: string;
  candidateName: string;
  candidateEmail: string;
  jobTitle: string;
  companyName: string;
  status: 'APPLIED' | 'INTERVIEWING' | 'HIRED' | 'REJECTED';
  appliedAt: string;
  hiredAt?: string;
  commission: number;
  isPaid: boolean;
  paidAt?: string;
}

interface EarningsData {
  totalEarnings: number;
  pendingEarnings: number;
  paidEarnings: number;
  totalReferrals: number;
  hiredReferrals: number;
  conversionRate: number;
  referrals: LocalReferral[];
  monthlyEarnings: {
    month: string;
    earnings: number;
    referrals: number;
  }[];
}

type TabType = 'referral-details' | 'earnings';

export default function EarningsPage() {
  const { user, isLoading: userLoading } = useUser();
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [partner, setPartner] = useState<ReferralPartner | null>(null);
  const [earningsData, setEarningsData] = useState<EarningsData | null>(null);
  const [selectedStatus, setSelectedStatus] = useState<string>('all');
  const [selectedMonth, setSelectedMonth] = useState<string>('all');
  const [showFilters, setShowFilters] = useState(false);
  const [activeTab, setActiveTab] = useState<TabType>('referral-details');
  const [stripeConnected, setStripeConnected] = useState(false);
  const [stripeLoading, setStripeLoading] = useState(false);

  const tabs = [
    {
      id: 'referral-details' as const,
      label: 'Referral Details',
      icon: FileText,
      imageSrc: '/images/insights/performance_analytics_turquoise.png',
    },
    {
      id: 'earnings' as const,
      label: 'Earnings & Payouts',
      icon: CreditCard,
      imageSrc: '/images/landing/open-jobs/open-jobs-6.webp',
    },
  ];

  const loadData = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      if (!user?.sub) {
        setError('User not authenticated.');
        return;
      }

      // Get partner data
      const partnerResponse = await referralApi.getPartnerByClientId(user.sub);

      if (!partnerResponse) {
        setError('No referral partner profile found.');
        return;
      }

      setPartner(partnerResponse);

      // Check if Stripe is connected
      if (partnerResponse.stripeAccountId) {
        setStripeConnected(true);
      }

      // Fetch earnings data
      const earningsResponse = await referralApi.getEarnings(partnerResponse.id);

      // Fetch referrals
      const referralsResponse = await referralApi.getReferrals({
        referralPartnerId: partnerResponse.id,
      });

      // Process the data into the format expected by the UI
      const processedEarningsData: EarningsData = {
        totalEarnings: earningsResponse.totalEarnings || 0,
        pendingEarnings: earningsResponse.pendingEarnings || 0,
        paidEarnings: earningsResponse.paidEarnings || 0,
        totalReferrals: referralsResponse.length,
        hiredReferrals: referralsResponse.filter(r => r.status === ReferralStatus.CANDIDATE_HIRED)
          .length,
        conversionRate:
          referralsResponse.length > 0
            ? (referralsResponse.filter(r => r.status === ReferralStatus.CANDIDATE_HIRED).length /
                referralsResponse.length) *
              100
            : 0,
        referrals: referralsResponse.map((referral: APIReferral) => {
          // Map API referral to local referral format
          let localStatus: 'APPLIED' | 'INTERVIEWING' | 'HIRED' | 'REJECTED' = 'APPLIED';

          switch (referral.status) {
            case ReferralStatus.CANDIDATE_APPLIED:
              localStatus = 'APPLIED';
              break;
            case ReferralStatus.CANDIDATE_INTERVIEWED:
              localStatus = 'INTERVIEWING';
              break;
            case ReferralStatus.CANDIDATE_HIRED:
            case ReferralStatus.BOUNTY_APPROVED:
            case ReferralStatus.BOUNTY_PAID:
              localStatus = 'HIRED';
              break;
            case ReferralStatus.CANCELLED:
            case ReferralStatus.EXPIRED:
              localStatus = 'REJECTED';
              break;
          }

          return {
            id: referral.id,
            candidateName: referral.candidate?.fullName || 'Unknown Candidate',
            candidateEmail: referral.candidate?.email || '',
            jobTitle: referral.job?.jobType || 'Position',
            companyName: referral.job?.companyName || 'Company',
            status: localStatus,
            appliedAt: referral.candidateAppliedAt || referral.createdAt,
            hiredAt: referral.candidateHiredAt,
            commission: referral.bountyAmount || 0,
            isPaid: referral.status === ReferralStatus.BOUNTY_PAID,
            paidAt: referral.bountyPaidAt,
          };
        }),
        monthlyEarnings: generateMonthlyEarnings(referralsResponse),
      };

      setEarningsData(processedEarningsData);
    } catch (err) {
      console.error('Error loading data:', err);
      setError('Failed to load earnings data. Please try again later.');
    } finally {
      setLoading(false);
    }
  }, [user?.sub]);

  // Generate monthly earnings from referrals
  const generateMonthlyEarnings = (referrals: APIReferral[]) => {
    const monthlyData: { [key: string]: { earnings: number; referrals: number } } = {};

    // Get last 6 months
    for (let i = 5; i >= 0; i--) {
      const month = format(subMonths(new Date(), i), 'yyyy-MM');
      monthlyData[month] = { earnings: 0, referrals: 0 };
    }

    // Calculate earnings per month
    referrals.forEach(referral => {
      if (referral.bountyAmount && referral.candidateHiredAt) {
        const month = format(parseISO(referral.candidateHiredAt), 'yyyy-MM');
        if (monthlyData[month]) {
          monthlyData[month].earnings += referral.bountyAmount;
          monthlyData[month].referrals += 1;
        }
      }
    });

    return Object.entries(monthlyData).map(([month, data]) => ({
      month,
      earnings: data.earnings,
      referrals: data.referrals,
    }));
  };

  useEffect(() => {
    if (user && !userLoading) {
      loadData();
    }
  }, [user, userLoading, loadData]);

  const handleStripeConnect = async () => {
    setStripeLoading(true);
    try {
      if (partner) {
        // Call API to create Stripe Connect account and get onboarding link
        const response = await referralApi.createStripeConnectAccount(partner.id);
        if (response.url) {
          window.location.href = response.url;
        }
      }
    } catch (error) {
      console.error('Error connecting Stripe:', error);
    } finally {
      setStripeLoading(false);
    }
  };

  const handleRequestPayout = async () => {
    if (!partner || !earningsData) return;

    try {
      await referralApi.requestPayment(partner.id, earningsData.pendingEarnings);
      // Refresh data
      await loadData();
    } catch (error) {
      console.error('Error requesting payout:', error);
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'HIRED':
        return (
          <span className="flex items-center gap-1 px-2 py-1 bg-green-500/20 text-green-300 rounded-full text-xs">
            <CheckCircle className="w-3 h-3" />
            Hired
          </span>
        );
      case 'INTERVIEWING':
        return (
          <span className="flex items-center gap-1 px-2 py-1 bg-blue-500/20 text-blue-300 rounded-full text-xs">
            <Clock className="w-3 h-3" />
            Interviewing
          </span>
        );
      case 'APPLIED':
        return (
          <span className="flex items-center gap-1 px-2 py-1 bg-yellow-500/20 text-yellow-300 rounded-full text-xs">
            <AlertCircle className="w-3 h-3" />
            Applied
          </span>
        );
      case 'REJECTED':
        return (
          <span className="flex items-center gap-1 px-2 py-1 bg-red-500/20 text-red-300 rounded-full text-xs">
            <XCircle className="w-3 h-3" />
            Rejected
          </span>
        );
      default:
        return null;
    }
  };

  const getPaymentBadge = (isPaid: boolean) => {
    if (isPaid) {
      return <span className="text-xs text-green-300">Paid</span>;
    }
    return <span className="text-xs text-yellow-300">Pending</span>;
  };

  const filteredReferrals =
    earningsData?.referrals.filter(referral => {
      if (selectedStatus !== 'all' && referral.status !== selectedStatus) return false;
      if (selectedMonth !== 'all') {
        const referralMonth = format(parseISO(referral.appliedAt), 'yyyy-MM');
        if (referralMonth !== selectedMonth) return false;
      }
      return true;
    }) || [];

  const downloadReport = () => {
    // Implement CSV download functionality
    console.log('Downloading earnings report...');
  };

  const handleTabChange = (tabId: TabType) => {
    setActiveTab(tabId);
  };

  const currentTab = tabs.find(tab => tab.id === activeTab);

  return (
    <AppLayout isLoading={loading || userLoading}>
      {error ? (
        <div className="relative flex h-full w-full items-center justify-center p-3">
          <Card className="max-w-md border-white/10 bg-red-900/20 backdrop-blur-lg">
            <CardContent className="p-8 text-center">
              <p className="text-white/80">{error}</p>
            </CardContent>
          </Card>
        </div>
      ) : earningsData ? (
        <div className="h-full flex flex-col relative">
          {/* Hero Header */}
          <ReferralPageHeader
            title="Earnings Dashboard"
            description="Track your referral earnings and payment history"
            icon={DollarSign}
            stats={{
              totalEarnings: earningsData.totalEarnings,
              pendingEarnings: earningsData.pendingEarnings,
              paidEarnings: earningsData.paidEarnings,
              conversionRate: earningsData.conversionRate,
            }}
          />

          {/* Header with Tabs */}
          <div className="flex-none border-b border-gray-300/10 bg-transparent h-[80px] relative mt-80">
            <div className="px-3 sm:px-6 h-full flex items-center justify-between gap-2">
              {/* Tab Navigation */}
              <div className="flex items-center h-full flex-1 min-w-0">
                <div className="flex overflow-x-auto h-full [&::-webkit-scrollbar]:hidden [-ms-overflow-style:none] [scrollbar-width:none]">
                  {tabs.map((tab, index) => (
                    <React.Fragment key={tab.id}>
                      {index > 0 && (
                        <div className="w-px bg-gray-300/10 self-center h-6 hidden sm:block" />
                      )}
                      <button
                        onClick={() => handleTabChange(tab.id)}
                        className={`
                          relative px-2 sm:px-4 h-full flex items-center gap-1 sm:gap-2 text-xs sm:text-sm font-medium transition-all whitespace-nowrap flex-shrink-0
                          ${
                            activeTab === tab.id
                              ? 'text-primary'
                              : 'text-muted-foreground hover:text-foreground hover:bg-muted/50'
                          }
                        `}
                      >
                        {/* Gradient background for active tab */}
                        {activeTab === tab.id && (
                          <div className="absolute inset-0 bg-gradient-to-b from-transparent via-purple-600/10 to-pink-600/20" />
                        )}

                        {/* Content */}
                        <span className="relative z-10 flex items-center gap-1 sm:gap-2">
                          <tab.icon className="w-3.5 sm:w-4 h-3.5 sm:h-4" />
                          <span className="hidden sm:inline">{tab.label}</span>
                          <span className="sm:hidden">
                            {tab.label
                              .split(' ')
                              .map(word => word[0])
                              .join('')}
                          </span>
                        </span>

                        {/* Bottom border for active tab */}
                        {activeTab === tab.id && (
                          <div className="absolute bottom-0 left-0 right-0 h-[2px] bg-gradient-to-r from-purple-600 to-pink-600" />
                        )}
                      </button>
                    </React.Fragment>
                  ))}
                </div>
              </div>

              {/* Right side - Action Buttons */}
              <div className="flex items-center gap-2 h-full">
                {/* Stripe Status Indicator */}
                <div className="flex items-center gap-2 px-3 py-1.5 rounded-lg bg-black/5 border border-white/10">
                  {stripeConnected ? (
                    <>
                      <CheckCircle className="w-4 h-4 text-green-400" />
                      <span className="text-xs text-green-300">Connected to Stripe</span>
                    </>
                  ) : (
                    <>
                      <CreditCard className="w-4 h-4 text-orange-400" />
                      <span className="text-xs text-orange-300">Stripe Not Connected</span>
                    </>
                  )}
                </div>

                <Button
                  variant="outline"
                  size="sm"
                  className="border-white/20 bg-white/5 hover:bg-white/10 text-white"
                  onClick={downloadReport}
                >
                  <Download className="w-4 h-4 mr-2" />
                  Export
                </Button>
              </div>
            </div>
          </div>

          {/* Tab Content */}
          <div className="flex-1 overflow-y-auto relative bg-background">
            <div className="relative min-h-full">
              <motion.div
                key={activeTab}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -20 }}
                transition={{ duration: 0.2 }}
                className="w-full"
              >
                <div className="p-4 sm:p-6 lg:p-8 pb-24 max-w-7xl mx-auto">
                  {/* Stripe Connect Banner */}
                  <StripeConnectBanner
                    isConnected={stripeConnected}
                    isLoading={stripeLoading}
                    onConnect={handleStripeConnect}
                  />

                  {activeTab === 'referral-details' && (
                    <>
                      {/* Referrals Table */}
                      <Card className="border-white/10 bg-black/5 backdrop-blur-lg">
                        <CardHeader>
                          <div className="flex items-center justify-between">
                            <CardTitle className="text-lg text-white flex items-center">
                              <Eye className="w-5 h-5 mr-2 text-pink-400" />
                              Referral Details
                            </CardTitle>

                            {/* Filter Button */}
                            <Button
                              variant="outline"
                              size="sm"
                              className="border-white/20 bg-white/5 hover:bg-white/10 text-white"
                              onClick={() => setShowFilters(!showFilters)}
                            >
                              <Filter className="h-4 w-4 mr-2" />
                              Filters
                              <ChevronDown
                                className={`h-4 w-4 ml-2 transition-transform ${showFilters ? 'rotate-180' : ''}`}
                              />
                            </Button>
                          </div>

                          {/* Filter Options */}
                          {showFilters && (
                            <div className="flex gap-4 mt-4 pt-4 border-t border-white/10">
                              <select
                                value={selectedStatus}
                                onChange={e => setSelectedStatus(e.target.value)}
                                className="px-3 py-1.5 bg-black/30 border border-white/20 rounded-lg text-sm text-white backdrop-blur-md"
                              >
                                <option value="all">All Status</option>
                                <option value="HIRED">Hired</option>
                                <option value="INTERVIEWING">Interviewing</option>
                                <option value="APPLIED">Applied</option>
                                <option value="REJECTED">Rejected</option>
                              </select>

                              <select
                                value={selectedMonth}
                                onChange={e => setSelectedMonth(e.target.value)}
                                className="px-3 py-1.5 bg-black/30 border border-white/20 rounded-lg text-sm text-white backdrop-blur-md"
                              >
                                <option value="all">All Months</option>
                                {earningsData.monthlyEarnings.map(month => (
                                  <option key={month.month} value={month.month}>
                                    {format(parseISO(month.month + '-01'), 'MMMM yyyy')}
                                  </option>
                                ))}
                              </select>
                            </div>
                          )}
                        </CardHeader>
                        <CardContent>
                          <div className="overflow-x-auto">
                            <table className="w-full">
                              <thead>
                                <tr className="border-b border-white/10 text-left">
                                  <th className="pb-3 text-sm font-medium text-white/70">
                                    Candidate
                                  </th>
                                  <th className="pb-3 text-sm font-medium text-white/70">
                                    Position
                                  </th>
                                  <th className="pb-3 text-sm font-medium text-white/70">Status</th>
                                  <th className="pb-3 text-sm font-medium text-white/70">
                                    Applied
                                  </th>
                                  <th className="pb-3 text-sm font-medium text-white/70">
                                    Commission
                                  </th>
                                  <th className="pb-3 text-sm font-medium text-white/70">
                                    Payment
                                  </th>
                                </tr>
                              </thead>
                              <tbody>
                                {filteredReferrals.map(referral => (
                                  <tr
                                    key={referral.id}
                                    className="border-b border-white/5 hover:bg-white/5 transition-colors"
                                  >
                                    <td className="py-4">
                                      <div>
                                        <p className="text-sm font-medium text-white">
                                          {referral.candidateName}
                                        </p>
                                        <p className="text-xs text-white/60">
                                          {referral.candidateEmail}
                                        </p>
                                      </div>
                                    </td>
                                    <td className="py-4">
                                      <div>
                                        <p className="text-sm text-white">{referral.jobTitle}</p>
                                        <p className="text-xs text-white/60">
                                          {referral.companyName}
                                        </p>
                                      </div>
                                    </td>
                                    <td className="py-4">{getStatusBadge(referral.status)}</td>
                                    <td className="py-4">
                                      <p className="text-sm text-white/80">
                                        {format(parseISO(referral.appliedAt), 'MMM d, yyyy')}
                                      </p>
                                    </td>
                                    <td className="py-4">
                                      <p className="text-sm font-medium text-white">
                                        {referral.commission > 0
                                          ? `$${referral.commission.toLocaleString()}`
                                          : '-'}
                                      </p>
                                    </td>
                                    <td className="py-4">
                                      {referral.commission > 0 && (
                                        <>
                                          {getPaymentBadge(referral.isPaid)}
                                          {referral.paidAt && (
                                            <p className="text-xs text-white/60 mt-1">
                                              {format(parseISO(referral.paidAt), 'MMM d')}
                                            </p>
                                          )}
                                        </>
                                      )}
                                    </td>
                                  </tr>
                                ))}
                              </tbody>
                            </table>
                          </div>

                          {filteredReferrals.length === 0 && (
                            <div className="text-center py-8">
                              <p className="text-white/60">
                                No referrals found matching your filters.
                              </p>
                            </div>
                          )}
                        </CardContent>
                      </Card>
                    </>
                  )}

                  {activeTab === 'earnings' && (
                    <>
                      {/* Payout Section - only show if Stripe is connected and there are pending earnings */}
                      {stripeConnected && earningsData.pendingEarnings > 0 && (
                        <div className="bg-purple-500/5 border border-purple-500/5 rounded-lg p-4 mb-6">
                          <div className="flex items-center justify-between">
                            <div>
                              <p className="text-sm font-medium text-white">Available for Payout</p>
                              <p className="text-2xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-white to-purple-200">
                                ${earningsData.pendingEarnings.toLocaleString()}
                              </p>
                            </div>
                            <Button
                              onClick={handleRequestPayout}
                              className="bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700"
                            >
                              Request Payout
                            </Button>
                          </div>
                        </div>
                      )}
                      {/* Monthly Trend Chart */}
                      <Card className="border-white/10 bg-black/5 backdrop-blur-lg">
                        <CardHeader>
                          <CardTitle className="text-lg text-white flex items-center">
                            <TrendingUp className="w-5 h-5 mr-2 text-pink-400" />
                            Monthly Earnings Trend
                          </CardTitle>
                        </CardHeader>
                        <CardContent>
                          <div className="h-64 flex items-end justify-between gap-4">
                            {earningsData.monthlyEarnings.map((month, index) => {
                              const maxEarnings = Math.max(
                                ...earningsData.monthlyEarnings.map(m => m.earnings)
                              );
                              const heightPercentage =
                                maxEarnings > 0 ? (month.earnings / maxEarnings) * 100 : 0;

                              return (
                                <div
                                  key={month.month}
                                  className="flex-1 flex flex-col items-center"
                                >
                                  <div className="relative w-full">
                                    <motion.div
                                      initial={{ height: 0 }}
                                      animate={{ height: `${heightPercentage * 2}px` }}
                                      transition={{ delay: index * 0.1, duration: 0.5 }}
                                      className="bg-gradient-to-t from-purple-600 to-pink-500 rounded-t-lg relative group cursor-pointer"
                                    >
                                      <div className="absolute -top-8 left-1/2 transform -translate-x-1/2 opacity-0 group-hover:opacity-100 transition-opacity bg-gray-900/90 px-2 py-1 rounded text-xs whitespace-nowrap">
                                        ${month.earnings.toLocaleString()}
                                      </div>
                                    </motion.div>
                                  </div>
                                  <p className="text-xs text-white/60 mt-2">
                                    {format(parseISO(month.month + '-01'), 'MMM')}
                                  </p>
                                  <p className="text-xs text-white/40">
                                    {month.referrals} referrals
                                  </p>
                                </div>
                              );
                            })}
                          </div>
                        </CardContent>
                      </Card>
                    </>
                  )}
                </div>
              </motion.div>
            </div>
          </div>
        </div>
      ) : null}
    </AppLayout>
  );
}
