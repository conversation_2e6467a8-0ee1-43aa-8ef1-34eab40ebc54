'use client';

import { referralApi } from '@/app/referral-partner/services/referralApi';
import { ReferralPartner } from '@/app/referral-partner/types';
import ReferralPageHeader from '@/components/common/ReferralPageHeader';
import AppLayout from '@/components/steps/layout/AppLayout';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { useInfiniteLoopProtection } from '@/hooks/useInfiniteLoopProtection';
import { useUser } from '@auth0/nextjs-auth0/client';
import { motion } from 'framer-motion';
import {
  Briefcase,
  Building2,
  Check,
  ChevronLeft,
  ChevronRight,
  Clock,
  Coins,
  Copy,
  MapPin,
  Search,
  Users,
  X,
} from 'lucide-react';
import { useCallback, useEffect, useState } from 'react';

interface Job {
  id: string;
  jobType: string;
  department: string;
  companyName: string;
  location?: string[];
  salaryRange?: string;
  experienceLevel?: string;
  typeOfJob?: string;
  isPublished: boolean;
  slug?: string;
  createdAt: string;
  referralSettings?: {
    acceptsReferrals: boolean;
    bountyConfiguration?: {
      type: 'PERCENTAGE' | 'FIXED';
      value: number;
    };
  };
  metrics?: {
    views: number;
    applications: number;
  };
}

const ITEMS_PER_PAGE = 10;

const formatJobType = (type?: string): string => {
  if (!type) return 'Full-time';

  const typeMap: Record<string, string> = {
    FULL_TIME: 'Full-time',
    PART_TIME: 'Part-time',
    CONTRACT: 'Contract',
    FREELANCE: 'Freelance',
    INTERNSHIP: 'Internship',
    TEMPORARY: 'Temporary',
    VOLUNTEER: 'Volunteer',
    REMOTE: 'Remote',
  };

  return (
    typeMap[type] ||
    type
      .replace(/_/g, ' ')
      .toLowerCase()
      .replace(/\b\w/g, l => l.toUpperCase())
  );
};

export default function ReferralJobBoardPage() {
  const { user, isLoading: userLoading } = useUser();
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [jobs, setJobs] = useState<Job[]>([]);
  const [totalJobs, setTotalJobs] = useState(0);
  const [partner, setPartner] = useState<ReferralPartner | null>(null);
  const [copiedJobId, setCopiedJobId] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [currentPage, setCurrentPage] = useState(1);

  // Add infinite loop protection
  useInfiniteLoopProtection('ReferralJobBoardPage', {
    maxRenders: 30,
    timeWindow: 3000,
    onLoopDetected: () => {
      console.error('Infinite loop detected in ReferralJobBoardPage');
      setError('An error occurred while loading the page. Please refresh.');
    },
  });

  const loadData = useCallback(
    async (page: number = 1, search: string = '') => {
      try {
        setLoading(true);
        setError(null);

        if (!user?.sub) {
          setError('User not authenticated.');
          setLoading(false);
          return;
        }

        // Get partner data if not already loaded
        if (!partner) {
          const partnerResponse = await referralApi.getPartnerByClientId(user.sub);

          if (!partnerResponse) {
            setError('No referral partner profile found.');
            setLoading(false);
            return;
          }

          setPartner(partnerResponse);
        }

        // Calculate offset for pagination
        const offset = (page - 1) * ITEMS_PER_PAGE;

        // Fetch published jobs from the referral-specific endpoint with pagination
        const jobsResponse = await referralApi.getAvailableJobs({
          limit: ITEMS_PER_PAGE,
          offset,
          sort: 'createdAt',
          order: 'DESC',
          search: search || undefined,
        });

        // The backend now returns paginated jobs
        const eligibleJobs = jobsResponse.jobs || [];
        const total = jobsResponse.total || 0;

        setJobs(eligibleJobs);
        setTotalJobs(total);
      } catch (err) {
        console.error('Error loading data:', err);
        setError('Failed to load jobs. Please try again later.');
      } finally {
        setLoading(false);
      }
    },
    [user, partner]
  );

  useEffect(() => {
    if (user && !userLoading) {
      loadData(currentPage, searchQuery);
    }
  }, [user, userLoading, currentPage, searchQuery]);

  // Debounce search to avoid too many API calls
  useEffect(() => {
    const timer = setTimeout(() => {
      if (user && !userLoading) {
        setCurrentPage(1);
        loadData(1, searchQuery);
      }
    }, 500);

    return () => clearTimeout(timer);
  }, [searchQuery]);

  // Calculate total pages
  const totalPages = Math.ceil(totalJobs / ITEMS_PER_PAGE);

  const getReferralLink = (jobId: string) => {
    if (!partner) return '';
    return `https://app.kaleidotalent.com/open-jobs/${jobId}?referral=${partner.referralCode}`;
  };

  const copyReferralLink = (jobId: string) => {
    const link = getReferralLink(jobId);
    navigator.clipboard.writeText(link);
    setCopiedJobId(jobId);
    setTimeout(() => setCopiedJobId(null), 2000);
  };

  const formatBounty = (job: Job) => {
    const bounty = job.referralSettings?.bountyConfiguration;
    if (!bounty) return '10% commission';

    if (bounty.type === 'PERCENTAGE') {
      return `${bounty.value}%`;
    } else {
      return `$${bounty.value.toLocaleString()}`;
    }
  };

  const isHighValueBounty = (job: Job) => {
    const bounty = job.referralSettings?.bountyConfiguration;
    if (!bounty) return false;

    if (bounty.type === 'PERCENTAGE') {
      return bounty.value >= 15; // 15% or higher
    } else {
      return bounty.value >= 5000; // $5000 or higher
    }
  };

  return (
    <AppLayout isLoading={loading || userLoading}>
      {error ? (
        <div className="relative flex h-full w-full items-center justify-center p-3">
          <Card className="max-w-md border-white/10 bg-red-900/20 backdrop-blur-lg">
            <CardContent className="p-8 text-center">
              <p className="text-white/80">{error}</p>
            </CardContent>
          </Card>
        </div>
      ) : (
        <div className="w-full relative">
          {/* Hero Header */}
          <ReferralPageHeader
            title="Open Job Board"
            description="Browse available positions and share your referral links"
            icon={Briefcase}
          />

          <motion.div
            className="w-full max-w-7xl mx-auto px-4 py-6 mt-80"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
          >
            {/* Quick Stats */}
            <div className="flex items-center justify-between mb-6">
              <div className="text-white/60">
                Showing {jobs.length} of {totalJobs} jobs
              </div>
            </div>

            {/* Search Bar */}
            <div className="mb-6">
              <div className="relative max-w-md">
                <Search className="w-5 h-5 text-gray-500 absolute left-3 top-1/2 transform -translate-y-1/2" />
                <input
                  type="search"
                  placeholder="Search by job title, company, department, or location..."
                  className="pl-10 pr-10 py-2 w-full border border-white/20 bg-black/30 backdrop-blur-md rounded-lg text-white placeholder:text-white/50 focus:outline-none focus:ring-2 focus:ring-purple-500"
                  value={searchQuery}
                  onChange={e => setSearchQuery(e.target.value)}
                />
                {searchQuery && (
                  <button
                    onClick={() => setSearchQuery('')}
                    className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-white"
                  >
                    <X className="w-5 h-5" />
                  </button>
                )}
              </div>
            </div>

            {/* Jobs Table */}
            <Card className="border-white/10 bg-black/20 backdrop-blur-lg">
              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead>
                    <tr className="border-b border-white/10">
                      <th className="px-4 py-3 text-left text-sm font-medium text-white/70">
                        <div className="flex items-center gap-2">
                          <Briefcase className="w-4 h-4" />
                          Job Title
                        </div>
                      </th>
                      <th className="px-4 py-3 text-left text-sm font-medium text-white/70">
                        <div className="flex items-center gap-2">
                          <Building2 className="w-4 h-4" />
                          Company
                        </div>
                      </th>
                      <th className="px-4 py-3 text-left text-sm font-medium text-white/70">
                        <div className="flex items-center gap-2">
                          <Users className="w-4 h-4" />
                          Department
                        </div>
                      </th>
                      <th className="px-4 py-3 text-left text-sm font-medium text-white/70">
                        <div className="flex items-center gap-2">
                          <MapPin className="w-4 h-4" />
                          Location
                        </div>
                      </th>
                      <th className="px-4 py-3 text-left text-sm font-medium text-white/70">
                        <div className="flex items-center gap-2">
                          <Coins className="w-4 h-4" />
                          Commission
                        </div>
                      </th>
                      <th className="px-4 py-3 text-left text-sm font-medium text-white/70">
                        <div className="flex items-center gap-2">
                          <Clock className="w-4 h-4" />
                          Type
                        </div>
                      </th>
                      <th className="px-4 py-3 text-center text-sm font-medium text-white/70">
                        Action
                      </th>
                    </tr>
                  </thead>
                  <tbody>
                    {jobs.map(job => (
                      <tr
                        key={job.id}
                        className="border-b border-white/5 hover:bg-white/5 transition-colors"
                      >
                        <td className="px-4 py-3 text-sm text-white">{job.jobType}</td>
                        <td className="px-4 py-3 text-sm text-white/80">{job.companyName}</td>
                        <td className="px-4 py-3 text-sm text-white/60">{job.department}</td>
                        <td className="px-4 py-3 text-sm text-white/60">
                          <div className="flex items-center gap-1">
                            <MapPin className="w-3 h-3" />
                            {job.location?.length ? job.location[0] : 'Remote'}
                          </div>
                        </td>
                        <td className="px-4 py-3 text-sm">
                          <span
                            className={`inline-flex items-center gap-1 px-2 py-1 rounded-full text-xs ${
                              isHighValueBounty(job)
                                ? 'bg-yellow-500/20 text-yellow-300'
                                : 'bg-green-500/20 text-green-300'
                            }`}
                          >
                            <Coins className="w-3 h-3" />
                            {formatBounty(job)}
                          </span>
                        </td>
                        <td className="px-4 py-3 text-sm text-white/60">
                          {formatJobType(job.typeOfJob)}
                        </td>
                        <td className="px-4 py-3 text-center">
                          <button
                            onClick={() => copyReferralLink(job.id)}
                            className="inline-flex items-center justify-center p-2 text-white/60 hover:text-white hover:bg-white/10 rounded-lg transition-all"
                            title="Copy referral link"
                          >
                            {copiedJobId === job.id ? (
                              <Check className="w-4 h-4 text-green-400" />
                            ) : (
                              <Copy className="w-4 h-4" />
                            )}
                          </button>
                        </td>
                      </tr>
                    ))}
                    {jobs.length === 0 && (
                      <tr>
                        <td colSpan={7} className="px-4 py-8 text-center text-white/60">
                          No jobs found matching your search criteria
                        </td>
                      </tr>
                    )}
                  </tbody>
                </table>
              </div>

              {/* Pagination */}
              {totalPages > 1 && (
                <div className="flex items-center justify-between px-4 py-3 border-t border-white/10">
                  <div className="text-sm text-white/60">
                    Page {currentPage} of {totalPages}
                  </div>
                  <div className="flex gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
                      disabled={currentPage === 1}
                      className="border-white/20 bg-white/5 hover:bg-white/10 text-white disabled:opacity-50"
                    >
                      <ChevronLeft className="w-4 h-4" />
                      Previous
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setCurrentPage(prev => Math.min(totalPages, prev + 1))}
                      disabled={currentPage === totalPages}
                      className="border-white/20 bg-white/5 hover:bg-white/10 text-white disabled:opacity-50"
                    >
                      Next
                      <ChevronRight className="w-4 h-4" />
                    </Button>
                  </div>
                </div>
              )}
            </Card>
          </motion.div>
        </div>
      )}
    </AppLayout>
  );
}
