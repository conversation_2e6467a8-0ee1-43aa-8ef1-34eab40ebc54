import Head from 'next/head';
import { useRouter } from 'next/router';
import Script from 'next/script';
import { useEffect, useState } from 'react';

import apiHelper from '@/lib/apiHelper';

// Define the structure of profile data

interface ProfileData {
  resumeHtml: string;
  firstName: string;
  lastName: string;
}

export async function getServerSideProps() {
  return {
    props: {}, // Pass empty props, we'll fetch data client-side
  };
}

export default function CandidateProfilePage() {
  const router = useRouter();
  const { id } = router.query;
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [profileData, setProfileData] = useState<ProfileData | null>(null);

  useEffect(() => {
    const fetchProfile = async () => {
      if (!id) return;

      try {
        // First try graduate public profile
        try {
          const graduateData = await apiHelper.get(`/graduates/public/${id}`);
          if (graduateData?.resumeHtml) {
            setProfileData({
              resumeHtml: graduateData.resumeHtml,
              firstName: graduateData.firstName || '',
              lastName: graduateData.lastName || '',
            });
            setLoading(false);
            return;
          }
        } catch (graduateError) {}

        // If graduate not found, try candidate profile
        try {
          const candidateData = await apiHelper.get(`/candidate-profiles/${id}`);
          if (candidateData?.resumeHtml) {
            setProfileData({
              resumeHtml: candidateData.resumeHtml,
              firstName: candidateData.firstName || '',
              lastName: candidateData.lastName || '',
            });
            setLoading(false);
            return;
          }
        } catch (candidateError) {}

        // If candidate not found, try job seeker profile
        try {
          const jobSeekerData = await apiHelper.get(`/job-seekers/${id}`);
          if (jobSeekerData?.resumeHtml) {
            setProfileData({
              resumeHtml: jobSeekerData.resumeHtml,
              firstName: jobSeekerData.firstName || '',
              lastName: jobSeekerData.lastName || '',
            });
            setLoading(false);
            return;
          }
        } catch (jobSeekerError) {}

        // If no profile found
        setError(
          `Profile with ID ${id} not found or resume not generated. This could be because the profile is not a graduate, candidate, or job seeker profile, or the resume has not been generated yet.`
        );
        setLoading(false);
      } catch (error) {
        setError(error instanceof Error ? error.message : 'Failed to load profile');
        setLoading(false);
      }
    };

    fetchProfile();
  }, [id]);

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-gray-900 to-black text-white flex items-center justify-center p-4">
        <div className="text-center">
          <h1 className="text-2xl font-bold mb-2">Loading...</h1>
        </div>
      </div>
    );
  }

  if (error || !profileData?.resumeHtml) {
    return (
      <>
        <Head>
          <title>Resume Not Found</title>
        </Head>
        <div className="min-h-screen bg-gradient-to-br from-gray-900 to-black text-white flex items-center justify-center p-4">
          <div className="text-center">
            <h1 className="text-2xl font-bold mb-2">Resume Not Found</h1>
            <p className="text-gray-400">
              {error || 'This candidate has not generated their resume yet.'}
            </p>
          </div>
        </div>
      </>
    );
  }

  return (
    <>
      <Head>
        <title>
          {profileData.firstName} {profileData.lastName} - Resume
        </title>
      </Head>
      <Script src="https://cdn.tailwindcss.com" strategy="afterInteractive" />
      <div className="min-h-screen bg-gradient-to-br from-gray-900 to-black">
        <div
          className="container mx-auto p-4 lg:p-8"
          dangerouslySetInnerHTML={{ __html: profileData.resumeHtml }}
        />
      </div>
    </>
  );
}
