import Link from 'next/link';

export async function getServerSideProps() {
  return {
    props: {}, // Pass empty props
  };
}

export default function NotFound() {
  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-gray-900 to-black text-white">
      <div className="text-center">
        <h1 className="text-3xl font-bold mb-4">Profile Not Found</h1>
        <p className="text-gray-400 mb-6">
          The candidate profile you're looking for doesn't exist.
        </p>
        <Link
          href="/"
          className="inline-flex items-center justify-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700"
        >
          Go Home
        </Link>
      </div>
    </div>
  );
}
