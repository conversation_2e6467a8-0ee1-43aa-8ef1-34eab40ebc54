'use client';

import { referral<PERSON>pi } from '@/app/referral-partner/services/referralApi';
import { DashboardData } from '@/app/referral-partner/types';
import AppLayout from '@/components/steps/layout/AppLayout';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { SIDEBAR } from '@/constants/layout';
import { useUser } from '@auth0/nextjs-auth0/client';
import { motion } from 'framer-motion';
import {
  Check,
  Copy,
  CreditCard,
  DollarSign,
  FileText,
  Percent,
  PlusCircle,
  Send,
  TrendingUp,
  Users,
} from 'lucide-react';
import Image from 'next/image';
import { useEffect, useState } from 'react';

export default function ReferralPartnerPage() {
  const { user, isLoading: userLoading } = useUser();
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [dashboardData, setDashboardData] = useState<DashboardData | null>(null);
  const [copied, setCopied] = useState(false);
  const [sidebarExpanded, setSidebarExpanded] = useState(false);

  // Get sidebar state from localStorage and listen for changes
  useEffect(() => {
    const checkSidebarState = () => {
      const savedState = localStorage.getItem('sidebarExpanded');
      if (savedState !== null) {
        setSidebarExpanded(JSON.parse(savedState));
      }
    };

    // Initial check
    checkSidebarState();

    // Listen for storage changes
    const handleStorageChange = (e: StorageEvent) => {
      if (e.key === 'sidebarExpanded') {
        checkSidebarState();
      }
    };

    window.addEventListener('storage', handleStorageChange);

    // Also check periodically for same-tab changes
    const interval = setInterval(checkSidebarState, 100);

    return () => {
      window.removeEventListener('storage', handleStorageChange);
      clearInterval(interval);
    };
  }, []);

  const loadDashboardData = async () => {
    try {
      setLoading(true);
      setError(null);

      if (!user?.sub) {
        setError('User not authenticated.');
        return;
      }

      const partnerResponse = await referralApi.getPartnerByClientId(user.sub);

      if (!partnerResponse) {
        setError('No referral partner profile found for your account.');
        return;
      }

      const dashboard = await referralApi.getDashboard(partnerResponse.id);
      setDashboardData(dashboard);
    } catch (err) {
      console.error('Error loading dashboard:', err);
      setError('Failed to load dashboard data. Please try again later.');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (user && !userLoading) {
      loadDashboardData();
    }
  }, [user, userLoading]);

  const copyReferralLink = () => {
    if (!dashboardData) return;
    const referralLink = `https://kaleidotalent.com/jobs?ref=${dashboardData.partner.referralCode}`;
    navigator.clipboard.writeText(referralLink);
    setCopied(true);
    setTimeout(() => setCopied(false), 2000);
  };

  return (
    <AppLayout isLoading={loading || userLoading}>
      {error || !dashboardData ? (
        <div className="relative flex h-full w-full items-center justify-center p-3">
          <Card className="max-w-md border-white/5 bg-red-900/10">
            <CardContent className="p-8 text-center">
              <p className="text-white/80">{error || 'Unable to load dashboard data.'}</p>
            </CardContent>
          </Card>
        </div>
      ) : (
        <div className="relative">
          {/* Hero Section with Background Image - Fixed position */}
          <div className="fixed top-0 left-0 right-0 h-[350px] z-0">
            <div
              className="absolute inset-0 transition-all duration-300"
              style={{
                marginLeft:
                  window.innerWidth >= 1024
                    ? sidebarExpanded
                      ? SIDEBAR.EXPANDED_WIDTH
                      : SIDEBAR.COLLAPSED_WIDTH
                    : '0px',
              }}
            >
              <Image
                src="/images/landing/open-jobs/open-jobs-7.webp"
                alt="Referral Partner Dashboard Hero"
                fill
                className="object-cover"
                priority
              />
              {/* Bottom to top gradient overlay - darker at bottom, fading to very light */}
              <div className="absolute inset-0 bg-gradient-to-t from-purple-950/80 via-pink-800/40 to-purple-600/10" />
              {/* Additional pink tint gradient */}
              <div className="absolute inset-0 bg-gradient-to-t from-purple-900/60 via-transparent to-transparent" />
            </div>
          </div>

          {/* Main Content Container */}
          <div className="relative flex flex-col w-full">
            {/* Hero Content - Fixed with image */}
            <div className="fixed top-0 left-0 right-0 h-[350px] flex flex-col justify-between pt-20 pb-4 z-10">
              {/* Welcome Section - Top */}
              <div
                className="w-full px-6 sm:px-8"
                style={{
                  paddingLeft:
                    window.innerWidth >= 1024
                      ? `calc(1.5rem + ${sidebarExpanded ? SIDEBAR.EXPANDED_WIDTH : SIDEBAR.COLLAPSED_WIDTH})`
                      : '1.5rem',
                }}
              >
                <div className="flex-1">
                  <h1 className="text-3xl sm:text-4xl font-bold text-white mb-2 drop-shadow-lg">
                    Referral Partner Dashboard
                  </h1>
                  <p className="text-base sm:text-lg text-gray-100 drop-shadow">
                    Welcome back, {dashboardData.partner.partnerName}
                  </p>
                </div>
              </div>

              {/* Key Metrics on overlay - Bottom right */}
              <div
                className="w-full px-6 sm:px-8 flex justify-end"
                style={{
                  paddingRight: '2rem',
                  paddingLeft:
                    window.innerWidth >= 1024
                      ? `calc(1.5rem + ${sidebarExpanded ? SIDEBAR.EXPANDED_WIDTH : SIDEBAR.COLLAPSED_WIDTH})`
                      : '1.5rem',
                }}
              >
                <div className="overflow-hidden rounded-lg border border-white/10 backdrop-blur-md bg-white/5 max-w-fit">
                  <div className="grid grid-cols-2 sm:grid-cols-4 divide-x divide-white/10">
                    {/* Total Earnings */}
                    <motion.div
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: 0.1 }}
                      className="relative p-3 hover:bg-green-500/10 transition-all duration-200 group"
                    >
                      <div className="flex flex-col">
                        <p className="text-[10px] font-medium text-white/60 mb-1">Total Earnings</p>
                        <p className="text-lg font-bold text-white">
                          ${dashboardData.earnings.total}
                        </p>
                      </div>
                      <div className="absolute right-2 top-1/2 transform -translate-y-1/2 opacity-10 group-hover:opacity-20 transition-opacity duration-200">
                        <DollarSign className="w-8 h-8 text-green-400" />
                      </div>
                    </motion.div>

                    {/* Pending Earnings */}
                    <motion.div
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: 0.2 }}
                      className="relative p-3 hover:bg-yellow-500/10 transition-all duration-200 group"
                    >
                      <div className="flex flex-col">
                        <p className="text-[10px] font-medium text-white/60 mb-1">Pending</p>
                        <p className="text-lg font-bold text-white">
                          ${dashboardData.earnings.pending}
                        </p>
                      </div>
                      <div className="absolute right-2 top-1/2 transform -translate-y-1/2 opacity-10 group-hover:opacity-20 transition-opacity duration-200">
                        <TrendingUp className="w-8 h-8 text-yellow-400" />
                      </div>
                    </motion.div>

                    {/* Total Referrals */}
                    <motion.div
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: 0.3 }}
                      className="relative p-3 hover:bg-blue-500/10 transition-all duration-200 group"
                    >
                      <div className="flex flex-col">
                        <p className="text-[10px] font-medium text-white/60 mb-1">Referrals</p>
                        <p className="text-lg font-bold text-white">
                          {dashboardData.metrics?.totalReferrals || 0}
                        </p>
                      </div>
                      <div className="absolute right-2 top-1/2 transform -translate-y-1/2 opacity-10 group-hover:opacity-20 transition-opacity duration-200">
                        <Users className="w-8 h-8 text-blue-400" />
                      </div>
                    </motion.div>

                    {/* Conversion Rate */}
                    <motion.div
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: 0.4 }}
                      className="relative p-3 hover:bg-purple-500/10 transition-all duration-200 group"
                    >
                      <div className="flex flex-col">
                        <p className="text-[10px] font-medium text-white/60 mb-1">Conversion</p>
                        <p className="text-lg font-bold text-white">
                          {dashboardData.metrics?.conversionRate?.toFixed(1) || 0}%
                        </p>
                      </div>
                      <div className="absolute right-2 top-1/2 transform -translate-y-1/2 opacity-10 group-hover:opacity-20 transition-opacity duration-200">
                        <Percent className="w-8 h-8 text-purple-400" />
                      </div>
                    </motion.div>
                  </div>
                </div>
              </div>
            </div>

            {/* Spacer for fixed hero */}
            <div className="h-[210px]"></div>

            {/* Main Dashboard Content */}
            <div className="relative w-full z-30">
              <div className="w-full max-w-7xl mx-auto px-3 sm:px-4 pt-20">
                {/* Quick Actions */}
                <div className="mb-8">
                  <h2 className="text-sm font-medium text-white/80 mb-4 flex items-center">
                    <PlusCircle className="w-4 h-4 text-pink-400 mr-2" />
                    Quick Actions
                    <div className="h-px bg-gradient-to-r from-pink-500/20 to-transparent flex-grow ml-3"></div>
                  </h2>
                  <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
                    {/* Referral Link Card */}
                    <Card className="group overflow-hidden border border-white/5 bg-purple-300/5 hover:bg-purple-400/10 transition-all duration-300">
                      <div className="relative h-32 overflow-hidden">
                        <Image
                          src="/images/landing/open-jobs/open-jobs-7.webp"
                          alt="Referral Link"
                          fill
                          className="object-cover transition-transform duration-500 group-hover:scale-105"
                        />
                        <div className="absolute inset-0 bg-gradient-to-t from-purple-900/80 to-transparent"></div>
                      </div>
                      <CardHeader className="pb-2">
                        <CardTitle className="text-sm flex items-center text-white">
                          <Send className="w-4 h-4 text-pink-400 mr-2" />
                          Your Referral Link
                        </CardTitle>
                      </CardHeader>
                      <CardContent className="pb-4">
                        <div className="bg-white/10 backdrop-blur rounded-lg px-3 py-2 mb-3">
                          <code className="text-xs text-white/80 break-all">
                            https://kaleidotalent.com/jobs?ref={dashboardData.partner.referralCode}
                          </code>
                        </div>
                        <button
                          onClick={copyReferralLink}
                          className="w-full flex items-center justify-center px-4 py-2 bg-white/20 hover:bg-white/30 text-white rounded-lg transition-all font-medium text-sm"
                        >
                          {copied ? (
                            <>
                              <Check className="w-4 h-4 mr-2" />
                              Copied!
                            </>
                          ) : (
                            <>
                              <Copy className="w-4 h-4 mr-2" />
                              Copy Link
                            </>
                          )}
                        </button>
                      </CardContent>
                    </Card>

                    {/* View Referrals Card */}
                    <Card className="group overflow-hidden border border-white/5 bg-purple-300/5 hover:bg-purple-400/10 transition-all duration-300 cursor-pointer">
                      <div className="relative h-32 overflow-hidden">
                        <Image
                          src="/images/narrow/expand-3.webp"
                          alt="View Referrals"
                          fill
                          className="object-cover transition-transform duration-500 group-hover:scale-105"
                        />
                        <div className="absolute inset-0 bg-gradient-to-t from-purple-900/80 to-transparent"></div>
                      </div>
                      <CardHeader className="pb-2">
                        <CardTitle className="text-sm flex items-center text-white">
                          <Users className="w-4 h-4 text-pink-400 mr-2" />
                          View Referrals
                        </CardTitle>
                      </CardHeader>
                      <CardContent className="pb-4">
                        <p className="text-xs text-white/70">
                          Track all your referral submissions and their current status
                        </p>
                      </CardContent>
                    </Card>

                    {/* Earnings Summary Card */}
                    <Card className="group overflow-hidden border border-white/5 bg-purple-300/5 hover:bg-purple-400/10 transition-all duration-300 cursor-pointer">
                      <div className="relative h-32 overflow-hidden">
                        <Image
                          src="/images/landing/open-jobs/open-jobs-5.webp"
                          alt="Earnings"
                          fill
                          className="object-cover transition-transform duration-500 group-hover:scale-105"
                        />
                        <div className="absolute inset-0 bg-gradient-to-t from-purple-900/80 to-transparent"></div>
                      </div>
                      <CardHeader className="pb-2">
                        <CardTitle className="text-sm flex items-center text-white">
                          <CreditCard className="w-4 h-4 text-pink-400 mr-2" />
                          Earnings Summary
                        </CardTitle>
                      </CardHeader>
                      <CardContent className="pb-4">
                        <p className="text-xs text-white/70">
                          View detailed breakdown of your earnings and payment history
                        </p>
                      </CardContent>
                    </Card>
                  </div>
                </div>

                {/* Your Referrals Section */}
                <div className="mb-8">
                  <h2 className="text-sm font-medium text-white/80 mb-4 flex items-center">
                    <FileText className="w-4 h-4 text-pink-400 mr-2" />
                    Your Referrals
                    <div className="h-px bg-gradient-to-r from-pink-500/20 to-transparent flex-grow ml-3"></div>
                  </h2>

                  {dashboardData.partner.referrals && dashboardData.partner.referrals.length > 0 ? (
                    <Card className="border-white/5 shadow-lg hover:shadow-xl hover:shadow-purple-900/10 backdrop-blur-xl transition-all duration-200">
                      <CardContent className="p-4">
                        {/* Referrals table will go here */}
                        <p className="text-white/60 text-sm">Referrals list coming soon...</p>
                      </CardContent>
                    </Card>
                  ) : (
                    <Card className="border-white/5 shadow-lg backdrop-blur-xl">
                      <CardContent className="p-16 text-center">
                        <div className="inline-flex items-center justify-center w-20 h-20 bg-purple-500/20 rounded-full mb-4">
                          <FileText className="w-10 h-10 text-purple-400" />
                        </div>
                        <h3 className="text-xl font-semibold text-white mb-2">No referrals yet</h3>
                        <p className="text-white/60 mb-6 max-w-md mx-auto">
                          Start sharing your referral link to earn commissions
                        </p>
                        <button
                          onClick={copyReferralLink}
                          className="inline-flex items-center px-6 py-3 bg-gradient-to-r from-purple-500 to-pink-500 text-white rounded-xl hover:from-purple-600 hover:to-pink-600 transition-all font-medium"
                        >
                          <Copy className="w-4 h-4 mr-2" />
                          Copy Referral Link
                        </button>
                      </CardContent>
                    </Card>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </AppLayout>
  );
}
