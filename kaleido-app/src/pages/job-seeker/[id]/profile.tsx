import Head from 'next/head';
import { useRouter } from 'next/router';
import Script from 'next/script';
import { useEffect, useState } from 'react';

import JobSeekerProfileWrapper from '@/components/JobSeeker/JobSeekerProfileWrapper';
import ColorfulSmokeyOrbLoader from '@/components/Layouts/ColourfulLoader';
import apiHelper from '@/lib/apiHelper';
import { JobSeekerProfile } from '@/types/jobSeeker';
import { DownloadProfileButton } from '@/components/JobSeeker/DownloadProfileButton';
import { ShareButton } from '@/components/shared/ShareButton';

// Import circuit breaker utilities for debugging
if (typeof window !== 'undefined') {
  import('@/utils/resetCircuitBreaker');
}

export async function getServerSideProps() {
  return {
    props: {}, // Pass empty props, we'll fetch data client-side
  };
}

interface ProfileResponse {
  record?: JobSeekerProfile & { resumeHtml?: string };
  data?: JobSeekerProfile & { resumeHtml?: string };
  resumeHtml?: string;
  firstName?: string;
  lastName?: string;
  [key: string]: any;
}

export default function JobSeekerProfilePage() {
  const router = useRouter();
  const { id } = router.query;
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [profileData, setProfileData] = useState<JobSeekerProfile | null>(null);
  const [resumeHtml, setResumeHtml] = useState<string | null>(null);

  useEffect(() => {
    const fetchProfile = async () => {
      if (!id) return;

      try {
        // Fetch job seeker profile by ID using the public endpoint
        const response: ProfileResponse = await apiHelper.get(`/job-seekers/public/profile/${id}`);

        // Handle different response structures
        let profileData = null;

        // Check if response has a 'record' property (wrapped response)
        if (response?.record) {
          profileData = response.record;
        }
        // Check if response has profile data directly
        else if (response?.firstName || response?.email) {
          profileData = response;
        }
        // Check if response has a 'data' property
        else if (response?.data) {
          profileData = response.data;
        }

        if (profileData) {
          setProfileData(profileData);
          // Also check for resumeHtml in the profile data
          const htmlContent =
            response?.resumeHtml ||
            response?.record?.resumeHtml ||
            response?.data?.resumeHtml ||
            (profileData as any)?.resumeHtml;
          if (htmlContent) {
            setResumeHtml(htmlContent);
          }
          setLoading(false);
          return;
        }

        // Check if response has resumeHtml (fallback to HTML view)
        if (response?.resumeHtml) {
          setResumeHtml(response.resumeHtml);
          setLoading(false);
          return;
        }

        // If no profile found
        setError(`Job seeker profile with ID ${id} not found.`);
        setLoading(false);
      } catch (error) {
        setError(error instanceof Error ? error.message : 'Failed to load profile');
        setLoading(false);
      }
    };

    fetchProfile();
  }, [id]);

  const handleClose = () => {
    // Close the browser window/tab
    window.close();

    // If window.close() doesn't work (due to browser restrictions),
    // try to go back in history or redirect to a default page
    if (!window.closed) {
      if (window.history.length > 1) {
        window.history.back();
      } else {
        // Redirect to a default page if can't close
        router.push('/');
      }
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 flex items-center justify-center p-4">
        <ColorfulSmokeyOrbLoader text="Loading profile..." />
      </div>
    );
  }

  if (error || (!profileData && !resumeHtml)) {
    return (
      <>
        <Head>
          <title>Profile Not Found</title>
        </Head>
        <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 flex items-center justify-center p-4">
          <div className="text-center">
            <h1 className="text-2xl font-bold mb-2 text-gray-800">Profile Not Found</h1>
            <p className="text-gray-600">
              {error || 'This job seeker profile could not be found.'}
            </p>
          </div>
        </div>
      </>
    );
  }

  // If we have component data, render the component-based view
  if (profileData) {
    return (
      <>
        <Head>
          <title>
            {profileData.firstName} {profileData.lastName} - Profile
          </title>
        </Head>

        <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 relative">
          {/* Action buttons - fixed position */}
          <div className="fixed top-4 right-4 z-30 flex items-center gap-2">
            <DownloadProfileButton profile={profileData} resumeHtml={resumeHtml} />

            <ShareButton
              title={`${profileData.firstName} ${profileData.lastName} - Profile`}
              text={`Check out ${profileData.firstName}'s professional profile`}
              url={window.location.href}
            />
          </div>

          {/* Profile wrapper with close functionality */}
          <div className="w-full h-screen profile-wrapper">
            <JobSeekerProfileWrapper profile={profileData} onClose={handleClose} />
          </div>
        </div>
      </>
    );
  }

  // Fallback to HTML view if only resumeHtml is available
  return (
    <>
      <Head>
        <title>Job Seeker Profile</title>
      </Head>
      <Script src="https://cdn.tailwindcss.com" strategy="afterInteractive" />

      <div className="min-h-screen bg-gradient-to-br from-gray-900 to-black relative">
        {/* Action buttons for HTML view */}
        <div className="fixed top-4 right-4 z-30 flex items-center gap-2">
          <DownloadProfileButton profile={null} resumeHtml={resumeHtml} />

          <button
            type="button"
            onClick={handleClose}
            className="bg-white rounded-full p-2 hover:bg-gray-200 transition-colors shadow-md"
            aria-label="Close"
          >
            ✕
          </button>
        </div>

        <div
          className="container mx-auto p-4 lg:p-8"
          dangerouslySetInnerHTML={{ __html: resumeHtml || '' }}
        />
      </div>
    </>
  );
}
