import { act, renderHook } from '@testing-library/react';
import { useComparisonJobsStore } from './comparisonJobsStore';
import { StatusJob } from '@/components/shared/GenericStatusManager/types';

describe('ComparisonJobsStore', () => {
  beforeEach(() => {
    // Reset store state before each test
    const { result } = renderHook(() => useComparisonJobsStore());
    act(() => {
      // Clear all jobs by removing them individually
      const allJobs = [...result.current.activeJobs, ...result.current.completedJobs];
      allJobs.forEach(job => result.current.removeJob(job.id));
    });
  });

  describe('Job Management', () => {
    it('should add a new comparison job', () => {
      const { result } = renderHook(() => useComparisonJobsStore());

      const newJob: StatusJob = {
        id: 'queue-job-123',
        jobId: 'comparison-123',
        status: 'queued',
        progress: 0,
        message: 'Comparison initiated',
        createdAt: new Date().toISOString(),
        metadata: {
          comparisonTitle: 'Comparing 3 candidates',
          candidateNames: ['<PERSON>', '<PERSON>', '<PERSON>'],
          candidateCount: 3,
          comparisonType: 'quick_overview',
        },
      };

      act(() => {
        result.current.addJob(newJob);
      });

      expect(result.current.activeJobs).toHaveLength(1);
      expect(result.current.activeJobs[0]).toEqual(newJob);
    });

    it('should update an existing job', () => {
      const { result } = renderHook(() => useComparisonJobsStore());

      const job: StatusJob = {
        id: 'queue-job-123',
        jobId: 'comparison-123',
        status: 'processing',
        progress: 0,
        message: 'Starting comparison',
        createdAt: new Date().toISOString(),
      };

      act(() => {
        result.current.addJob(job);
      });

      act(() => {
        result.current.updateJob('queue-job-123', {
          progress: 50,
          message: 'Analyzing candidates...',
        });
      });

      const updatedJob = result.current.getJobById('queue-job-123');
      expect(updatedJob?.progress).toBe(50);
      expect(updatedJob?.message).toBe('Analyzing candidates...');
      expect(updatedJob?.status).toBe('processing'); // Status unchanged
    });

    it('should remove a job', () => {
      const { result } = renderHook(() => useComparisonJobsStore());

      const job1: StatusJob = {
        id: 'job-1',
        jobId: 'comp-1',
        status: 'processing',
        progress: 50,
        createdAt: new Date().toISOString(),
      };

      const job2: StatusJob = {
        id: 'job-2',
        jobId: 'comp-2',
        status: 'queued',
        progress: 0,
        createdAt: new Date().toISOString(),
      };

      act(() => {
        result.current.addJob(job1);
        result.current.addJob(job2);
      });

      expect(result.current.activeJobs).toHaveLength(2);

      act(() => {
        result.current.removeJob('job-1');
      });

      expect(result.current.activeJobs).toHaveLength(1);
      expect(result.current.activeJobs[0].id).toBe('job-2');
    });
  });

  describe('Active Jobs', () => {
    it('should track active jobs correctly', () => {
      const { result } = renderHook(() => useComparisonJobsStore());

      const activeJob: StatusJob = {
        id: 'active-job',
        jobId: 'comp-1',
        status: 'processing',
        progress: 30,
        createdAt: new Date().toISOString(),
      };

      const completedJob: StatusJob = {
        id: 'completed-job',
        jobId: 'comp-2',
        status: 'completed',
        progress: 100,
        createdAt: new Date().toISOString(),
      };

      const failedJob: StatusJob = {
        id: 'failed-job',
        jobId: 'comp-3',
        status: 'failed',
        progress: 0,
        error: 'API Error',
        createdAt: new Date().toISOString(),
      };

      act(() => {
        // Add active job directly
        result.current.addJob(activeJob);
        
        // Add completed job as active first, then update to move it to completed
        result.current.addJob({ ...completedJob, status: 'processing' as const });
        result.current.updateJob(completedJob.id, { status: 'completed' as const, progress: 100 });
        
        // Add failed job as active first, then update to move it to completed
        result.current.addJob({ ...failedJob, status: 'processing' as const });
        result.current.updateJob(failedJob.id, { status: 'failed' as const, error: 'API Error' });
      });

      expect(result.current.activeJobs).toHaveLength(1);
      expect(result.current.activeJobs[0].id).toBe('active-job');
    });

    it('should update active jobs when status changes', () => {
      const { result } = renderHook(() => useComparisonJobsStore());

      const job: StatusJob = {
        id: 'job-1',
        jobId: 'comp-1',
        status: 'processing',
        progress: 50,
        createdAt: new Date().toISOString(),
      };

      act(() => {
        result.current.addJob(job);
      });

      expect(result.current.activeJobs).toHaveLength(1);

      // Complete the job
      act(() => {
        result.current.updateJob('job-1', {
          status: 'completed',
          progress: 100,
          result: { comparisonId: 'comp-1' },
        });
      });

      expect(result.current.activeJobs).toHaveLength(0);
      expect(result.current.completedJobs).toHaveLength(1);
      expect(result.current.completedJobs[0].status).toBe('completed');
    });
  });

  describe('Completed Jobs Management', () => {
    it('should clear only completed jobs', () => {
      const { result } = renderHook(() => useComparisonJobsStore());

      const jobs: StatusJob[] = [
        {
          id: 'job-1',
          jobId: 'comp-1',
          status: 'completed',
          progress: 100,
          createdAt: new Date().toISOString(),
        },
        {
          id: 'job-2',
          jobId: 'comp-2',
          status: 'processing',
          progress: 50,
          createdAt: new Date().toISOString(),
        },
        {
          id: 'job-3',
          jobId: 'comp-3',
          status: 'completed',
          progress: 100,
          createdAt: new Date().toISOString(),
        },
        {
          id: 'job-4',
          jobId: 'comp-4',
          status: 'failed',
          progress: 0,
          createdAt: new Date().toISOString(),
        },
      ];

      act(() => {
        jobs.forEach(job => {
          if (job.status === 'completed' || job.status === 'failed') {
            // Add as processing first, then update to final status
            result.current.addJob({ ...job, status: 'processing' as const });
            result.current.updateJob(job.id, { status: job.status, progress: job.progress });
          } else {
            result.current.addJob(job);
          }
        });
      });

      const totalJobs = result.current.activeJobs.length + result.current.completedJobs.length;
      expect(totalJobs).toBe(4);

      act(() => {
        result.current.clearCompletedJobs();
      });

      // After clearCompletedJobs, should have 1 active job and 0 completed jobs
      expect(result.current.activeJobs).toHaveLength(1);
      expect(result.current.completedJobs).toHaveLength(0);
      expect(result.current.activeJobs[0].id).toBe('job-2');
    });
  });

  describe('Job Retrieval', () => {
    it('should get job by ID', () => {
      const { result } = renderHook(() => useComparisonJobsStore());

      const job: StatusJob = {
        id: 'queue-job-123',
        jobId: 'comparison-123',
        status: 'processing',
        progress: 75,
        createdAt: new Date().toISOString(),
      };

      act(() => {
        result.current.addJob(job);
      });

      const retrievedJob = result.current.getJobById('queue-job-123');
      expect(retrievedJob).toEqual(job);

      const nonExistentJob = result.current.getJobById('non-existent');
      expect(nonExistentJob).toBeUndefined();
    });

    it('should get jobs by comparison ID', () => {
      const { result } = renderHook(() => useComparisonJobsStore());

      const jobs: StatusJob[] = [
        {
          id: 'queue-1',
          jobId: 'comp-123',
          status: 'completed',
          progress: 100,
          createdAt: new Date('2024-01-01').toISOString(),
        },
        {
          id: 'queue-2',
          jobId: 'comp-456',
          status: 'processing',
          progress: 50,
          createdAt: new Date('2024-01-02').toISOString(),
        },
        {
          id: 'queue-3',
          jobId: 'comp-123',
          status: 'failed',
          progress: 0,
          createdAt: new Date('2024-01-03').toISOString(),
        },
      ];

      act(() => {
        jobs.forEach(job => {
          if (job.status === 'completed' || job.status === 'failed') {
            // Add as processing first, then update to final status
            result.current.addJob({ ...job, status: 'processing' as const });
            result.current.updateJob(job.id, { status: job.status, progress: job.progress });
          } else {
            result.current.addJob(job);
          }
        });
      });

      const comparisonJobs = result.current.getJobsByComparisonId('comp-123');
      expect(comparisonJobs).toHaveLength(2);
      expect(comparisonJobs[0].jobId).toBe('comp-123');
      expect(comparisonJobs[1].jobId).toBe('comp-123');
    });
  });

  describe('Store Persistence', () => {
    it('should maintain state across multiple hook instances', () => {
      const { result: result1 } = renderHook(() => useComparisonJobsStore());
      const { result: result2 } = renderHook(() => useComparisonJobsStore());

      const job: StatusJob = {
        id: 'shared-job',
        jobId: 'comp-shared',
        status: 'processing',
        progress: 60,
        createdAt: new Date().toISOString(),
      };

      act(() => {
        result1.current.addJob(job);
      });

      // Both instances should see the same state
      expect(result1.current.activeJobs).toHaveLength(1);
      expect(result2.current.activeJobs).toHaveLength(1);
      expect(result2.current.activeJobs[0]).toEqual(job);
    });
  });
});