import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { StatusJob } from '@/components/shared/GenericStatusManager/types';

interface ComparisonJobsState {
  activeJobs: StatusJob[];
  completedJobs: StatusJob[];
  addJob: (job: StatusJob) => void;
  updateJob: (jobId: string, updates: Partial<StatusJob>) => void;
  removeJob: (jobId: string) => void;
  clearCompletedJobs: () => void;
  getJobById: (jobId: string) => StatusJob | undefined;
  getJobsByComparisonId: (comparisonId: string) => StatusJob[];
  moveToCompleted: (jobId: string) => void;
}

export const useComparisonJobsStore = create<ComparisonJobsState>()(
  persist(
    (set, get) => ({
      activeJobs: [],
      completedJobs: [],

      addJob: (job) =>
        set((state) => ({
          activeJobs: [...state.activeJobs, job],
        })),

      updateJob: (jobId, updates) =>
        set((state) => {
          const activeIndex = state.activeJobs.findIndex((j) => j.id === jobId);
          const completedIndex = state.completedJobs.findIndex((j) => j.id === jobId);

          if (activeIndex !== -1) {
            const newActiveJobs = [...state.activeJobs];
            newActiveJobs[activeIndex] = { ...newActiveJobs[activeIndex], ...updates };
            
            // Move to completed if status is completed or failed
            if (updates.status === 'completed' || updates.status === 'failed') {
              const job = newActiveJobs[activeIndex];
              newActiveJobs.splice(activeIndex, 1);
              return {
                activeJobs: newActiveJobs,
                completedJobs: [job, ...state.completedJobs],
              };
            }
            
            return { activeJobs: newActiveJobs };
          }

          if (completedIndex !== -1) {
            const newCompletedJobs = [...state.completedJobs];
            newCompletedJobs[completedIndex] = { ...newCompletedJobs[completedIndex], ...updates };
            return { completedJobs: newCompletedJobs };
          }

          return state;
        }),

      removeJob: (jobId) =>
        set((state) => ({
          activeJobs: state.activeJobs.filter((j) => j.id !== jobId),
          completedJobs: state.completedJobs.filter((j) => j.id !== jobId),
        })),

      clearCompletedJobs: () =>
        set(() => ({
          completedJobs: [],
        })),

      getJobById: (jobId) => {
        const state = get();
        return (
          state.activeJobs.find((j) => j.id === jobId) ||
          state.completedJobs.find((j) => j.id === jobId)
        );
      },

      getJobsByComparisonId: (comparisonId) => {
        const state = get();
        const allJobs = [...state.activeJobs, ...state.completedJobs];
        return allJobs.filter((job) => job.jobId === comparisonId);
      },

      moveToCompleted: (jobId) =>
        set((state) => {
          const jobIndex = state.activeJobs.findIndex((j) => j.id === jobId);
          if (jobIndex !== -1) {
            const job = state.activeJobs[jobIndex];
            const newActiveJobs = [...state.activeJobs];
            newActiveJobs.splice(jobIndex, 1);
            return {
              activeJobs: newActiveJobs,
              completedJobs: [job, ...state.completedJobs],
            };
          }
          return state;
        }),
    }),
    {
      name: 'comparison-jobs-storage',
      partialize: (state) => ({
        activeJobs: state.activeJobs,
        completedJobs: state.completedJobs.slice(0, 20), // Keep only last 20 completed jobs
      }),
    }
  )
);