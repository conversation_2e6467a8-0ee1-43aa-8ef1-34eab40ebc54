'use client';

import React, { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { 
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { DollarSign, TrendingUp, Clock } from 'lucide-react';
import { ReferralPartner } from '../types';
import { referralApi } from '../services/referralApi';
import { showToast } from '@/components/CustomToaster';

interface EarningsSectionProps {
  partner: ReferralPartner;
  onRefresh?: () => void;
}

export const EarningsSection: React.FC<EarningsSectionProps> = ({ partner, onRefresh }) => {
  const [isPayoutOpen, setIsPayoutOpen] = useState(false);
  const [payoutAmount, setPayoutAmount] = useState('');
  const [isLoading, setIsLoading] = useState(false);

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(amount);
  };

  const handlePayoutRequest = async () => {
    const amount = parseFloat(payoutAmount);
    
    if (isNaN(amount) || amount <= 0) {
      showToast({ message: 'Please enter a valid amount', type: 'error' });
      return;
    }

    if (amount > partner.pendingEarnings) {
      showToast({ message: 'Amount exceeds pending earnings', type: 'error' });
      return;
    }

    setIsLoading(true);
    try {
      await referralApi.requestPayment(partner.id, amount);
      showToast({ message: 'Payment request submitted successfully', type: 'success' });
      setIsPayoutOpen(false);
      setPayoutAmount('');
      onRefresh?.();
    } catch (error) {
      showToast({ message: 'Failed to submit payment request', type: 'error' });
      console.error(error);
    } finally {
      setIsLoading(false);
    }
  };

  const earnings = [
    {
      title: 'Total Earnings',
      amount: partner.totalEarnings,
      icon: DollarSign,
      description: 'All time earnings from referrals',
      color: 'text-green-600',
    },
    {
      title: 'Pending Earnings',
      amount: partner.pendingEarnings,
      icon: Clock,
      description: 'Awaiting payment approval',
      color: 'text-yellow-600',
    },
    {
      title: 'Paid Out',
      amount: partner.paidEarnings,
      icon: TrendingUp,
      description: 'Successfully paid earnings',
      color: 'text-blue-600',
    },
  ];

  return (
    <div className="space-y-6">
      <div className="grid gap-4 md:grid-cols-3">
        {earnings.map((item, index) => (
          <Card key={index}>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">{item.title}</CardTitle>
              <item.icon className={`h-4 w-4 ${item.color}`} />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{formatCurrency(item.amount)}</div>
              <p className="text-xs text-muted-foreground mt-1">{item.description}</p>
            </CardContent>
          </Card>
        ))}
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Payment Settings</CardTitle>
          <CardDescription>
            Manage your payment preferences and request payouts
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="font-medium">Payment Method</p>
              <p className="text-sm text-muted-foreground">
                {partner.settings?.paymentPreferences?.method || 'Not configured'}
              </p>
            </div>
            <Button variant="outline" size="sm">
              Configure
            </Button>
          </div>

          <div className="flex items-center justify-between pt-4 border-t">
            <div>
              <p className="font-medium">Available for Payout</p>
              <p className="text-2xl font-bold text-green-600">
                {formatCurrency(partner.pendingEarnings)}
              </p>
            </div>
            
            <Dialog open={isPayoutOpen} onOpenChange={setIsPayoutOpen}>
              <DialogTrigger asChild>
                <Button 
                  disabled={partner.pendingEarnings <= 0}
                  size="lg"
                >
                  Request Payout
                </Button>
              </DialogTrigger>
              <DialogContent>
                <DialogHeader>
                  <DialogTitle>Request Payout</DialogTitle>
                  <DialogDescription>
                    Enter the amount you'd like to withdraw. Available balance: {formatCurrency(partner.pendingEarnings)}
                  </DialogDescription>
                </DialogHeader>
                <div className="grid gap-4 py-4">
                  <div className="grid gap-2">
                    <Label htmlFor="amount">Amount</Label>
                    <Input
                      id="amount"
                      type="number"
                      step="0.01"
                      placeholder="0.00"
                      value={payoutAmount}
                      onChange={(e) => setPayoutAmount(e.target.value)}
                      max={partner.pendingEarnings}
                    />
                  </div>
                </div>
                <DialogFooter>
                  <Button variant="outline" onClick={() => setIsPayoutOpen(false)}>
                    Cancel
                  </Button>
                  <Button onClick={handlePayoutRequest} disabled={isLoading}>
                    {isLoading ? 'Processing...' : 'Submit Request'}
                  </Button>
                </DialogFooter>
              </DialogContent>
            </Dialog>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};