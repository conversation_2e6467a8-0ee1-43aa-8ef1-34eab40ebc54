'use client';

import React, { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Copy, Link, Share2, CheckCircle } from 'lucide-react';
import { ReferralPartner } from '../types';
import { showToast } from '@/components/CustomToaster';

interface ReferralToolsProps {
  partner: ReferralPartner;
}

export const ReferralTools: React.FC<ReferralToolsProps> = ({ partner }) => {
  const [selectedJobId, setSelectedJobId] = useState('');
  const [copied, setCopied] = useState(false);

  const baseUrl = typeof window !== 'undefined' ? window.location.origin : '';

  const generateReferralLink = (jobId?: string) => {
    let link = `${baseUrl}/api/r/${partner.referralCode}`;
    if (jobId) {
      link += `?job=${jobId}`;
    }
    return link;
  };

  const copyToClipboard = async (text: string) => {
    try {
      await navigator.clipboard.writeText(text);
      setCopied(true);
      showToast({ message: 'Link copied to clipboard!', type: 'success' });
      setTimeout(() => setCopied(false), 2000);
    } catch (err) {
      showToast({ message: 'Failed to copy link', type: 'error' });
    }
  };

  const shareLink = async (link: string) => {
    if (navigator.share) {
      try {
        await navigator.share({
          title: 'Check out this job opportunity',
          text: 'I thought you might be interested in this position',
          url: link,
        });
      } catch (err) {
        console.error('Error sharing:', err);
      }
    } else {
      copyToClipboard(link);
    }
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Your Referral Code</CardTitle>
          <CardDescription>
            Share your unique referral code with candidates
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex items-center space-x-2">
            <div className="flex-1">
              <Input
                value={partner.referralCode}
                readOnly
                className="font-mono text-lg"
              />
            </div>
            <Button
              variant="outline"
              size="icon"
              onClick={() => copyToClipboard(partner.referralCode)}
            >
              {copied ? <CheckCircle className="h-4 w-4" /> : <Copy className="h-4 w-4" />}
            </Button>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Referral Link Generator</CardTitle>
          <CardDescription>
            Create custom referral links for specific jobs or general applications
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="job-select">Select a Job (Optional)</Label>
            <Select value={selectedJobId} onValueChange={setSelectedJobId}>
              <SelectTrigger id="job-select">
                <SelectValue placeholder="All Jobs" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="">All Jobs</SelectItem>
                {/* TODO: Load actual jobs from API */}
                <SelectItem value="job1">Software Engineer</SelectItem>
                <SelectItem value="job2">Product Manager</SelectItem>
                <SelectItem value="job3">Data Scientist</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <Label>Generated Link</Label>
            <div className="flex items-center space-x-2">
              <Input
                value={generateReferralLink(selectedJobId)}
                readOnly
                className="font-mono text-sm"
              />
              <Button
                variant="outline"
                size="icon"
                onClick={() => copyToClipboard(generateReferralLink(selectedJobId))}
              >
                <Copy className="h-4 w-4" />
              </Button>
              <Button
                variant="outline"
                size="icon"
                onClick={() => shareLink(generateReferralLink(selectedJobId))}
              >
                <Share2 className="h-4 w-4" />
              </Button>
            </div>
          </div>

          <div className="pt-4 space-y-2">
            <h4 className="font-medium text-sm">Quick Share Links</h4>
            <div className="grid gap-2">
              <div className="flex items-center justify-between p-3 border rounded-lg">
                <div className="flex items-center space-x-2">
                  <Link className="h-4 w-4 text-muted-foreground" />
                  <span className="text-sm">General Referral Link</span>
                </div>
                <div className="flex space-x-2">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => copyToClipboard(generateReferralLink())}
                  >
                    Copy
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => shareLink(generateReferralLink())}
                  >
                    Share
                  </Button>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Marketing Materials</CardTitle>
          <CardDescription>
            Download templates and resources to help promote job opportunities
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4 md:grid-cols-2">
            <Button variant="outline" className="justify-start">
              <Link className="mr-2 h-4 w-4" />
              Email Templates
            </Button>
            <Button variant="outline" className="justify-start">
              <Link className="mr-2 h-4 w-4" />
              Social Media Posts
            </Button>
            <Button variant="outline" className="justify-start">
              <Link className="mr-2 h-4 w-4" />
              Banner Images
            </Button>
            <Button variant="outline" className="justify-start">
              <Link className="mr-2 h-4 w-4" />
              Best Practices Guide
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};