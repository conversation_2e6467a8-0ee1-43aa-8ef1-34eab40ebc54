'use client';

import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { useUser } from '@auth0/nextjs-auth0/client';
import { motion } from 'framer-motion';
import {
  AlertCircle,
  Building2,
  Check,
  FileText,
  Globe,
  Loader2,
  Mail,
  Phone,
  Sparkles,
  User,
} from 'lucide-react';
import Image from 'next/image';
import { useRouter } from 'next/navigation';
import React, { useEffect, useState } from 'react';

import apiHelper from '@/lib/apiHelper';

interface ReferralPartnerFormData {
  email: string;
  organizationName: string;
  contactName: string;
  phoneNumber: string;
  website?: string;
  description?: string;
}

export default function ReferralPartnerOnboarding() {
  const { user, isLoading: userLoading } = useUser();
  const router = useRouter();
  const [loading, setLoading] = useState(false);
  const [checking, setChecking] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);
  const [formData, setFormData] = useState<ReferralPartnerFormData>({
    email: '',
    organizationName: '',
    contactName: '',
    phoneNumber: '',
    website: '',
    description: '',
  });

  useEffect(() => {
    if (user && !userLoading) {
      checkExistingPartner();
      // Pre-fill email from Auth0 user
      setFormData(prev => ({
        ...prev,
        email: user.email || '',
        contactName: user.name || '',
      }));
    }
  }, [user, userLoading]);

  const checkExistingPartner = async () => {
    try {
      setChecking(true);
      const response = await apiHelper.post(
        '/referral-partners/public/check',
        {},
        {
          headers: {
            'x-client-id': user?.sub || '',
          },
        }
      );

      if (response.data.exists) {
        // User is already a referral partner, redirect to dashboard
        router.push('/referral-partner');
      }
    } catch (error) {
      console.error('Error checking existing partner:', error);
    } finally {
      setChecking(false);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError(null);

    try {
      // Create referral partner profile (this will also update the user role)
      await apiHelper.post(
        '/referral-partners/public/register',
        {
          ...formData,
          clientId: user?.sub,
        },
        {
          headers: {
            'x-client-id': user?.sub || '',
          },
        }
      );

      setSuccess(true);

      // Redirect to dashboard after 2 seconds
      setTimeout(() => {
        router.push('/referral-partner');
      }, 2000);
    } catch (error: any) {
      console.error('Error creating referral partner:', error);
      setError(error.response?.data?.message || 'Failed to create referral partner profile');
    } finally {
      setLoading(false);
    }
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    setFormData(prev => ({
      ...prev,
      [e.target.name]: e.target.value,
    }));
  };

  if (userLoading || checking) {
    return (
      <div className="min-h-screen relative overflow-hidden">
        {/* Background Image */}
        <div className="absolute inset-0 z-0">
          <Image
            src="/images/landing/exclusive-2.webp"
            alt="Background"
            fill
            className="object-cover"
            priority
            quality={100}
          />
          <div
            className="absolute inset-0"
            style={{
              background:
                'linear-gradient(to top, rgba(88, 28, 135, 0.7) 0%, rgba(88, 28, 135, 0.5) 15%, rgba(67, 56, 202, 0.4) 30%, rgba(67, 56, 202, 0.3) 45%, rgba(49, 46, 129, 0.2) 60%, rgba(49, 46, 129, 0.15) 75%, rgba(30, 27, 75, 0.1) 90%, rgba(30, 27, 75, 0.05) 100%)',
            }}
          />
        </div>
        <div className="relative z-10 flex items-center justify-center min-h-screen">
          <div className="text-center">
            <Loader2 className="h-8 w-8 animate-spin text-white mx-auto mb-4" />
            <p className="text-white">Loading...</p>
          </div>
        </div>
      </div>
    );
  }

  if (!user) {
    return (
      <div className="min-h-screen relative overflow-hidden">
        {/* Background Image */}
        <div className="absolute inset-0 z-0">
          <Image
            src="/images/landing/exclusive-2.webp"
            alt="Background"
            fill
            className="object-cover"
            priority
            quality={100}
          />
          <div
            className="absolute inset-0"
            style={{
              background:
                'linear-gradient(to top, rgba(88, 28, 135, 0.7) 0%, rgba(88, 28, 135, 0.5) 15%, rgba(67, 56, 202, 0.4) 30%, rgba(67, 56, 202, 0.3) 45%, rgba(49, 46, 129, 0.2) 60%, rgba(49, 46, 129, 0.15) 75%, rgba(30, 27, 75, 0.1) 90%, rgba(30, 27, 75, 0.05) 100%)',
            }}
          />
        </div>
        <div className="relative z-10 flex items-center justify-center min-h-screen">
          <div className="text-center bg-white/10 backdrop-blur-md rounded-2xl p-8 border border-white/20">
            <AlertCircle className="h-12 w-12 text-yellow-400 mx-auto mb-4" />
            <h2 className="text-2xl font-semibold text-white mb-2">Authentication Required</h2>
            <p className="text-white/80 mb-6">
              Please log in to continue with your referral partner registration.
            </p>
            <Button
              onClick={() => router.push('/api/auth/login?returnTo=/referral-partner/onboarding')}
              className="bg-white text-purple-700 hover:bg-gray-100"
            >
              Log In
            </Button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen relative overflow-hidden">
      {/* Background Image */}
      <div className="absolute inset-0 z-0">
        <Image
          src="/images/landing/exclusive-2.webp"
          alt="Background"
          fill
          className="object-cover"
          priority
          quality={100}
        />
        {/* Gradient overlay */}
        <div
          className="absolute inset-0"
          style={{
            background:
              'linear-gradient(to top, rgba(88, 28, 135, 0.7) 0%, rgba(88, 28, 135, 0.5) 15%, rgba(67, 56, 202, 0.4) 30%, rgba(67, 56, 202, 0.3) 45%, rgba(49, 46, 129, 0.2) 60%, rgba(49, 46, 129, 0.15) 75%, rgba(30, 27, 75, 0.1) 90%, rgba(30, 27, 75, 0.05) 100%)',
          }}
        />
      </div>

      {/* Content */}
      <div className="relative z-10 py-12 px-4">
        <div className="max-w-2xl mx-auto">
          {/* Logo */}
          <div className="text-center mb-8">
            <Image
              src="/images/logos/kaleido-logo-only.webp"
              alt="Kaleido Talent"
              width={80}
              height={80}
              className="mx-auto mb-4"
            />
            <h1 className="text-3xl font-bold text-white">Become a Referral Partner</h1>
            <p className="text-white/80 mt-2">Complete your profile to start earning commissions</p>
          </div>

          {success ? (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              className="bg-white/10 backdrop-blur-lg rounded-2xl shadow-2xl p-8 text-center border border-white/20"
            >
              <motion.div
                initial={{ scale: 0 }}
                animate={{ scale: 1 }}
                transition={{ delay: 0.2, type: 'spring' }}
                className="mx-auto flex items-center justify-center h-20 w-20 rounded-full bg-gradient-to-br from-green-400 to-emerald-600 mb-6"
              >
                <Check className="h-10 w-10 text-white" />
              </motion.div>
              <h2 className="text-3xl font-bold text-white mb-3">Welcome to the Team!</h2>
              <p className="text-white/80 mb-4 text-lg">
                Your referral partner account has been created successfully.
              </p>
              <p className="text-white/60">Redirecting to your dashboard...</p>
            </motion.div>
          ) : (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              className="bg-black/20 backdrop-blur-xl rounded-2xl shadow-2xl p-10 border border-white/20"
            >
              <div className="flex items-center mb-8">
                <motion.div
                  initial={{ rotate: -180, opacity: 0 }}
                  animate={{ rotate: 0, opacity: 1 }}
                  transition={{ duration: 0.5 }}
                  className="bg-gradient-to-br from-purple-500 to-pink-600 p-4 rounded-xl shadow-lg"
                >
                  <Sparkles className="h-8 w-8 text-white" />
                </motion.div>
                <div className="ml-4">
                  <h2 className="text-2xl font-bold text-white">Partner Information</h2>
                  <p className="text-white/60 text-sm mt-1">Fill in your details to get started</p>
                </div>
              </div>

              {error && (
                <motion.div
                  initial={{ opacity: 0, scale: 0.95 }}
                  animate={{ opacity: 1, scale: 1 }}
                  className="mb-6 bg-red-500/20 backdrop-blur-sm border border-red-500/30 rounded-xl p-4"
                >
                  <div className="flex items-start gap-3">
                    <AlertCircle className="h-5 w-5 text-red-400 flex-shrink-0 mt-0.5" />
                    <p className="text-red-100 text-sm">{error}</p>
                  </div>
                </motion.div>
              )}

              <form onSubmit={handleSubmit} className="space-y-8">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <motion.div
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: 0.1 }}
                    className="space-y-2"
                  >
                    <Label
                      htmlFor="email"
                      className="text-white/90 font-medium flex items-center gap-2"
                    >
                      <Mail className="h-4 w-4" />
                      Email Address
                    </Label>
                    <div className="relative">
                      <Input
                        id="email"
                        name="email"
                        type="email"
                        value={formData.email}
                        onChange={handleChange}
                        required
                        disabled
                        className="bg-white/10 border-white/20 text-white placeholder-white/40 focus:border-purple-400 focus:ring-purple-400 pr-10"
                      />
                      <Mail className="absolute right-3 top-1/2 -translate-y-1/2 h-5 w-5 text-white/40" />
                    </div>
                  </motion.div>

                  <motion.div
                    initial={{ opacity: 0, x: 20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: 0.2 }}
                    className="space-y-2"
                  >
                    <Label
                      htmlFor="contactName"
                      className="text-white/90 font-medium flex items-center gap-2"
                    >
                      <User className="h-4 w-4" />
                      Contact Name
                    </Label>
                    <div className="relative">
                      <Input
                        id="contactName"
                        name="contactName"
                        type="text"
                        value={formData.contactName}
                        onChange={handleChange}
                        required
                        className="bg-white/10 border-white/20 text-white placeholder-white/40 focus:border-purple-400 focus:ring-purple-400 pr-10"
                        placeholder="John Doe"
                      />
                      <User className="absolute right-3 top-1/2 -translate-y-1/2 h-5 w-5 text-white/40" />
                    </div>
                  </motion.div>

                  <motion.div
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: 0.3 }}
                    className="space-y-2"
                  >
                    <Label
                      htmlFor="organizationName"
                      className="text-white/90 font-medium flex items-center gap-2"
                    >
                      <Building2 className="h-4 w-4" />
                      Organization Name
                    </Label>
                    <div className="relative">
                      <Input
                        id="organizationName"
                        name="organizationName"
                        type="text"
                        value={formData.organizationName}
                        onChange={handleChange}
                        required
                        className="bg-white/10 border-white/20 text-white placeholder-white/40 focus:border-purple-400 focus:ring-purple-400 pr-10"
                        placeholder="Your company or organization"
                      />
                      <Building2 className="absolute right-3 top-1/2 -translate-y-1/2 h-5 w-5 text-white/40" />
                    </div>
                  </motion.div>

                  <motion.div
                    initial={{ opacity: 0, x: 20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: 0.4 }}
                    className="space-y-2"
                  >
                    <Label
                      htmlFor="phoneNumber"
                      className="text-white/90 font-medium flex items-center gap-2"
                    >
                      <Phone className="h-4 w-4" />
                      Phone Number
                    </Label>
                    <div className="relative">
                      <Input
                        id="phoneNumber"
                        name="phoneNumber"
                        type="tel"
                        value={formData.phoneNumber}
                        onChange={handleChange}
                        required
                        className="bg-white/10 border-white/20 text-white placeholder-white/40 focus:border-purple-400 focus:ring-purple-400 pr-10"
                        placeholder="+****************"
                      />
                      <Phone className="absolute right-3 top-1/2 -translate-y-1/2 h-5 w-5 text-white/40" />
                    </div>
                  </motion.div>

                  <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.5 }}
                    className="md:col-span-2 space-y-2"
                  >
                    <Label
                      htmlFor="website"
                      className="text-white/90 font-medium flex items-center gap-2"
                    >
                      <Globe className="h-4 w-4" />
                      Website (Optional)
                    </Label>
                    <div className="relative">
                      <Input
                        id="website"
                        name="website"
                        type="url"
                        value={formData.website}
                        onChange={handleChange}
                        className="bg-white/10 border-white/20 text-white placeholder-white/40 focus:border-purple-400 focus:ring-purple-400 pr-10"
                        placeholder="https://www.example.com"
                      />
                      <Globe className="absolute right-3 top-1/2 -translate-y-1/2 h-5 w-5 text-white/40" />
                    </div>
                  </motion.div>

                  <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.6 }}
                    className="md:col-span-2 space-y-2"
                  >
                    <Label
                      htmlFor="description"
                      className="text-white/90 font-medium flex items-center gap-2"
                    >
                      <FileText className="h-4 w-4" />
                      Description (Optional)
                    </Label>
                    <div className="relative">
                      <textarea
                        id="description"
                        name="description"
                        value={formData.description}
                        onChange={handleChange}
                        rows={4}
                        className="w-full rounded-md bg-white/10 border border-white/20 px-4 py-3 text-white placeholder-white/40 focus:border-purple-400 focus:outline-none focus:ring-1 focus:ring-purple-400"
                        placeholder="Tell us about your organization and how you plan to refer candidates..."
                      />
                      <FileText className="absolute right-3 top-3 h-5 w-5 text-white/40" />
                    </div>
                  </motion.div>
                </div>

                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.7 }}
                  className="pt-6 space-y-4"
                >
                  <div className="bg-gradient-to-r from-purple-600/20 to-pink-600/20 rounded-xl p-4 border border-white/10">
                    <p className="text-white/80 text-sm flex items-start gap-2">
                      <Sparkles className="h-4 w-4 text-yellow-400 flex-shrink-0 mt-0.5" />
                      <span>
                        By becoming a partner, you'll unlock exclusive benefits and earn competitive
                        commissions for successful referrals.
                      </span>
                    </p>
                  </div>

                  <Button
                    type="submit"
                    disabled={loading}
                    className="w-full bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white font-semibold py-6 text-lg shadow-xl hover:shadow-2xl transition-all duration-300"
                  >
                    {loading ? (
                      <>
                        <Loader2 className="mr-2 h-5 w-5 animate-spin" />
                        Creating Your Partner Account...
                      </>
                    ) : (
                      <>
                        Complete Registration
                        <Sparkles className="ml-2 h-5 w-5" />
                      </>
                    )}
                  </Button>
                </motion.div>
              </form>
            </motion.div>
          )}
        </div>
      </div>
    </div>
  );
}
