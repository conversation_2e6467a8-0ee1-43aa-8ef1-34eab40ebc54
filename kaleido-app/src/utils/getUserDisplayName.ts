/**
 * Utility function to get a consistent display name for a user
 * Handles various Auth0 user object structures and provides fallbacks
 * Always returns the name in Title Case
 */
export const getUserDisplayName = (user: any): string => {
  if (!user) return 'Guest';

  let displayName = '';

  // Priority order for display name:
  // 1. given_name (if it exists and is not an email)
  // 2. nickname (if it's not an email)
  // 3. name split at @ (if it's an email)
  // 4. first part of email
  // 5. fallback to 'User'

  // Check for given_name first (some Auth0 setups might have this)
  if (user.given_name && !user.given_name.includes('@')) {
    displayName = user.given_name;
  }
  // Check for family_name combined with given_name
  else if (user.given_name && user.family_name) {
    displayName = `${user.given_name} ${user.family_name}`.trim();
  }
  // Use nickname if it's not an email
  else if (user.nickname && !user.nickname.includes('@')) {
    displayName = user.nickname;
  }
  // If name exists and is an email, extract the part before @
  else if (user.name && user.name.includes('@')) {
    displayName = user.name.split('@')[0];
  }
  // If name exists but is not an email, use it
  else if (user.name && !user.name.includes('@')) {
    displayName = user.name;
  }
  // Try email as last resort
  else if (user.email && user.email.includes('@')) {
    displayName = user.email.split('@')[0];
  }
  // Final fallback
  else {
    displayName = 'User';
  }

  // Apply title case formatting before returning
  return toTitleCase(displayName);
};

/**
 * Converts a name to Title Case
 * Example: "john doe" -> "John Doe"
 * Handles special cases like hyphens and apostrophes
 */
export const toTitleCase = (name: string): string => {
  if (!name) return '';
  
  // Handle various separators (spaces, hyphens, apostrophes)
  return name
    .split(/(\s|-|')/g) // Split on spaces, hyphens, or apostrophes but keep the separators
    .map(part => {
      // Handle edge cases like all caps or mixed case
      if (part.length === 0) return '';
      // Don't capitalize separators
      if (part === ' ' || part === '-' || part === "'") return part;
      // Special handling for common name particles
      const lowerCaseWords = ['de', 'van', 'der', 'la', 'le', 'von'];
      if (lowerCaseWords.includes(part.toLowerCase())) {
        return part.toLowerCase();
      }
      // Capitalize first letter, lowercase the rest
      return part.charAt(0).toUpperCase() + part.slice(1).toLowerCase();
    })
    .join('');
};

/**
 * Get formatted display name (in Title Case)
 */
export const getFormattedUserDisplayName = (user: any): string => {
  const displayName = getUserDisplayName(user);
  return toTitleCase(displayName);
};