import axios, { AxiosError, AxiosInstance, AxiosRequestConfig } from 'axios';

import { ApiError, isApiError } from '@/lib/ApiError';
import { authService } from '@/services/auth.service';
import { parseSubscriptionError, showSubscriptionLimitModal } from '@/utils/subscriptionUtils';

import {
  API_CALL_TRACKERS,
  BACKEND_API_URL,
  cleanupAbortController,
  createAbortController,
  debounceApiCall,
  getOnboardingContext,
  isCancellableEndpoint,
  isPublicEndpoint,
  isWorkerTaskEndpoint,
  pendingRequests,
  trackApiCall,
} from './apiHelper.utils';

// Track failed auth attempts per endpoint to prevent infinite loops
const AUTH_RETRY_TRACKER: Record<string, { count: number; lastAttempt: number }> = {};
const MAX_AUTH_RETRIES = 4; // Maximum retries before giving up
const RETRY_WINDOW_MS = 60000; // Reset retry count after 1 minute

// Track rapid API calls from the same source (potential infinite loops)
const RAPID_CALL_TRACKER: Record<string, { timestamps: number[]; warningShown: boolean }> = {};
const RAPID_CALL_THRESHOLD = 10; // Number of calls to trigger warning
const RAPID_CALL_WINDOW_MS = 5000; // 5 second window

// Function to show error modal - will be initialized later
let showErrorModalFn: (options?: { message?: string; title?: string }) => void = () => {};

// Function to show auth modal - will be initialized later
let showAuthModalFn: (options?: {
  message?: string;
  title?: string;
  autoLogout?: boolean;
}) => void = () => {};

// Function to show credit purchase modal - will be initialized later
let showCreditPurchaseModalFn: (options?: {
  actionType?: string;
  requiredCredits?: number;
  availableCredits?: number;
  message?: string;
}) => void = () => {};

// Function to initialize the error modal function
export const initializeErrorModal = (
  showModalFn: (options?: { message?: string; title?: string }) => void
) => {
  showErrorModalFn = showModalFn;
};

// Function to initialize the auth modal function
export const initializeAuthModal = (
  showModalFn: (options?: { message?: string; title?: string; autoLogout?: boolean }) => void
) => {
  showAuthModalFn = showModalFn;
};

// Function to initialize the credit purchase modal function
export const initializeCreditPurchaseModal = (
  showModalFn: (options?: {
    actionType?: string;
    requiredCredits?: number;
    availableCredits?: number;
    message?: string;
  }) => void
) => {
  showCreditPurchaseModalFn = showModalFn;
};

// Create axios instance with default config
const axiosInstance: AxiosInstance = axios.create({
  baseURL: BACKEND_API_URL,
  headers: {
    Accept: 'application/json',
  },
  withCredentials: true, // Important for HTTP-only cookies
});

// Industry standard: Request interceptor for automatic token management
axiosInstance.interceptors.request.use(
  async config => {
    // Skip token for public endpoints
    if (isPublicEndpoint(config.url || '')) {
      return config;
    }

    try {
      // Get valid access token (handles refresh automatically)
      const accessToken = await authService.getValidAccessToken();

      if (accessToken) {
        config.headers.Authorization = `Bearer ${accessToken}`;
      }
    } catch (error) {
      console.error('Failed to get access token:', error);
      // Continue without token - let backend handle auth error
    }

    // Don't set Content-Type for FormData (let browser set it with boundary)
    if (!(config.data instanceof FormData)) {
      config.headers['Content-Type'] = 'application/json';
    }

    return config;
  },
  error => {
    return Promise.reject(error);
  }
);

// Function to clear temporary user data
export const clearTempUserData = () => {
  if (typeof window !== 'undefined') {
    localStorage.removeItem('temp_user_id');
    localStorage.removeItem('temp_user_session');
  }
};

// Helper function to track auth retry attempts
const trackAuthRetry = (endpoint: string): boolean => {
  const now = Date.now();
  const tracker = AUTH_RETRY_TRACKER[endpoint];

  // Clean up old entries
  if (tracker && now - tracker.lastAttempt > RETRY_WINDOW_MS) {
    delete AUTH_RETRY_TRACKER[endpoint];
  }

  // Initialize or update tracker
  if (!AUTH_RETRY_TRACKER[endpoint]) {
    AUTH_RETRY_TRACKER[endpoint] = { count: 1, lastAttempt: now };
    return true; // Allow first attempt
  }

  // Check if we've exceeded max retries
  if (AUTH_RETRY_TRACKER[endpoint].count >= MAX_AUTH_RETRIES) {
    return false; // Deny further attempts
  }

  // Increment counter and allow
  AUTH_RETRY_TRACKER[endpoint].count++;
  AUTH_RETRY_TRACKER[endpoint].lastAttempt = now;
  return true;
};

// Helper function to reset auth retry tracker for an endpoint
const resetAuthRetryTracker = (endpoint?: string) => {
  if (endpoint) {
    delete AUTH_RETRY_TRACKER[endpoint];
  } else {
    // Clear all if no endpoint specified
    Object.keys(AUTH_RETRY_TRACKER).forEach(key => delete AUTH_RETRY_TRACKER[key]);
  }
};

// Helper function to detect rapid API calls (potential infinite loops)
const detectRapidApiCalls = (endpoint: string): boolean => {
  const now = Date.now();
  const key = endpoint.split('?')[0]; // Ignore query params

  // Initialize tracker if needed
  if (!RAPID_CALL_TRACKER[key]) {
    RAPID_CALL_TRACKER[key] = { timestamps: [], warningShown: false };
  }

  const tracker = RAPID_CALL_TRACKER[key];

  // Remove old timestamps outside the window
  tracker.timestamps = tracker.timestamps.filter(ts => now - ts < RAPID_CALL_WINDOW_MS);

  // Add current timestamp
  tracker.timestamps.push(now);

  // Check if we've exceeded the threshold
  if (tracker.timestamps.length >= RAPID_CALL_THRESHOLD) {
    const errorMessage =
      `⚠️ POTENTIAL INFINITE LOOP DETECTED! ⚠️\n` +
      `Endpoint: ${endpoint}\n` +
      `${tracker.timestamps.length} calls in ${RAPID_CALL_WINDOW_MS / 1000} seconds`;

    console.error(
      errorMessage +
        `\n\n` +
        `This is likely caused by:\n` +
        `1. useEffect with missing or incorrect dependencies\n` +
        `2. State updates triggering re-renders in a loop\n` +
        `3. Conditional logic that always evaluates to true\n` +
        `\n` +
        `Check your component's useEffect hooks!`
    );

    // Show error modal to user
    if (showErrorModalFn) {
      showErrorModalFn({
        title: 'Too Many Requests',
        message: `The application is making too many requests. Please refresh the page.`,
      });
    }

    // Clear the tracker to prevent further errors
    tracker.timestamps = [];

    // Throw an error to stop the API call
    const error = new Error(
      `Infinite loop detected: ${tracker.timestamps.length} calls to ${endpoint} in ${RAPID_CALL_WINDOW_MS / 1000}s`
    );
    error.name = 'InfiniteLoopError';
    throw error;
  }

  // Reset warning flag if calls slow down
  if (tracker.timestamps.length < RAPID_CALL_THRESHOLD / 2) {
    tracker.warningShown = false;
  }

  return false;
};

// Second interceptor for onboarding context only
axiosInstance.interceptors.request.use(async config => {
  // Handle onboarding context - send role and context info to backend
  const onboardingContext = getOnboardingContext();
  if (onboardingContext) {
    config.headers['X-Onboarding-Context'] = onboardingContext.context;
    config.headers['X-Onboarding-Role'] = onboardingContext.role;
  }

  return config;
});

// Helper function to mark the start of authentication process
export const markAuthInitializing = () => {
  if (typeof window !== 'undefined') {
    sessionStorage.setItem('auth_initializing', 'true');
    sessionStorage.setItem('auth_start_time', Date.now().toString());

    // Clear the flag after 15 seconds to prevent it from persisting indefinitely
    setTimeout(() => {
      sessionStorage.removeItem('auth_initializing');
      sessionStorage.removeItem('auth_start_time');
    }, 15000);
  }
};

// Helper function to wait for auth session to be ready
export const waitForAuthSession = async (maxWaitMs: number = 5000): Promise<boolean> => {
  if (typeof window === 'undefined') return false;

  const startTime = Date.now();

  while (Date.now() - startTime < maxWaitMs) {
    const authSession = localStorage.getItem('auth_session');
    const authSessionReady = sessionStorage.getItem('auth_session_ready') === 'true';

    if (authSession && authSessionReady) {
      return true;
    }

    // Wait 100ms before checking again
    await new Promise(resolve => setTimeout(resolve, 100));
  }

  return false;
};

// Helper function to check if we're in auth initialization period
const isInAuthInitialization = (): boolean => {
  if (typeof window === 'undefined') return false;

  const authInitializing = sessionStorage.getItem('auth_initializing') === 'true';
  const authStartTime = sessionStorage.getItem('auth_start_time');
  const authSessionReady = sessionStorage.getItem('auth_session_ready') === 'true';

  // If auth session is ready, we're no longer initializing
  if (authSessionReady) return false;

  if (authInitializing && authStartTime) {
    const timeSinceStart = Date.now() - parseInt(authStartTime);
    // Consider auth initializing for up to 15 seconds
    return timeSinceStart < 15000;
  }

  // Also check if we're within 10 seconds of page load
  const withinPageLoad = window.performance && performance.now() < 10000;

  // Check if we're on an auth-related page
  const isAuthPage =
    window.location.pathname.includes('/api/auth') ||
    window.location.pathname === '/dashboard' ||
    window.location.search.includes('code=') ||
    window.location.search.includes('state=');

  return withinPageLoad || (isAuthPage && !authSessionReady);
};

// Helper function to handle user logout (uses auth service)
export const logoutUser = async () => {
  // Use the centralized auth service for logout
  await authService.logout();
};

// Response interceptor to handle errors
//
// CRITICAL: This interceptor prevents redirect loops by NEVER automatically redirecting
// for authentication errors (403/401). Instead, it shows AuthModal to let user choose.
//
// ROOT CAUSE OF REDIRECT LOOPS:
// 1. API returns 403 → apiHelper redirects to page X
// 2. Middleware checks page X → redirects back to original page (307 redirect)
// 3. Same API call → same 403 → infinite loop
//
// SOLUTION: Show AuthModal instead of redirecting for ALL authentication errors
// This is future-proof and prevents loops regardless of endpoint or middleware logic.
//
axiosInstance.interceptors.response.use(
  response => response,
  async (error: AxiosError) => {
    let errorMessage = 'An unexpected error occurred';

    if (error.response?.data && typeof error.response.data === 'object') {
      errorMessage = (error.response.data as { message?: string })?.message || errorMessage;
    }

    // Handle 401 Unauthorized responses (expired token)
    if (error.response?.status === 401) {
      // Get the URL that caused the 401 error
      const url = error.config?.url || '';

      // Check if this is an auth-related endpoint
      const isAuthEndpoint = url.includes('/auth/') || url.includes('/api/auth/');

      // Check if we're in auth initialization period
      const isInitialLogin = isInAuthInitialization();

      if (isAuthEndpoint || isInitialLogin) {
        // For auth endpoints during initial login, we'll let the component handle the error
        // This prevents logout loops during the authentication process
        return Promise.reject(
          new ApiError('Authentication in progress...', 401, error.response?.data, error)
        );
      }

      // For other 401 errors, redirect to login
      // This provides a better UX than showing a modal

      if (typeof window !== 'undefined') {
        const params = new URLSearchParams({
          returnTo: window.location.pathname + window.location.search,
        });
        window.location.href = `/api/auth/login?${params.toString()}`;
      }

      // Create a special error that indicates the modal was shown
      const modalShownError = new ApiError(
        'Session expired - auth modal shown',
        401,
        error.response?.data,
        error
      );
      // Add a flag to indicate the modal was shown
      modalShownError.modalShown = true;

      return Promise.reject(modalShownError);
    }

    // Handle 402 Payment Required responses (credit/subscription issues)
    if (error.response?.status === 402) {
      // Parse the subscription error
      const { actionType, message, requiredCredits, availableCredits } = parseSubscriptionError(
        error.response.data
      );

      // Try to show the credit purchase modal directly first
      if (showCreditPurchaseModalFn) {
        showCreditPurchaseModalFn({
          actionType: actionType,
          requiredCredits,
          availableCredits,
          message,
        });
      } else {
        // Fallback to the subscription limit modal if credit modal not available
        showSubscriptionLimitModal(actionType, message, requiredCredits, availableCredits);
      }

      // Return a custom error
      return Promise.reject(
        new ApiError(message || 'Insufficient credits', 402, error.response.data, error)
      );
    }

    // Handle 403 Forbidden responses (permission issues)
    if (error.response?.status === 403) {
      // Get the URL that caused the 403 error
      const url = error.config?.url || '';
      const endpoint = url.replace(BACKEND_API_URL, '');

      // Check if this is a job-seeker related endpoint
      const isJobSeekerEndpoint = url.includes('/job-seekers');

      // Check if this is an auth-related endpoint
      const isAuthEndpoint = url.includes('/auth/');

      // Check if this is a subscription-related error
      const errorData = error.response?.data as any;
      const isSubscriptionError =
        errorData &&
        (errorData.limitType ||
          (typeof errorData.message === 'string' &&
            errorData.message.toLowerCase().includes('limit')));

      // FUTURE-PROOF APPROACH: Never redirect automatically for 403 errors
      // This prevents ALL possible redirect loops, regardless of endpoint

      // Only allow component-level handling for specific cases that need it
      if (isJobSeekerEndpoint || isAuthEndpoint || isSubscriptionError) {
        // For subscription errors, show the subscription limit modal
        if (isSubscriptionError) {
          // Parse the subscription error
          const { actionType, message, requiredCredits, availableCredits } = parseSubscriptionError(
            error.response.data
          );

          // Show the subscription limit modal
          showSubscriptionLimitModal(actionType, message, requiredCredits, availableCredits);

          // Return a custom error
          return Promise.reject(
            new ApiError(message || 'Subscription limit reached', 403, error.response.data, error)
          );
        }

        // For job-seeker or auth endpoints, we'll let the component handle the error
        // This allows JobSeekerSetupSlider to create a profile if needed
        if (error.response?.data) {
          // Use type assertion to add custom property
          (error as any).userData = error.response.data;
        }
      } else {
        // For ALL other endpoints (company, dashboard, jobs, candidates, etc.)
        // Check if we should retry or redirect to session-expired page

        // Track this auth retry attempt
        const canRetry = trackAuthRetry(endpoint);

        if (!canRetry) {
          // We've exceeded max retries, redirect to login
          if (typeof window !== 'undefined') {
            const params = new URLSearchParams({
              returnTo: window.location.pathname + window.location.search,
              reason: 'max_retries_exceeded',
            });
            window.location.href = `/api/auth/login?${params.toString()}`;
          }

          // Create a special error that indicates max retries exceeded
          const maxRetriesError = new ApiError(
            'Maximum authentication retries exceeded',
            403,
            error.response?.data,
            error
          );
          maxRetriesError.maxRetriesExceeded = true;

          return Promise.reject(maxRetriesError);
        }

        // Instead of redirecting, show auth modal or let the component handle it
        if (typeof window !== 'undefined') {
          // Check if we're already on session-expired page
          const currentPath = window.location.pathname;
          if (currentPath.includes('/session-expired')) {
            // Don't redirect if we're already on session-expired page
            return Promise.reject(
              new ApiError('Authentication required', 403, error.response?.data, error)
            );
          }

          // For first few retries, just return the error and let the component handle it
          if (canRetry) {
            return Promise.reject(
              new ApiError('Permission denied - please try again', 403, error.response?.data, error)
            );
          }
        }

        // Create a special error that indicates max retries
        const authError = new ApiError('Authentication required', 403, error.response?.data, error);
        authError.requiresAuth = true;

        return Promise.reject(authError);
      }
    }

    // Create a custom ApiError with better formatting
    const apiError = new ApiError(
      errorMessage,
      error.response?.status,
      error.response?.data,
      error
    );

    // Show error modal for server errors (5xx) or certain 4xx errors
    // Skip showing modal for auth errors, subscription errors, and job seeker setup errors
    const url = error.config?.url || '';
    const isAuthEndpoint = url.includes('/auth/') || url.includes('/api/auth/');
    const isJobSeekerEndpoint = url.includes('/job-seekers');
    const isCompanyEndpoint = url.includes('/companies');
    const isSubscriptionError =
      error.response?.status === 403 &&
      error.response?.data &&
      ((error.response.data as any).limitType ||
        (typeof (error.response.data as any).message === 'string' &&
          (error.response.data as any).message.toLowerCase().includes('limit')));

    // Handle 404 errors that might indicate setup/permission issues
    // Be more selective about which 404s trigger auth modal to avoid false positives
    if (error.response?.status === 404) {
      const url = error.config?.url || '';

      // Handle 404 for auth/sync-role endpoint specifically - this is now expected behavior
      if (url.includes('/auth/sync-role')) {
        // This is expected if the endpoint doesn't exist yet
        // Just return the error without redirecting
        return Promise.reject(apiError);
      }

      // Only show auth modal for 404s on core user/company endpoints that might indicate setup issues
      if (!isAuthEndpoint) {
        const isCoreUserEndpoint =
          url.includes('/companies') || url.includes('/profile') || url.includes('/dashboard');

        if (isCoreUserEndpoint) {
          // Redirect to login with role_missing error
          if (typeof window !== 'undefined') {
            const params = new URLSearchParams({
              returnTo: window.location.pathname + window.location.search,
            });
            window.location.href = `/api/auth/login?${params.toString()}`;
          }

          // Create a special error that indicates the redirect was performed
          const redirectError = new ApiError(
            'Setup required - redirected to auth-check',
            404,
            error.response?.data,
            error
          );
          redirectError.redirectPerformed = true;
          return Promise.reject(redirectError);
        }
      }
    }

    // Show error modal for server errors (5xx) or certain client errors
    if (
      (error.response?.status && error.response.status >= 500) || // Server errors
      (error.response?.status === 404 &&
        !isAuthEndpoint &&
        !isJobSeekerEndpoint &&
        !isCompanyEndpoint) || // Not found errors (except auth/job seeker/company)
      (error.response?.status === 400 && !isAuthEndpoint && !isJobSeekerEndpoint) // Bad request errors (except auth/job seeker)
    ) {
      // Don't show error modal for JobSeekerSetupSlider, CompanySetupSlider, or email endpoints
      const isSetupEndpoint =
        url.includes('/setup') || url.includes('/companies/client') || url.includes('/job-seekers');
      const isEmailEndpoint = url.includes('/email/');

      if (!isSetupEndpoint && !isSubscriptionError && !isEmailEndpoint) {
        showErrorModalFn({
          message: apiError.message,
          title: `Error ${apiError.status || ''}`,
        });
      }
    }

    // Return our custom error instead of the original axios error
    return Promise.reject(apiError);
  }
);

// Main request function
export const request = async <T>(
  endpoint: string,
  method: string = 'GET',
  body: any = null,
  options: {
    params?: any;
    headers?: Record<string, string>;
    signal?: AbortSignal;
    bypassCache?: boolean;
    skipDedupe?: boolean;
    bypassCircuitBreaker?: boolean;
    skipAuth?: boolean;
    onUploadProgress?: (progressEvent: any) => void;
    isPollingRequest?: boolean; // New flag for polling requests
  } = {}
): Promise<T> => {
  const {
    params,
    headers = {},
    signal,
    bypassCache = false,
    skipDedupe = false,
    bypassCircuitBreaker = false,
    isPollingRequest = false,
    skipAuth = false,
    onUploadProgress,
  } = options;

  // Add null check for endpoint
  if (!endpoint) {
    throw new Error('API endpoint cannot be null or undefined');
  }

  // Create a unique request key for deduplication
  const requestKey = `${method}_${endpoint}_${JSON.stringify(params || {})}`;

  // Handle request cancellation for problematic endpoints
  let abortController: AbortController | undefined;
  if (isCancellableEndpoint(endpoint)) {
    abortController = createAbortController(endpoint);
    // If signal is already provided, use it; otherwise use our AbortController
    if (!signal && abortController) {
      options.signal = abortController.signal;
    }
  }

  // Skip auth check for public endpoints or if skipAuth is true
  if (!isPublicEndpoint(endpoint) && !skipAuth) {
    // Check if auth token exists and is valid before proceeding
    // Skip localStorage access in server-side environment
    if (typeof window === 'undefined') {
      // In server-side context (Next.js API routes), skip auth checks
      // The API routes handle their own authentication via session middleware
    } else {
      const authSession = localStorage.getItem('auth_session');
      if (!authSession) {
        // Check if we're in auth initialization period
        const isInitialLogin = isInAuthInitialization();

        // Check if this is an auth-related endpoint
        const isAuthEndpoint = endpoint.includes('/auth/') || endpoint === '/companies/client';

        if (isInitialLogin || isAuthEndpoint) {
          // For auth endpoints during initial login, we'll proceed with the request
          // This prevents logout loops during the authentication process
        } else {
          // Only redirect if this is a browser environment
          if (typeof window !== 'undefined') {
            console.warn('No auth session found for protected endpoint:', endpoint);

            // Show auth modal with auto-logout
            if (showAuthModalFn) {
              showAuthModalFn({
                message: 'Your session has expired. Please log in again.',
                title: 'Session Required',
                autoLogout: true,
              });
            } else {
              console.warn(
                'Auth modal function not initialized, cannot show modal for missing auth session'
              );
              // Fallback to error modal if auth modal not available
              if (showErrorModalFn) {
                showErrorModalFn({
                  message:
                    'Your session has expired. Please refresh the page or log out and log back in.',
                  title: 'Session Required',
                });
              }
            }

            // Create a special error that indicates the modal was shown
            const modalShownError = new ApiError('Session required - modal shown', 401);
            // Add a flag to indicate the modal was shown
            modalShownError.modalShown = true;

            throw modalShownError;
          }
        }
      }
    }
  }

  try {
    // Detect rapid API calls (potential infinite loops)
    if (!isPollingRequest && !bypassCache) {
      detectRapidApiCalls(endpoint);
    }

    // First, check if this is a polling endpoint
    const isPolling = isWorkerTaskEndpoint(endpoint);

    // Automatically bypass circuit breaker for worker task endpoints, polling requests, or when explicitly requested
    const shouldBypassCircuitBreaker = bypassCircuitBreaker || isPolling || isPollingRequest;

    // For polling endpoints, we want to track them but with different rules
    if (isPolling) {
      // Always track the call for monitoring purposes
      trackApiCall(endpoint, method);

      // For polling endpoints, we don't apply debouncing in the same way
      // but we still want to ensure we're not hammering the API
      debounceApiCall(endpoint, method);

      // No caching for video-jd status endpoints - always poll for fresh status
    }
    // For non-polling endpoints, apply normal circuit breaker logic
    else if (!shouldBypassCircuitBreaker) {
      // Check if we should allow this call based on circuit breaker and rate limiting
      if (!trackApiCall(endpoint, method)) {
        console.error(`🚫 Circuit breaker or rate limiting blocked call to ${endpoint}`);

        // No caching - return fallback response for circuit breaker

        // For navigation-related endpoints, we want to be more lenient
        const isNavigationEndpoint =
          endpoint && endpoint.startsWith('/companies/client') ||
          endpoint === '/auth/me' ||
          endpoint === '/dashboard/stats';

        if (isNavigationEndpoint) {
          const endpointKey = endpoint.split('?')[0];
          if (API_CALL_TRACKERS[endpointKey]) {
            API_CALL_TRACKERS[endpointKey].circuitBroken = false;
            API_CALL_TRACKERS[endpointKey].consecutiveErrors = 0;
            API_CALL_TRACKERS[endpointKey].circuitBrokenUntil = undefined;
            API_CALL_TRACKERS[endpointKey].timestamps = [];
          }
        } else {
          // For job endpoints, return an empty array or object based on the endpoint pattern
          if (endpoint.includes('/jobs/')) {
            // For specific job endpoints, return an empty job object with minimal fields
            if (endpoint.match(/\/jobs\/[a-zA-Z0-9-]+$/)) {
              return {
                id: endpoint.split('/jobs/')[1],
                title: 'Loading...',
                status: 'DRAFT',
                _circuit_breaker_response: true,
              } as unknown as T;
            }
            return [] as unknown as T;
          }

          // For candidate endpoints, return empty arrays or objects
          if (endpoint.includes('/candidates/')) {
            return [] as unknown as T;
          }
          return { _circuit_breaker_response: true } as unknown as T;
        }
      }

      // Check debouncing for rapid successive calls
      if (!debounceApiCall(endpoint, method)) {
        // If debounced, wait a bit and continue (don't block completely)
        await new Promise(resolve => setTimeout(resolve, 500));
      }
    }

    // Block all job-related API calls if the global block flag is set - DISABLED
    // if (typeof window !== 'undefined' && window['blockJobsApiCalls'] === true) {
    //   // Only block GET requests to job-related endpoints
    //   if (
    //     method === 'GET' &&
    //     (endpoint.includes('/jobs/') || endpoint === '/jobs' || endpoint.includes('/by-status'))
    //   ) {
    //     // Unless this is a forced call (like user clicking refresh)
    //     if (!bypassCache || bypassCache !== true) {
    //       // No cached data available - throw error
    //       throw new Error(`API call blocked due to file upload in progress: ${endpoint}`);
    //     }
    //   }
    // }

    // For GET requests, check if there's already a pending request for this exact endpoint and params
    // This prevents duplicate requests for the same data
    if (method === 'GET' && !skipDedupe && pendingRequests[requestKey]) {
      return await pendingRequests[requestKey];
    }

    // No caching for GET requests - always fetch fresh data

    // For company client endpoint, add special handling to prevent excessive calls
    if (method === 'GET' && endpoint === '/companies/client' && !bypassCache) {
      // Check for a very recent fetch (within last 2 seconds) to prevent rapid successive calls
      const recentFetchKey = `recent_fetch_${endpoint}`;
      const recentFetch = localStorage.getItem(recentFetchKey);

      if (recentFetch) {
        const timestamp = parseInt(recentFetch, 10);
        const now = Date.now();

        // If fetched within last 2 seconds, wait for pending request if available
        if (now - timestamp < 2000) {
          if (pendingRequests[requestKey]) {
            return await pendingRequests[requestKey];
          }
        }
      }

      // Mark this endpoint as recently fetched
      localStorage.setItem(`recent_fetch_${endpoint}`, Date.now().toString());
    }

    // Remove bypassCache from params before sending to the server
    const requestParams = { ...params };
    delete requestParams.bypassCache;

    const config: AxiosRequestConfig = {
      url: endpoint && endpoint.startsWith('/') ? endpoint : `/${endpoint}`,
      method,
      params: requestParams,
      headers: {
        ...headers,
        'Content-Type': 'application/json',
      },
      signal,
      ...(onUploadProgress && { onUploadProgress }),
      // Add skipAuth as a custom property that will be checked in the interceptor
      ...(skipAuth && { skipAuth }),
    } as AxiosRequestConfig & { skipAuth?: boolean };

    if (['POST', 'PUT', 'PATCH'].includes(method) && body !== null) {
      config.data = body;
    }

    if (body instanceof FormData) {
      delete config.headers['Content-Type'];
    }

    // Enhanced request deduplication for GET requests
    if (method === 'GET' && !skipDedupe) {
      // Check if there's already a pending request for this exact endpoint and params
      if (pendingRequests[requestKey]) {
        return await pendingRequests[requestKey];
      }

      // Create the request promise
      const requestPromise = axiosInstance(config).then(response => response.data);

      // Store in both the old system and new enhanced system
      pendingRequests[requestKey] = requestPromise;

      try {
        const response = await requestPromise;

        // Track successful API call
        trackApiCall(endpoint, method, false);

        // Reset auth retry tracker on successful request
        resetAuthRetryTracker(endpoint);

        return response as T;
      } catch (error) {
        // For certain errors, keep the failed promise in cache for a short time
        // to prevent rapid retries of the same failing request
        if (
          isApiError(error) &&
          (error.status === 500 || error.status === 403 || error.status === 404)
        ) {
          // Keep the failed promise for 2 seconds to prevent rapid retries
          setTimeout(() => {
            delete pendingRequests[requestKey];
          }, 2000);
        } else {
          // For other errors, clean up immediately
          delete pendingRequests[requestKey];
        }

        // Clean up AbortController if we created one
        if (abortController) {
          cleanupAbortController(endpoint);
        }

        throw error;
      }
    } else {
      // For non-GET requests or when skipping deduplication
      const response = await axiosInstance(config);

      // Track successful API call
      trackApiCall(endpoint, method, false);

      // Reset auth retry tracker on successful request
      resetAuthRetryTracker(endpoint);

      // Clean up AbortController if we created one
      if (abortController) {
        cleanupAbortController(endpoint);
      }

      return response.data as T;
    }
  } catch (error) {
    // Clean up AbortController if we created one
    if (abortController) {
      cleanupAbortController(endpoint);
    }

    // Track this error for circuit breaker
    let statusCode: number | undefined;

    if (isApiError(error)) {
      statusCode = error.status;
    } else if (error.isAxiosError) {
      statusCode = (error as AxiosError).response?.status;
    }

    // Update the API call tracker with this error
    trackApiCall(endpoint, method, true, statusCode);

    // Handle AbortError specifically
    if (axios.isCancel(error)) {
      throw new ApiError(`Request to ${endpoint} timed out`, 408);
    }

    // Special handling for circuit breaker errors
    if (isApiError(error) && error.message.includes('circuit breaker or rate limiting')) {
      // For navigation-related endpoints, we want to be more lenient with circuit breaker errors
      const isNavigationEndpoint =
        endpoint.startsWith('/companies/client') ||
        endpoint === '/auth/me' ||
        endpoint === '/dashboard/stats';

      // Reset circuit breaker for navigation endpoints
      if (isNavigationEndpoint) {
        const endpointKey = endpoint.split('?')[0];
        if (API_CALL_TRACKERS[endpointKey]) {
          API_CALL_TRACKERS[endpointKey].circuitBroken = false;
          API_CALL_TRACKERS[endpointKey].consecutiveErrors = 0;
          API_CALL_TRACKERS[endpointKey].circuitBrokenUntil = undefined;
          API_CALL_TRACKERS[endpointKey].timestamps = [];
        }

        // Return a minimal response to prevent UI errors
        if (endpoint === '/auth/me') {
          return { isAuthenticated: true } as unknown as T;
        } else if (endpoint && endpoint.startsWith('/companies/client')) {
          return { id: 'temp-id', name: 'Loading...' } as unknown as T;
        } else {
          return {} as unknown as T;
        }
      }
      // For job endpoints, return an empty array or object based on the endpoint pattern
      if (endpoint.includes('/jobs/')) {
        // For specific job endpoints, return an empty job object with minimal fields
        if (endpoint.match(/\/jobs\/[a-zA-Z0-9-]+$/)) {
          return {
            id: endpoint.split('/jobs/')[1],
            title: 'Loading...',
            status: 'DRAFT',
            _circuit_breaker_response: true,
          } as unknown as T;
        }
        return [] as unknown as T;
      }

      // For candidate endpoints, return empty arrays or objects
      if (endpoint.includes('/candidates/')) {
        return [] as unknown as T;
      }
      return { _circuit_breaker_response: true } as unknown as T;
    }

    // If it's already our custom ApiError, just rethrow it
    if (isApiError(error)) {
      throw error;
    }

    // If it's an Axios error, convert it to our ApiError
    if (error.isAxiosError) {
      throw ApiError.fromAxiosError(error as AxiosError);
    }

    // For any other error, wrap it in our ApiError
    if (error instanceof Error) {
      throw new ApiError(error.message, undefined, undefined, error);
    }

    // For unknown errors
    throw new ApiError('An unexpected error occurred', undefined, undefined);
  }
};

// Export both the apiClient for direct use and apiHelper for method-based use
export const apiClient = {
  get: <T>(url: string, options: Parameters<typeof request>[3] = {}) =>
    request<T>(url, 'GET', null, options),
  post: <T>(url: string, data?: any, options: Parameters<typeof request>[3] = {}) =>
    request<T>(url, 'POST', data, options),
  put: <T>(url: string, data?: any, options: Parameters<typeof request>[3] = {}) =>
    request<T>(url, 'PUT', data, options),
  patch: <T>(url: string, data?: any, options: Parameters<typeof request>[3] = {}) =>
    request<T>(url, 'PATCH', data, options),
  delete: <T>(url: string, options: Parameters<typeof request>[3] = {}) =>
    request<T>(url, 'DELETE', null, options),
};

const apiHelper = {
  request,
  get: <T = any>(endpoint: string, options: Parameters<typeof request>[3] = {}) =>
    request<T>(endpoint, 'GET', null, options),
  post: <T = any>(endpoint: string, body: any, options: Parameters<typeof request>[3] = {}) =>
    request<T>(endpoint, 'POST', body, options),
  put: <T = any>(endpoint: string, body: any, options: Parameters<typeof request>[3] = {}) =>
    request<T>(endpoint, 'PUT', body, options),
  patch: <T = any>(endpoint: string, body: any, options: Parameters<typeof request>[3] = {}) =>
    request<T>(endpoint, 'PATCH', body, options),
  delete: <T>(endpoint: string, options: Parameters<typeof request>[3] = {}) =>
    request<T>(endpoint, 'DELETE', null, options),
  isPublicEndpoint,
  isWorkerTaskEndpoint,
  logoutUser, // Export the logout function for use elsewhere

  // Add a method to clear jobs cache (now just clears recent fetch tracking)
  clearJobsCache: () => {
    if (typeof localStorage !== 'undefined') {
      // Only clear recent fetch tracking since caching is removed
      const keys = Object.keys(localStorage);
      const jobsFetchKeys = keys.filter(
        key => key.startsWith('recent_fetch_') && key.includes('/jobs')
      );

      jobsFetchKeys.forEach(key => {
        localStorage.removeItem(key);
      });
    }
  },

  // Add a method to clear company cache (now just clears recent fetch tracking)
  clearCompanyCache: () => {
    if (typeof localStorage !== 'undefined') {
      localStorage.removeItem('recent_fetch_/companies/client');
    }
  },

  // Add a method to get pending requests (useful for debugging)
  getPendingRequests: () => ({ ...pendingRequests }),

  // Mark an endpoint as a polling endpoint (useful for debugging or handling edge cases)
  markAsPollingEndpoint: (endpoint: string) => {
    const endpointKey = endpoint.split('?')[0];
    if (!API_CALL_TRACKERS[endpointKey]) {
      API_CALL_TRACKERS[endpointKey] = {
        count: 0,
        timestamps: [],
        consecutiveErrors: 0,
        circuitBroken: false,
        isPollingEndpoint: true,
        lastPollingTimestamp: Date.now(),
      };
    } else {
      API_CALL_TRACKERS[endpointKey].isPollingEndpoint = true;
      API_CALL_TRACKERS[endpointKey].lastPollingTimestamp = Date.now();
      // If the circuit was broken, reset it
      if (API_CALL_TRACKERS[endpointKey].circuitBroken) {
        API_CALL_TRACKERS[endpointKey].circuitBroken = false;
        API_CALL_TRACKERS[endpointKey].circuitBrokenUntil = undefined;
      }
    }
    return true;
  },

  // Debug helper to check if an endpoint is being blocked
  debugEndpoint: (endpoint: string) => {
    const endpointKey = endpoint.split('?')[0];
    const tracker = API_CALL_TRACKERS[endpointKey];
    const isWorker = isWorkerTaskEndpoint(endpoint);

    return { endpointKey, tracker, isWorker };
  },
};

export default apiHelper;
