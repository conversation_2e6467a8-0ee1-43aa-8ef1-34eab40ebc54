import {
  BarChart3,
  BrainIcon,
  Briefcase,
  Building2,
  ClipboardCheck,
  FileText,
  Flag,
  Heart,
  HeartHandshake,
  LayoutDashboard,
  Mail,
  Plus,
  User,
  Users,
  Video,
  DollarSign,
} from 'lucide-react';

import { themeGradients } from '@/constants/gradients';
import { UserRole } from '@/types/roles';

// Define navigation items with their associated gradients
export const NAVIGATION_CONFIG = [
  {
    label: 'Dashboard',
    href: '/dashboard',
    icon: LayoutDashboard,
    roles: [UserRole.EMPLOYER, UserRole.JOB_SEEKER, UserRole.GRADUATE, UserRole.ADMIN],
    gradient: themeGradients.dashboard,
    description: `Here's an overview of your activities`,
    group: 'hub',
  },
  {
    label: 'Jobs',
    href: '/jobs',
    icon: Briefcase,
    roles: [UserRole.EMPLOYER, UserRole.ADMIN],
    gradient: themeGradients.notifications,
    description: 'View and manage your active job postings',
    group: 'hub',
  },
  {
    label: 'Create Job Description',
    href: '/job-description-creation',
    icon: Plus,
    roles: [UserRole.EMPLOYER, UserRole.ADMIN],
    gradient: themeGradients.notifications,
    description: 'Create new job descriptions with AI assistance',
    group: 'hub',
    hidden: true,
  },
  {
    label: 'Video JD',
    href: '/video-jd',
    icon: Video,
    roles: [UserRole.EMPLOYER, UserRole.ADMIN],
    gradient: themeGradients.notifications,
    description: 'Create and manage video job descriptions',
    group: 'hub',
    hidden: true,
  },
  {
    label: 'Match & Rank',
    href: '/match-rank',
    icon: BrainIcon,
    roles: [UserRole.EMPLOYER, UserRole.ADMIN],
    gradient: themeGradients.matchRank,
    description: 'AI-powered candidate matching and ranking',
    group: 'hub',
    hidden: true,
  },
  {
    label: 'Culture Fit',
    href: '/culture-fit',
    icon: HeartHandshake,
    roles: [UserRole.EMPLOYER, UserRole.ADMIN],
    gradient: themeGradients.cultureFit,
    description: 'Assess candidate cultural alignment',
    group: 'hub',
    hidden: true,
  },
  // {
  //   label: 'Our Candidates',
  //   href: '/our-candidates',
  //   icon: Users,
  //   roles: [UserRole.EMPLOYER],
  //   gradient: themeGradients.notifications,
  //   description: 'Browse and manage qualified talent in your candidate pool',
  //   group: 'hub',
  // },
  {
    label: 'Talent Hub',
    href: '/talent-hub',
    icon: Users,
    roles: [UserRole.EMPLOYER, UserRole.ADMIN],
    gradient: themeGradients.matchRank,
    description: 'Track and manage candidates through the hiring workflow',
    group: 'hub',
  },
  {
    label: 'My Candidates',
    href: '/my-candidates',
    icon: Heart,
    roles: [UserRole.EMPLOYER, UserRole.ADMIN],
    gradient: themeGradients.cultureFit,
    description: 'Manage your favorite candidates, shortlisted talent, and candidate notes',
    group: 'hub',
    hidden: true,
  },
  // {
  //   label: 'ATS Integration',
  //   href: '/ats-integration',
  //   icon: Database,
  //   roles: [UserRole.EMPLOYER, UserRole.ADMIN],
  //   gradient: themeGradients.matchRank,
  //   description:
  //     'Jobs are automatically synchronized from your ATS providers. These jobs cannot be directly edited in this interface',
  //   group: 'hub',
  // },
  {
    label: 'Company Settings',
    href: '/company-settings',
    icon: Building2,
    roles: [UserRole.EMPLOYER, UserRole.ADMIN],
    gradient: themeGradients.notifications,
    description: 'Manage your company profile settings',
    group: 'hub',
  },

  {
    label: 'My Applications',
    href: '/applications',
    icon: FileText,
    roles: [UserRole.JOB_SEEKER, UserRole.GRADUATE],
    gradient: themeGradients.notifications,
    description: 'Track your job applications and their status',
    group: 'hub',
  },
  {
    label: 'Learning Path',
    href: '/learning',
    icon: BrainIcon,
    roles: [UserRole.GRADUATE],
    gradient: themeGradients.notifications,
    description: 'Access learning resources and career guidance',
    group: 'hub',
  },
  {
    label: 'Profile',
    href: '/profile',
    icon: User,
    roles: [UserRole.JOB_SEEKER, UserRole.GRADUATE],
    gradient: themeGradients.notifications,
    description: 'Manage your professional profile and resume',
    group: 'hub',
  },
  {
    label: 'Career Insights',
    href: '/career-insights',
    icon: BrainIcon,
    roles: [UserRole.JOB_SEEKER, UserRole.GRADUATE],
    gradient: themeGradients.matchRank,
    description: 'Discover personalized career paths and growth opportunities',
    group: 'hub',
  },
  {
    label: 'Open Jobs',
    href: '/open-jobs',
    icon: Briefcase,
    roles: [UserRole.JOB_SEEKER, UserRole.GRADUATE, UserRole.ADMIN],
    gradient: themeGradients.notifications,
    description: 'Browse all open positions across our platform',
    group: 'jobs',
  },
  {
    label: 'My Job Board',
    href: '/my-jobs',
    icon: Briefcase,
    roles: [UserRole.EMPLOYER],
    gradient: themeGradients.notifications,
    description: 'View and manage your posted job listings',
    group: 'jobs',
  },

  {
    label: 'Partners',
    href: '/marketplace',
    icon: HeartHandshake, // Using HeartHandshake icon from lucide-react for a nice marketplace representation
    roles: [UserRole.EMPLOYER, UserRole.ADMIN],
    gradient: themeGradients.notifications,
    description: 'Discover and connect with our trusted partners',
    group: 'market',
  },
  // {
  //   label: 'Feedback',
  //   href: '/feedback',
  //   icon: Lightbulb,
  //   roles: [UserRole.EMPLOYER, UserRole.JOB_SEEKER, UserRole.GRADUATE, UserRole.ADMIN],
  //   gradient: themeGradients.notifications,
  //   description: 'Share your ideas and feedback to help us improve',
  //   group: 'market',
  // },
  // Admin pages
  {
    label: 'Registration',
    href: '/admin/registration',
    icon: ClipboardCheck,
    roles: [UserRole.ADMIN], // Only admin users should see this
    gradient: themeGradients.dashboard,
    description: 'Manage and approve user registrations',
    group: 'admin',
  },
  {
    label: 'Usage',
    href: '/admin/usage',
    icon: BarChart3,
    roles: [UserRole.ADMIN], // Only admin users should see this
    gradient: themeGradients.dashboard,
    description: 'Monitor platform usage and company statistics',
    group: 'admin',
  },
  {
    label: 'Feature Flags',
    href: '/admin/feature-flags',
    icon: Flag,
    roles: [UserRole.ADMIN], // Only admin users should see this
    gradient: themeGradients.dashboard,
    description: 'Manage feature flags for the application',
    group: 'admin',
  },
  {
    label: 'Test Dashboard',
    href: '/test-dashboard',
    icon: BarChart3, // Using BarChart3 temporarily until TestTube import is fixed
    roles: [UserRole.ADMIN], // Available to all users
    gradient: themeGradients.dashboard,
    description: 'Monitor and manage tests across your entire application',
    group: 'admin',
  },
  {
    label: 'Waitlists',
    href: '/admin/waitlists',
    icon: Users,
    roles: [UserRole.ADMIN], // Admin and employer users can see this
    gradient: themeGradients.dashboard,
    description: 'View and manage waitlist entries',
    group: 'admin',
  },
  {
    label: 'Email Configuration',
    href: '/email-configuration',
    icon: Mail,
    roles: [UserRole.ADMIN],
    gradient: themeGradients.cultureFit,
    description: 'Configure email settings and templates',
    group: 'admin',
  },

  // Referral Partner pages
  {
    label: 'Partner Dashboard',
    href: '/referral-partner',
    icon: Users,
    roles: [UserRole.REFERRAL_PARTNER],
    gradient: themeGradients.matchRank,
    description: 'Manage your referrals and track earnings',
    group: 'partner',
  },
  {
    label: 'Open Job Board',
    href: '/referral-partner/jobs',
    icon: Briefcase,
    roles: [UserRole.REFERRAL_PARTNER],
    gradient: themeGradients.notifications,
    description: 'Browse jobs and get your referral links',
    group: 'partner',
  },
  {
    label: 'Earnings',
    href: '/referral-partner/earnings',
    icon: DollarSign,
    roles: [UserRole.REFERRAL_PARTNER],
    gradient: themeGradients.cultureFit,
    description: 'Track your earnings and request payouts',
    group: 'partner',
  },
] as const;

// Helper functions
export const getGradientByPath = (path: string) => {
  const route = NAVIGATION_CONFIG.find(item => item.href === path);
  return route?.gradient.background ?? themeGradients.dashboard.background;
};

export const getActiveGradient = (path: string) => {
  const route = NAVIGATION_CONFIG.find(item => item.href === path);
  return route?.gradient.active ?? themeGradients.dashboard.active;
};

export const getLineColor = (path: string) => {
  const route = NAVIGATION_CONFIG.find(item => item.href === path);
  return route?.gradient.lineColor ?? themeGradients.dashboard.lineColor;
};

export const getPageDescription = (path: string) => {
  const route = NAVIGATION_CONFIG.find(item => item.href === path);
  return route?.description ?? '';
};

export const getNavItemsByRole = (role?: UserRole) => {
  if (!role) return NAVIGATION_CONFIG;
  return NAVIGATION_CONFIG.filter((item: any) => item.roles.includes(role));
};

export type NavItem = (typeof NAVIGATION_CONFIG)[number] & {
  isDynamic?: boolean;
  hidden?: boolean;
};
