/**
 * Centralized definition of public routes that don't require authentication
 * This ensures consistency across all authentication guards and middleware
 */

// Define public route patterns
export const PUBLIC_ROUTES = {
  // Root and landing pages
  ROOT: '/',
  LANDING: '/landing',

  // Holding pages (pre-authentication)
  EMPLOYER_HOLDING: '/employer-holding',
  JO<PERSON>_SEEKER_HOLDING: '/job-seeker-holding',
  GRADUATE_HOLDING: '/graduate-holding',

  // Auth-related routes
  AUTH_DEBUG: '/auth-debug',
  DEBUG: '/debug',
  SESSION_EXPIRED: '/session-expired',

  // Public company and job pages
  COMPANY_PROFILE: '/company-profile',
  OPEN_JOBS: '/open-jobs',

  // Shared content
  SHARED: '/shared',

  // System routes
  API_AUTH: '/api/auth',
  NEXT_STATIC: '/_next',
  STATIC: '/static',
  FAVICON: '/favicon.ico',

  // Subscription and feedback pages
  SUBSCRIPTION_SUCCESS: '/subscription/success',
  SUBSCRIPTION_CANCEL: '/subscription/cancel',
  SUBSCRIPTION_UPGRADE: '/subscription/upgrade',
  FEEDBACK: '/feedback',
  RECRUITMENT_ASSESSMENT: '/recruitment-assessment',

  // Video capture page (public for candidates)
  VIDEO_CAPTURE: '/video-capture',

  // Public candidate profile pages
  PUBLIC_CANDIDATE: '/public/candidate',

  // Team invitation acceptance
  ACCEPT_INVITATION: '/accept-invitation',
} as const;

// Array of all public route patterns for easy iteration
export const PUBLIC_ROUTE_PATTERNS = Object.values(PUBLIC_ROUTES);

// Type for public routes
export type PublicRoute = (typeof PUBLIC_ROUTES)[keyof typeof PUBLIC_ROUTES];

/**
 * Check if a given path matches any public route pattern
 * @param path - The path to check
 * @returns true if the path is public, false otherwise
 */
export function isPublicRoute(path: string): boolean {
  if (!path) return false;
  return PUBLIC_ROUTE_PATTERNS.some(
    publicPath => publicPath && (path === publicPath || path.startsWith(publicPath + '/'))
  );
}

/**
 * Get public routes for middleware ignore patterns
 * These are the exact patterns used in middleware
 */
export function getMiddlewareIgnorePaths(): string[] {
  return [
    PUBLIC_ROUTES.API_AUTH,
    PUBLIC_ROUTES.NEXT_STATIC,
    PUBLIC_ROUTES.FAVICON,
    PUBLIC_ROUTES.STATIC,
    PUBLIC_ROUTES.AUTH_DEBUG,
    PUBLIC_ROUTES.DEBUG,
    PUBLIC_ROUTES.SESSION_EXPIRED,
    PUBLIC_ROUTES.COMPANY_PROFILE,
    PUBLIC_ROUTES.OPEN_JOBS,
    PUBLIC_ROUTES.RECRUITMENT_ASSESSMENT,
    PUBLIC_ROUTES.VIDEO_CAPTURE,
    PUBLIC_ROUTES.PUBLIC_CANDIDATE,
    PUBLIC_ROUTES.ACCEPT_INVITATION,
  ];
}

/**
 * Get public routes for client-side authentication guards
 * These include all routes that should be accessible without authentication
 */
export function getClientPublicRoutes(): string[] {
  return [
    PUBLIC_ROUTES.ROOT,
    PUBLIC_ROUTES.LANDING,
    PUBLIC_ROUTES.EMPLOYER_HOLDING,
    PUBLIC_ROUTES.JOB_SEEKER_HOLDING,
    PUBLIC_ROUTES.GRADUATE_HOLDING,
    PUBLIC_ROUTES.AUTH_DEBUG,
    PUBLIC_ROUTES.SESSION_EXPIRED,
    PUBLIC_ROUTES.COMPANY_PROFILE,
    PUBLIC_ROUTES.OPEN_JOBS,
    PUBLIC_ROUTES.SHARED,
    PUBLIC_ROUTES.RECRUITMENT_ASSESSMENT,
    PUBLIC_ROUTES.VIDEO_CAPTURE,
    PUBLIC_ROUTES.PUBLIC_CANDIDATE,
    PUBLIC_ROUTES.ACCEPT_INVITATION,
  ];
}

/**
 * Get skip paths for role validation
 * These paths should skip role validation checks
 */
export function getRoleValidationSkipPaths(): string[] {
  return [
    PUBLIC_ROUTES.API_AUTH,
    PUBLIC_ROUTES.NEXT_STATIC,
    PUBLIC_ROUTES.STATIC,
    PUBLIC_ROUTES.AUTH_DEBUG,
    PUBLIC_ROUTES.DEBUG,
    PUBLIC_ROUTES.OPEN_JOBS,
    PUBLIC_ROUTES.COMPANY_PROFILE,
    PUBLIC_ROUTES.SUBSCRIPTION_SUCCESS,
    PUBLIC_ROUTES.SUBSCRIPTION_CANCEL,
    PUBLIC_ROUTES.SUBSCRIPTION_UPGRADE,
    PUBLIC_ROUTES.FEEDBACK,
    PUBLIC_ROUTES.RECRUITMENT_ASSESSMENT,
  ];
}

/**
 * Get protected route check exclusions for AuthStateHandler
 * These paths should not trigger authentication redirects
 */
export function getAuthStateHandlerPublicPaths(): string[] {
  return [
    PUBLIC_ROUTES.API_AUTH,
    PUBLIC_ROUTES.ROOT,
    PUBLIC_ROUTES.LANDING,
    PUBLIC_ROUTES.EMPLOYER_HOLDING,
    PUBLIC_ROUTES.JOB_SEEKER_HOLDING,
    PUBLIC_ROUTES.GRADUATE_HOLDING,
    PUBLIC_ROUTES.SESSION_EXPIRED,
    PUBLIC_ROUTES.NEXT_STATIC,
    PUBLIC_ROUTES.STATIC,
    PUBLIC_ROUTES.COMPANY_PROFILE,
    PUBLIC_ROUTES.OPEN_JOBS,
    PUBLIC_ROUTES.RECRUITMENT_ASSESSMENT,
    PUBLIC_ROUTES.VIDEO_CAPTURE,
    PUBLIC_ROUTES.PUBLIC_CANDIDATE,
    PUBLIC_ROUTES.ACCEPT_INVITATION,
  ];
}
