import React, { useEffect, useRef, useState } from 'react';

import { motion } from 'framer-motion';
import {
  Bell,
  BriefcaseIcon,
  ChevronDown,
  GraduationCapIcon,
  LogOut,
  UserIcon,
  Users2Icon,
} from 'lucide-react';
import Image from 'next/image';
import { useRouter } from 'next/navigation';

import useEnhancedUserData from '@/hooks/useEnhancedUserData';
import { useNotifications } from '@/hooks/useNotifications';
import { useUser } from '@/hooks/useUser';
import { logoutUser } from '@/lib/apiHelper';
import { useCompanyStore } from '@/stores/companyStore';
import { useOnboardingStore } from '@/stores/onboardingStore';
import { UserRole } from '@/types/roles';
import { getUserDisplayName, toTitleCase } from '@/utils/getUserDisplayName';

// Custom hook to get user profile data from existing stores
const useUserProfileData = () => {
  const { user } = useUser();
  const { userData } = useEnhancedUserData();

  // Get data from existing stores
  const {
    profile: jobSeekerProfile,
    fetchProfile,
    isLoading: jobSeekerLoading,
  } = useOnboardingStore();
  const { company: companyData, fetchCompany, isLoading: companyLoading } = useCompanyStore();

  const userRole = userData?.userRole;
  const isJobSeeker = userRole === UserRole.JOB_SEEKER;
  const isEmployer = userRole === UserRole.EMPLOYER || userRole === UserRole.ADMIN;

  // Fetch data once when user role is determined
  React.useEffect(() => {
    if (user?.sub && isJobSeeker && !jobSeekerProfile && !jobSeekerLoading) {
      fetchProfile();
    }
  }, [user?.sub, isJobSeeker]); // Only depend on user and role, not the fetch function

  React.useEffect(() => {
    if (user?.sub && isEmployer && !companyData && !companyLoading) {
      fetchCompany();
    }
  }, [user?.sub, isEmployer]); // Only depend on user and role, not the fetch function

  // Return the appropriate name based on user role
  const getDisplayName = () => {
    // If we're still loading user role, show loading state
    if (!userData?.userRole && user?.sub) {
      return 'Loading...';
    }

    if (isJobSeeker) {
      if (jobSeekerLoading) {
        return 'Loading...';
      }

      if (jobSeekerProfile) {
        // Use proper name fields, avoid email addresses
        if (jobSeekerProfile.fullName && !jobSeekerProfile.fullName.includes('@')) {
          return jobSeekerProfile.fullName;
        }

        const firstName = jobSeekerProfile.firstName || '';
        const lastName = jobSeekerProfile.lastName || '';
        const fullName = `${firstName} ${lastName}`.trim();

        if (firstName.includes('@') && !lastName.includes('@')) {
          return lastName;
        }

        // Only return the constructed name if it's not empty and doesn't look like an email
        if (fullName && !fullName.includes('@')) {
          return fullName;
        }
      }
    }

    // Fallback - use our utility function for consistent naming
    const fallbackName = getUserDisplayName(user);
    if (fallbackName && fallbackName !== 'Guest') {
      return fallbackName;
    }

    if (isEmployer) {
      if (companyLoading) {
        return 'Loading...';
      }

      if (companyData) {
        return companyData.companyName || 'Company User';
      }
    }

    // Enhanced dashboard stats might have company data for employers
    if (isEmployer && userData?.company?.companyName) {
      return userData.company.companyName;
    }

    // If we have a user role but no name data yet, show role-based placeholder
    if (userRole) {
      return isJobSeeker ? 'Job Seeker' : isEmployer ? 'Employer' : 'User';
    }

    return 'Guest';
  };

  return {
    displayName: getDisplayName(),
    userRole,
    isLoading: jobSeekerLoading || companyLoading,
    profileData: isJobSeeker ? jobSeekerProfile : companyData,
  };
};

interface UserProfileWidgetProps {
  userName?: string;
  userRole?: string;
  simplified?: boolean;
}

const getRoleIcon = (role?: string) => {
  switch (role?.toLowerCase()) {
    case UserRole.EMPLOYER:
      return <BriefcaseIcon className="w-3.5 h-3.5" />;
    case UserRole.JOB_SEEKER:
      return <UserIcon className="w-3.5 h-3.5" />;
    case UserRole.EMPLOYER:
      return <Users2Icon className="w-3.5 h-3.5" />;
    case UserRole.GRADUATE:
      return <GraduationCapIcon className="w-3.5 h-3.5" />;
    default:
      return <UserIcon className="w-3.5 h-3.5" />;
  }
};

const formatRole = (role?: string) => {
  if (!role) return 'Employer';
  // Convert kebab-case or snake_case to Title Case
  return role
    .replace(/[-_]/g, ' ')
    .split(' ')
    .map(word => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
    .join(' ');
};

interface OnboardingPopupProps {
  onboardingStatus: any;
  isOpen: boolean;
  onClose: () => void;
}

export const UserProfileWidget: React.FC<UserProfileWidgetProps> = ({
  userName,
  userRole,
  simplified = false,
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [showOnboardingPopup, setShowOnboardingPopup] = useState(false);
  const router = useRouter();
  const { user } = useUser();
  const { notifications = [] } = useNotifications(user?.sub ?? '');
  const { userData } = useEnhancedUserData();
  const hasNotifications = notifications.length > 0;

  // Use the custom hook to get user profile data
  const {
    displayName,
    userRole: effectiveUserRole,
    isLoading: profileLoading,
  } = useUserProfileData();
  const formattedName = toTitleCase(displayName);

  const onboardingStatus = userData?.dashboardStats?.onboardingStatus;
  const completionPercentage = onboardingStatus?.overall?.percentage || 0;
  const isJobSeeker = effectiveUserRole?.toLowerCase() === UserRole.JOB_SEEKER;

  const dropdownRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  const handleSignOut = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    // Use the centralized logout function
    logoutUser();
  };

  const handleNotificationsClick = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setIsOpen(false);
    router.push('/notifications');
  };

  const handleProfileCompletion = () => {
    if (isJobSeeker && onboardingStatus) {
      setShowOnboardingPopup(true);
    }
  };

  // Get user initials for avatar fallback
  const getInitials = (name?: string) => {
    const nameToUse = name || displayName;
    if (!nameToUse) return 'G';
    return nameToUse
      .split(' ')
      .map(n => n[0])
      .join('')
      .toUpperCase();
  };

  return (
    <motion.div
      initial={{ opacity: 0, x: 20 }}
      animate={{ opacity: 1, x: 0 }}
      className="flex items-center"
    >
      {/* Notification Bell - Hide in simplified mode */}
      {!simplified && (
        <button
          type="button"
          onClick={handleNotificationsClick}
          className="relative p-2 rounded-full hover:bg-white/5 transition-colors"
          aria-label="Notifications"
        >
          <Bell className="w-5 h-5 text-white/80" />
          {hasNotifications && (
            <span className="absolute top-2 right-2 w-2 h-2 bg-purple-500 rounded-full animate-pulse"></span>
          )}
        </button>
      )}

      {/* User Profile */}
      <div className="relative" ref={dropdownRef}>
        <button
          type="button"
          onClick={() => setIsOpen(!isOpen)}
          className="flex items-center gap-3 hover:bg-white/5 p-2 rounded-full transition-colors"
          aria-label="User profile"
        >
          {/* User Avatar */}
          <div className="w-10 h-10 rounded-full bg-white/10 flex items-center justify-center overflow-hidden">
            {user?.picture ? (
              <Image
                src={user.picture}
                alt={formattedName}
                width={40}
                height={40}
                className="w-full h-full object-cover rounded-full"
              />
            ) : (
              <span className="text-white text-sm font-medium">{getInitials()}</span>
            )}
          </div>

          {/* User Info - Hide in simplified mode */}
          {!simplified && (
            <div className="flex flex-col items-start">
              <span className="text-base font-medium text-white">{formattedName}</span>
              <div className="flex items-center gap-1 text-white/60">
                {getRoleIcon(effectiveUserRole)}
                <span className="text-xs">{formatRole(effectiveUserRole)}</span>
              </div>
            </div>
          )}

          <ChevronDown
            className={`w-5 h-5 text-white/60 transition-transform duration-200 ${isOpen ? 'rotate-180' : ''}`}
          />
        </button>

        {/* Dropdown Menu */}
        {isOpen && (
          <motion.div
            initial={{ opacity: 0, y: 8, scale: 0.96 }}
            animate={{ opacity: 1, y: 0, scale: 1 }}
            exit={{ opacity: 0, y: 8, scale: 0.96 }}
            transition={{ duration: 0.15, ease: [0.23, 1, 0.32, 1] }}
            className="absolute right-0 mt-3 w-72 rounded-2xl bg-black/80 border border-white/10 shadow-2xl z-10 overflow-hidden"
            style={{
              backdropFilter: 'blur(20px) saturate(180%)',
              WebkitBackdropFilter: 'blur(20px) saturate(180%)',
            }}
          >
            {/* Glass effect layers */}
            <div className="absolute inset-0 bg-gradient-to-br from-white/5 via-transparent to-white/5 pointer-events-none" />
            <div className="absolute inset-0 bg-gradient-to-tr from-purple-500/10 via-transparent to-blue-500/10 pointer-events-none" />
            
            {/* User Header Section */}
            <div className="relative p-5 border-b border-white/10">
              <div className="absolute inset-0 bg-gradient-to-r from-purple-600/20 to-blue-600/20" />
              <div className="relative flex items-center gap-4">
                <div className="relative">
                  <div className="w-14 h-14 rounded-2xl bg-gradient-to-br from-purple-500 to-blue-500 p-0.5">
                    <div className="w-full h-full rounded-2xl bg-black/50 flex items-center justify-center overflow-hidden">
                      {user?.picture ? (
                        <Image
                          src={user.picture}
                          alt={formattedName}
                          width={56}
                          height={56}
                          className="w-full h-full object-cover"
                        />
                      ) : (
                        <span className="text-white font-semibold text-lg">{getInitials()}</span>
                      )}
                    </div>
                  </div>
                  {/* Status indicator */}
                  <div className="absolute -bottom-1 -right-1 w-4 h-4 bg-green-500 rounded-full border-2 border-black/40" />
                </div>
                <div className="flex-1">
                  <p className="text-white font-semibold text-base leading-tight">{formattedName}</p>
                  <div className="flex items-center gap-2 mt-1">
                    <div 
                      className="flex items-center gap-1.5 px-2.5 py-1 bg-white/10 rounded-full"
                      style={{
                        backdropFilter: 'blur(8px)',
                        WebkitBackdropFilter: 'blur(8px)',
                      }}
                    >
                      {getRoleIcon(effectiveUserRole)}
                      <span className="text-xs text-white/80 font-medium">
                        {formatRole(effectiveUserRole)}
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Menu Items */}
            <div className="p-2">
              {/* Notifications */}
              <button
                type="button"
                onClick={handleNotificationsClick}
                className="group w-full text-left px-4 py-3.5 rounded-xl transition-all duration-200 flex items-center gap-3 relative overflow-hidden hover:bg-white/10"
              >
                <div className="absolute inset-0 bg-gradient-to-r from-purple-500/0 via-purple-500/10 to-purple-500/0 translate-x-[-100%] group-hover:translate-x-[100%] transition-transform duration-700" />
                <div 
                  className="relative flex items-center justify-center w-10 h-10 rounded-xl bg-purple-500/20 group-hover:bg-purple-500/30 transition-colors"
                  style={{
                    backdropFilter: 'blur(8px)',
                    WebkitBackdropFilter: 'blur(8px)',
                  }}
                >
                  <Bell className="w-5 h-5 text-purple-300 group-hover:text-purple-200" />
                  {hasNotifications && (
                    <span className="absolute -top-1 -right-1 flex h-3 w-3">
                      <span className="animate-ping absolute inline-flex h-full w-full rounded-full bg-purple-400 opacity-75"></span>
                      <span className="relative inline-flex rounded-full h-3 w-3 bg-purple-500"></span>
                    </span>
                  )}
                </div>
                <div className="flex-1">
                  <span className="text-sm font-medium text-white/90 group-hover:text-white">Notifications</span>
                  {hasNotifications && (
                    <p className="text-xs text-white/50 mt-0.5">{notifications.length} unread</p>
                  )}
                </div>
                {hasNotifications && (
                  <div className="px-2.5 py-1 bg-purple-500/20 backdrop-blur-sm rounded-lg">
                    <span className="text-xs font-medium text-purple-300">{notifications.length}</span>
                  </div>
                )}
              </button>

              {/* Divider */}
              <div className="my-2 mx-4 h-px bg-white/10" />

              {/* Sign Out */}
              <button
                type="button"
                onClick={handleSignOut}
                className="group w-full text-left px-4 py-3.5 rounded-xl transition-all duration-200 flex items-center gap-3 relative overflow-hidden hover:bg-red-500/10"
              >
                <div className="absolute inset-0 bg-gradient-to-r from-red-500/0 via-red-500/10 to-red-500/0 translate-x-[-100%] group-hover:translate-x-[100%] transition-transform duration-700" />
                <div 
                  className="relative flex items-center justify-center w-10 h-10 rounded-xl bg-white/10 group-hover:bg-red-500/20 transition-colors"
                  style={{
                    backdropFilter: 'blur(8px)',
                    WebkitBackdropFilter: 'blur(8px)',
                  }}
                >
                  <LogOut className="w-5 h-5 text-white/70 group-hover:text-red-300" />
                </div>
                <span className="text-sm font-medium text-white/70 group-hover:text-white">Sign Out</span>
              </button>
            </div>
          </motion.div>
        )}
      </div>
    </motion.div>
  );
};
