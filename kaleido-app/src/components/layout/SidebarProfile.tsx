'use client';

import React, { useEffect, useMemo, useRef, useState } from 'react';
import { createPortal } from 'react-dom';

import { formatDistanceToNow } from 'date-fns';
import { AnimatePresence, motion } from 'framer-motion';
import { Bell, CheckCircle2, FileVideo, LogOut, Sparkles, User, Video } from 'lucide-react';
import Image from 'next/image';
import { useRouter } from 'next/navigation';

import { showVideoJDNotification } from '@/components/VideoJDNotification';
import { Notification } from '@/entities/Notification.entities';
import { useNotifications } from '@/hooks/useNotifications';
import { notificationsApi } from '@/lib/api/notifications';
import { useCurrentUser } from '@/services/user.service';
import { UserProfile } from '@auth0/nextjs-auth0/client';

import { logoutUser } from '@/lib/apiHelper';
import { getUserDisplayName } from '@/utils/getUserDisplayName';
import { FeedbackIcon } from './FeedbackIcon';

// @ts-nocheck

interface SidebarProfileProps {
  expanded: boolean;
  userData: UserProfile;
}

interface NotificationGroup {
  title: string;
  icon: React.ReactNode;
  notifications: Notification[];
}

export const SidebarProfile: React.FC<SidebarProfileProps> = ({ expanded, userData }) => {
  const [showNotifications, setShowNotifications] = useState(false);
  const [showProfile, setShowProfile] = useState(false);
  const [isMounted, setIsMounted] = useState(false);
  const notificationRef = useRef<HTMLDivElement>(null);
  const profileRef = useRef<HTMLDivElement>(null);
  const currentUser = useCurrentUser();
  const router = useRouter();

  useEffect(() => {
    setIsMounted(true);
  }, []);

  if (!currentUser) {
    return null;
  }

  // Helper function to get the first name using our utility
  const getFirstName = (fullName: string | undefined | null): string => {
    // Use our utility function to get a proper display name
    const displayName = getUserDisplayName({ name: fullName, ...userData });
    // Get just the first word/name from the display name
    const firstName = displayName.split(' ')[0];
    return firstName;
  };

  // Check if auth session exists in localStorage before making API calls
  const hasAuthSession = typeof window !== 'undefined' && !!localStorage.getItem('auth_session');
  const {
    notifications = [],
    isLoading,
    refetch,
  } = useNotifications(hasAuthSession ? (currentUser?.sub ?? '') : '');

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (notificationRef.current && !notificationRef.current.contains(event.target as Node)) {
        setShowNotifications(false);
      }
      if (profileRef.current && !profileRef.current.contains(event.target as Node)) {
        setShowProfile(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  const NotificationPopup = () => {
    const [popupPosition, setPopupPosition] = useState({ top: 0, left: 0 });
    const popupRef = useRef<HTMLDivElement>(null);
    const router = useRouter();

    // Group notifications by type
    const notificationGroups = useMemo(() => {
      const groups = {
        VIDEO_JD_GENERATING: {
          title: 'Video Job Descriptions',
          icon: <Video className="w-5 h-5 text-violet-400" />,
          notifications: [],
        },
        VIDEO_JD_READY: {
          title: 'Ready to View',
          icon: <CheckCircle2 className="w-5 h-5 text-emerald-400" />,
          notifications: [],
        },
        VIDEO_INTERVIEW_SUBMITTED: {
          title: 'Video Interviews',
          icon: <FileVideo className="w-5 h-5 text-teal-400" />,
          notifications: [],
        },
      };

      // @ts-ignore - Suppress unknown type error for notifications array
      notifications.forEach(notification => {
        const group = groups[notification.type];
        if (group) {
          group.notifications.push(notification);

          // Show toast notification for new VIDEO_JD_READY notifications
          if (notification.type === 'VIDEO_JD_READY' && !notification.read) {
            showVideoJDNotification({
              message: notification.message,
              // videoUrl: notification?.metadata?.videoUrl,
            });
          }
        }
      });

      // @ts-ignore - Suppress unknown type error for notifications.length dependency
      return Object.values(groups).filter(group => group.notifications.length > 0);
    }, [notifications]);

    useEffect(() => {
      if (popupRef.current && notificationRef.current) {
        const trigger = notificationRef.current.getBoundingClientRect();
        const popup = popupRef.current.getBoundingClientRect();
        const windowHeight = window.innerHeight;
        const windowWidth = window.innerWidth;

        if (trigger) {
          let topPosition = trigger.top;
          let leftPosition = trigger.right + 12; // 12px gap from the trigger

          // Adjust vertical position if needed
          if (topPosition < 20) topPosition = 20;
          if (topPosition + popup.height > windowHeight - 20) {
            topPosition = windowHeight - popup.height - 20;
          }

          // Adjust horizontal position if popup goes off screen
          if (leftPosition + 300 > windowWidth - 20) {
            // 300px is popup width
            leftPosition = trigger.left - 300 - 12; // Position to the left of trigger
          }

          setPopupPosition({ top: topPosition, left: leftPosition });
        }
      }
    }, [notifications.length, showNotifications]);

    const handleMarkAllAsRead = async () => {
      // Check if auth session exists before making API call
      if (!hasAuthSession) return;

      try {
        await notificationsApi.markAllAsRead(currentUser?.sub ?? '');
        refetch();
      } catch (error) {
        console.error('Error marking all as read:', error);
      }
    };

    const handleNotificationClick = async (notification: any) => {
      // Check if auth session exists before making API call
      if (!hasAuthSession) return;

      try {
        await notificationsApi.markAsRead(notification.id);
        refetch();

        if (notification.type === 'VIDEO_JD_READY' && notification.metadata?.jobId) {
          // Create a URL with query parameters
          const queryParams = new URLSearchParams({
            jobId: notification.metadata.jobId,
            showVideoJd: 'true',
            videoJdId: notification.metadata.videoJdId,
          }).toString();

          router.push(`/video-jd?${queryParams}`);
          setShowNotifications(false);
        }
      } catch (error) {
        console.error('Error handling notification:', error);
      }
    };

    const getNotificationBackground = (type: string) => {
      switch (type) {
        case 'VIDEO_JD_GENERATING':
          return 'from-violet-500/10 to-violet-500/5';
        case 'VIDEO_JD_READY':
          return 'from-emerald-500/10 to-emerald-500/5';
        case 'VIDEO_INTERVIEW_SUBMITTED':
          return 'from-teal-500/10 to-teal-500/5';
        default:
          return 'from-amber-500/10 to-amber-500/5';
      }
    };

    return isMounted
      ? createPortal(
          <>
            {/* Backdrop */}
            <div className="fixed inset-0 z-[9998]" onClick={() => setShowNotifications(false)} />

            {/* Notification popup */}
            <motion.div
              ref={popupRef}
              initial={{ opacity: 0, x: 10 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: 10 }}
              style={{
                position: 'fixed',
                top: popupPosition.top,
                left: popupPosition.left,
                zIndex: 9999,
              }}
              className="w-[300px] bg-gradient-to-b from-gray-900/80 to-gray-900/60 backdrop-blur-2xl rounded-xl shadow-2xl border border-white/10 overflow-visible"
              onClick={e => e.stopPropagation()}
            >
              <div className="relative bg-gray-900/20 backdrop-blur-xl rounded-xl overflow-hidden max-h-[80vh] overflow-y-auto">
                <div className="p-4 border-b border-white/10 bg-white/5 sticky top-0 z-10">
                  <div className="flex items-center justify-between">
                    <div>
                      <h3 className="text-white font-medium bg-gradient-to-r from-white to-white/70 bg-clip-text text-transparent">
                        Notifications
                      </h3>
                      <p className="text-white/70 text-sm">
                        {/* @ts-ignore - Suppress unknown type error for notifications.length */}
                        {notifications.length} unread notification
                        {notifications.length !== 1 ? 's' : ''}
                      </p>
                    </div>
                    {/* @ts-ignore - Suppress unknown type error for notifications.length */}
                    {notifications.length > 0 && (
                      <button
                        type="button"
                        onClick={handleMarkAllAsRead}
                        className="px-3 py-1.5 text-xs rounded-lg bg-white/10 hover:bg-white/20 text-white/70 hover:text-white transition-colors"
                      >
                        Mark all as read
                      </button>
                    )}
                  </div>
                </div>

                <div className="p-3 space-y-4">
                  {isLoading ? (
                    <div className="text-white/70 text-sm text-center py-4">
                      Loading notifications...
                    </div>
                  ) : /* @ts-ignore - Suppress unknown type error for notifications.length */ notifications.length ===
                    0 ? (
                    <div className="text-white/70 text-sm text-center py-4">
                      No new notifications
                    </div>
                  ) : (
                    notificationGroups.map(group => (
                      <div key={group.title} className="space-y-2">
                        <div className="flex items-center gap-2 pb-2">
                          <div
                            className={`p-1.5 rounded-lg bg-gradient-to-br ${getNotificationBackground(group.notifications[0].type)}`}
                          >
                            {group.icon}
                          </div>
                          <h4 className="text-sm font-medium text-white/90">{group.title}</h4>
                          <span className="px-2 py-0.5 text-xs rounded-full bg-white/10 text-white/70">
                            {group.notifications.length}
                          </span>
                        </div>

                        {group.notifications.slice(0, 3).map(notification => (
                          <div
                            key={notification.id}
                            onClick={() => handleNotificationClick(notification)}
                            className="bg-white/5 hover:bg-white/10 rounded-lg p-3 cursor-pointer transition-all"
                          >
                            <p className="text-sm text-white font-medium">{notification.title}</p>
                            <p className="text-xs text-white/70 mt-1">{notification.message}</p>
                            <p className="text-xs text-white/50 mt-1">
                              {formatDistanceToNow(new Date(notification.createdAt), {
                                addSuffix: true,
                              })}
                            </p>
                          </div>
                        ))}
                      </div>
                    ))
                  )}

                  {/* @ts-ignore - Suppress unknown type error for notifications.length */}
                  {notifications.length > 0 && (
                    <button
                      type="button"
                      onClick={() => {
                        setShowNotifications(false);
                        router.push('/notifications');
                      }}
                      className="w-full mt-2 px-3 py-2 text-xs text-white/70 hover:text-white
                        bg-white/5 hover:bg-white/10 rounded-lg transition-all duration-300
                        border border-white/10 hover:border-white/20"
                    >
                      {/* @ts-ignore - Suppress unknown type error for notifications.length */}
                      View All Notifications ({notifications.length})
                    </button>
                  )}
                </div>
              </div>
            </motion.div>
          </>,
          document.body
        )
      : null;
  };

  const ProfilePopup = () => {
    const [popupPosition, setPopupPosition] = useState(0);
    const popupRef = useRef<HTMLDivElement>(null);

    useEffect(() => {
      if (popupRef.current) {
        const trigger = profileRef.current?.getBoundingClientRect();
        const popup = popupRef.current.getBoundingClientRect();
        const windowHeight = window.innerHeight;

        if (trigger) {
          let topPosition = trigger.top + trigger.height / 2 - popup.height / 2;
          if (topPosition < 20) topPosition = 20;
          if (topPosition + popup.height > windowHeight - 20) {
            topPosition = windowHeight - popup.height - 20;
          }
          setPopupPosition(topPosition);
        }
      }
    }, []);

    return (
      <motion.div
        ref={popupRef}
        initial={{ opacity: 0, x: 10 }}
        animate={{ opacity: 1, x: 0 }}
        exit={{ opacity: 0, x: 10 }}
        style={{ top: popupPosition }}
        className="fixed w-[300px] bg-gradient-to-b from-gray-900/80 to-gray-900/60 backdrop-blur-2xl rounded-xl shadow-2xl border border-white/10 z-[100]"
        onClick={e => e.stopPropagation()}
      >
        <div className="relative bg-gray-900/10 rounded-xl overflow-hidden">
          <div className="p-4 border-b border-white/10 relative">
            <button
              type="button"
              onClick={e => {
                e.stopPropagation();
                setShowProfile(false);
              }}
              title="Close profile menu"
              aria-label="Close profile menu"
              className="absolute top-2 right-2 p-1 rounded-full hover:bg-white/10 text-white/70 hover:text-white transition-colors"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="20"
                height="20"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
              >
                <line x1="18" y1="6" x2="6" y2="18"></line>
                <line x1="6" y1="6" x2="18" y2="18"></line>
              </svg>
            </button>
            <div className="flex items-center">
              {userData.picture ? (
                <Image
                  src={userData.picture}
                  alt={getFirstName(userData.name) || 'User'}
                  width={48}
                  height={48}
                  className="rounded-full"
                />
              ) : (
                <div className="bg-blue-500/20 p-3 rounded-full">
                  <User className="h-6 w-6 text-white" />
                </div>
              )}
              <div className="ml-3">
                <p className="text-white font-medium">{getFirstName(userData.name)}</p>
                {/* <p className="text-sm text-white/70">{userData.email}</p> */}
              </div>
            </div>
          </div>

          <div className="p-2">
            {/* eslint-disable-next-line @next/next/no-html-link-for-pages */}
            <a
              href="/api/auth/logout"
              onClick={e => {
                e.preventDefault();
                e.stopPropagation();
                // Use the centralized logout function
                logoutUser();
              }}
              className="flex items-center p-2 text-white/70 hover:text-white hover:bg-white/10 rounded-lg transition-colors"
            >
              <LogOut className="h-5 w-5 mr-3" />
              <span>Sign Out</span>
            </a>
          </div>
        </div>
      </motion.div>
    );
  };

  // Handle sign out
  const handleSignOut = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    // Use the centralized logout function
    logoutUser();
  };

  return (
    <div className="mt-auto">
      {/* Feedback Section */}
      <div className="relative z-50">
        <FeedbackIcon expanded={expanded} />
      </div>

      {/* Notification Section */}
      <div className="relative z-50" ref={notificationRef}>
        <motion.div
          className="relative p-4 hover:bg-white/5 cursor-pointer transition-colors group"
          onClick={() => {
            setShowNotifications(!showNotifications);
            setShowProfile(false);
          }}
        >
          <div className="flex items-center">
            <div className="relative bg-blue-500/20 p-2 rounded-xl overflow-hidden">
              <motion.div
                animate={{
                  opacity: [0.3, 0.5, 0.3],
                  scale: [0.9, 1.1, 0.9],
                }}
                transition={{
                  duration: 3,
                  repeat: Infinity,
                  ease: 'easeInOut',
                }}
                className="absolute inset-0 bg-gradient-conic from-purple-600/30 via-blue-500/30 to-purple-500/30 rounded-xl blur-md"
              />

              <motion.div className="relative">
                <Bell
                  // @ts-ignore - Suppress unknown type error for notifications.length
                  className={`h-6 w-6 ${notifications.length > 0 ? 'text-white' : 'text-blue-400'}`}
                />

                {/* @ts-ignore - Suppress unknown type error for notifications.length */}
                {notifications.length > 0 && (
                  <motion.div
                    initial={{ scale: 0 }}
                    animate={{
                      scale: [1, 1.2, 1],
                      opacity: [1, 0.7, 1],
                    }}
                    transition={{
                      duration: 2,
                      repeat: Infinity,
                      ease: 'easeInOut',
                    }}
                    className="absolute -top-1.5 -right-1.5 flex items-center justify-center"
                  >
                    <span className="absolute w-3 h-3 bg-green-500/30 rounded-full animate-ping" />
                    <span className="relative h-2.5 w-2.5 bg-green-500 rounded-full" />
                  </motion.div>
                )}
              </motion.div>

              {/* @ts-ignore - Suppress unknown type error for notifications.length */}
              {notifications.length > 0 && (
                <motion.div
                  initial={{ opacity: 0, scale: 0 }}
                  animate={{
                    opacity: [0, 1, 0],
                    scale: [0.8, 1.2, 0.8],
                  }}
                  transition={{
                    duration: 2,
                    repeat: Infinity,
                    ease: 'easeInOut',
                  }}
                  className="absolute -top-1 -right-1"
                >
                  <Sparkles className="h-3 w-3 text-green-400" />
                </motion.div>
              )}
            </div>

            {expanded && (
              <motion.div initial={{ opacity: 0 }} animate={{ opacity: 1 }} className="ml-3 flex-1">
                <p className="text-sm font-medium text-white group-hover:text-white/90">
                  Notifications
                </p>
                <p className="text-xs text-white/70">
                  {/* @ts-ignore - Suppress unknown type error for notifications.length */}
                  {notifications.length > 0
                    ? `unread notification${notifications.length !== 1 ? 's' : ''}`
                    : 'No new notifications'}
                </p>
              </motion.div>
            )}
          </div>
        </motion.div>
      </div>

      {/* Notification Portal */}
      <AnimatePresence>{showNotifications && <NotificationPopup />}</AnimatePresence>

      {/* User Profile Section */}
      <div className="relative z-50" ref={profileRef}>
        <div
          className="relative p-4 bg-white/5 backdrop-blur-md border-t border-white/10 cursor-pointer"
          onClick={e => {
            e.stopPropagation();
            setShowProfile(!showProfile);
            setShowNotifications(false);
          }}
        >
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              {userData.picture ? (
                <Image
                  src={userData.picture}
                  alt={getFirstName(userData.name) || 'User'}
                  width={32}
                  height={32}
                  className="rounded-full"
                />
              ) : (
                <div className="bg-blue-500/20 p-2 rounded-full">
                  <User className="h-5 w-5 text-white" />
                </div>
              )}

              {expanded && (
                <motion.div
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  className="ml-3 flex-1"
                >
                  <p className="text-sm font-medium text-white">
                    {getUserDisplayName(userData)}
                  </p>
                  {/* <p className="text-xs text-white/70">{userData.email}</p> */}
                </motion.div>
              )}
            </div>

            {/* Separator */}
            <div className="h-8 w-px bg-white/10 mx-2"></div>

            {/* Sign out icon */}
            <button
              type="button"
              onClick={handleSignOut}
              className="p-2 rounded-lg hover:bg-purple-900/70 text-white/70 hover:text-white transition-colors"
              title="Sign Out"
            >
              <LogOut className="h-5 w-5" />
            </button>
          </div>
        </div>
        <AnimatePresence>
          {showProfile && (
            <div className="fixed inset-0 z-[100]">
              <ProfilePopup />
            </div>
          )}
        </AnimatePresence>
      </div>
    </div>
  );
};
