import { AlertCircle, CheckCircle, GitCompare, Users, XCircle } from 'lucide-react';
import { StatusJob, StatusManagerConfig } from '../GenericStatusManager';

export const createComparisonConfig = (): StatusManagerConfig => ({
  title: 'Candidate Comparison',
  icon: <GitCompare className="text-purple-600 h-6 w-6" />,
  statusEndpoint: (jobId: string) => `/comparisons/status/${jobId}`, // jobId here is the queue job ID (job.id)
  pollInterval: 3000,
  maxRetries: 10,

  // Enhanced features
  useStatusCompletionModal: true,
  action: 'comparison',
  actionDisplay: 'Candidate Comparison',
  enableNavigation: true,
  enableJobStoreIntegration: false, // Comparisons use their own store
  enableAutoRedirection: false,

  // Manager coordination
  managerId: 'comparison',
  enableManagerCoordination: true,
  autoCloseOnCompletion: false,
  showCloseButton: true,

  // Action buttons
  showViewResultsButton: true,
  viewResultsButtonText: 'View Comparison',
  onViewResults: (jobId: string, job: StatusJob) => {
    // Navigate to comparison results page
    // job.jobId is the comparison entity ID, job.id is the queue job ID
    const comparisonId = job.result?.comparisonId || job.jobId;
    window.location.href = `/comparisons/${comparisonId}`;
  },

  // Completion details configuration
  completionDetailsConfig: {
    action: 'comparison',
    renderDetails: (job: StatusJob) => (
      <div className="space-y-3">
        <div className="flex justify-between items-center">
          <span className="text-white/70">Status:</span>
          <div className="flex items-center gap-2">
            {job.status === 'completed' ? (
              <div className="flex items-center gap-2 text-green-400">
                <CheckCircle size={16} />
                <span className="font-medium">Completed</span>
              </div>
            ) : job.status === 'failed' ? (
              <div className="flex items-center gap-2 text-red-400">
                <XCircle size={16} />
                <span className="font-medium">Failed</span>
              </div>
            ) : (
              <div className="flex items-center gap-2 text-blue-400">
                <div className="relative w-4 h-4">
                  <div className="absolute inset-0 rounded-full border-2 border-blue-400 border-t-transparent animate-spin"></div>
                </div>
                <span className="font-medium">Processing</span>
              </div>
            )}
          </div>
        </div>

        {/* Progress bar for active jobs */}
        {job.status === 'processing' && job.progress !== undefined && (
          <div className="space-y-1">
            <div className="flex justify-between text-xs">
              <span className="text-white/70">Progress:</span>
              <span className="text-white">{Math.round(job.progress)}%</span>
            </div>
            <div className="w-full bg-gray-700 rounded-full h-2">
              <div
                className="bg-purple-500 h-2 rounded-full transition-all duration-300"
                style={{ width: `${job.progress}%` }}
              />
            </div>
          </div>
        )}

        {/* Comparison summary when completed */}
        {job.status === 'completed' && job.result && (
          <div className="mt-3 space-y-3">
            {/* Executive Summary */}
            {job.result.executiveSummary && (
              <div className="bg-black/20 p-3 rounded-lg">
                <h4 className="text-sm font-medium text-white mb-2">Executive Summary</h4>
                <p className="text-xs text-gray-300 leading-relaxed">
                  {job.result.executiveSummary}
                </p>
              </div>
            )}

            {/* Top Recommendation */}
            {job.result.recommendations?.topChoice && (
              <div className="bg-purple-900/20 border border-purple-500/30 p-3 rounded-lg">
                <div className="flex items-center gap-2 mb-2">
                  <Users size={14} className="text-purple-400" />
                  <span className="text-purple-300 text-sm font-medium">Recommended Candidate</span>
                </div>
                <p className="text-white font-medium text-sm mb-1">
                  {job.result.recommendations.topChoice.candidateName || 
                   job.result.recommendations.topChoice.candidateId}
                </p>
                <p className="text-xs text-gray-300">
                  {job.result.recommendations.topChoice.reasoning}
                </p>
              </div>
            )}

            {/* Comparison Type */}
            {job.result.comparisonType && (
              <div className="flex justify-between items-center">
                <span className="text-white/70 text-sm">Comparison Type:</span>
                <span className="text-white text-sm capitalize">
                  {job.result.comparisonType.replace(/_/g, ' ')}
                </span>
              </div>
            )}

            {/* Candidates Compared */}
            {job.result.candidateCount && (
              <div className="flex justify-between items-center">
                <span className="text-white/70 text-sm">Candidates Compared:</span>
                <span className="text-white text-sm">{job.result.candidateCount}</span>
              </div>
            )}
          </div>
        )}

        {/* Error details when failed */}
        {job.status === 'failed' && job.error && (
          <div className="mt-3 p-2 bg-red-900/20 border border-red-500/30 rounded-lg">
            <div className="flex items-center gap-2 mb-1">
              <AlertCircle size={14} className="text-red-400" />
              <span className="text-red-400 text-xs font-medium">Error Details</span>
            </div>
            <p className="text-white/90 text-xs">{job.error}</p>
          </div>
        )}

        {/* Additional message */}
        {job.message && job.message !== job.error && (
          <div className="mt-2 p-2 bg-gray-700/30 rounded-lg">
            <p className="text-gray-300 text-xs">{job.message}</p>
          </div>
        )}
      </div>
    ),
    renderCompletionStats: (job: StatusJob) => {
      if (job.status !== 'completed') return null;

      return (
        <div className="flex items-center justify-between mb-2">
          <div className="flex items-center gap-2">
            <GitCompare size={16} className="text-purple-400" />
            <span className="text-sm font-medium text-white">
              Comparison Complete
            </span>
          </div>
          {job.result?.comparisonId && (
            <button
              type="button"
              onClick={() => (window.location.href = `/comparisons/${job.result.comparisonId}`)}
              className="flex items-center gap-1 bg-purple-600/20 hover:bg-purple-600/30 text-purple-300 px-2 py-1 rounded text-xs transition-colors"
            >
              View Details
            </button>
          )}
        </div>
      );
    },
  },

  getActualJobId: (job: StatusJob) => job.result?.comparisonId || job.jobId || job.id,
});

export default createComparisonConfig;