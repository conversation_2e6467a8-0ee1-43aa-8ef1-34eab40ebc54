'use client';

import { motion } from 'framer-motion';
import { Bar<PERSON>hart3, Star, TrendingUp, Users } from 'lucide-react';
import React from 'react';
import { Bar, BarChart, CartesianGrid, ResponsiveContainer, Tooltip, XAxis, YAxis, Cell, RadarChart, PolarGrid, PolarAngleAxis, PolarRadiusAxis, Radar } from 'recharts';

interface ComparisonStatsProps {
  data?: any;
  candidateAnalysis?: any;
  metadata?: any;
  headToHeadComparisons?: any[];
}

const COLORS = {
  purple: '#8b5cf6',
  pink: '#ec4899',
  cyan: '#06b6d4',
  green: '#10b981',
  orange: '#f59e0b',
};

export const ComparisonStats: React.FC<ComparisonStatsProps> = ({
  data,
  candidateAnalysis,
  metadata,
  headToHeadComparisons,
}) => {
  if (!data || !candidateAnalysis) {
    return (
      <div className="text-center py-12">
        <BarChart3 className="w-16 h-16 mx-auto mb-4 text-gray-400" />
        <p className="text-white/60">No visualization data available</p>
      </div>
    );
  }

  // Get real candidate names
  const getCandidateInfo = (key: string) => {
    const candidateKey = key.toLowerCase().replace('candidate', '');
    const analysis = candidateAnalysis[key] || {};

    // Try to find the name from metadata
    let name = key;
    if (metadata?.candidateNames) {
      const found = metadata.candidateNames.find(
        (c: any) =>
          c.name === analysis.name ||
          key.includes(c.name) ||
          c.name.toLowerCase().includes(candidateKey)
      );
      if (found) name = found.name;
    }

    return { name, analysis };
  };

  // Prepare real data for charts
  const candidatesData = Object.keys(candidateAnalysis).map(key => {
    const { name, analysis } = getCandidateInfo(key);
    return {
      name,
      key,
      overallScore: analysis.scores
        ? Math.round(
            (analysis.scores.skills +
              analysis.scores.experience +
              analysis.scores.leadership +
              analysis.scores.culturalFit +
              analysis.scores.availability) /
              5
          )
        : 0,
      ...analysis.scores,
    };
  });

  // Sort by overall score
  candidatesData.sort((a, b) => b.overallScore - a.overallScore);

  // Prepare radar data from real scores
  const radarData = [
    {
      metric: 'Skills',
      ...candidatesData.reduce((acc, c) => ({ ...acc, [c.name]: c.skills || 0 }), {}),
    },
    {
      metric: 'Experience',
      ...candidatesData.reduce((acc, c) => ({ ...acc, [c.name]: c.experience || 0 }), {}),
    },
    {
      metric: 'Leadership',
      ...candidatesData.reduce((acc, c) => ({ ...acc, [c.name]: c.leadership || 0 }), {}),
    },
    {
      metric: 'Culture Fit',
      ...candidatesData.reduce((acc, c) => ({ ...acc, [c.name]: c.culturalFit || 0 }), {}),
    },
    {
      metric: 'Availability',
      ...candidatesData.reduce((acc, c) => ({ ...acc, [c.name]: c.availability || 0 }), {}),
    },
  ];

  // Create comparison table data
  const comparisonTableData = candidatesData.map(candidate => {
    const analysis = candidateAnalysis[candidate.key];
    return {
      name: candidate.name,
      overallScore: candidate.overallScore,
      rank: analysis.overallRank || 0,
      strengths: analysis.strengths || [],
      weaknesses: analysis.weaknesses || [],
      keyDifferentiator: analysis.keyDifferentiator || '',
      riskFactors: analysis.riskFactors || [],
      uniqueAdvantages: analysis.uniqueAdvantages || [],
    };
  });

  // Prepare detailed metrics for better visualization
  const metricsData = candidatesData.map(candidate => {
    const analysis = candidateAnalysis[candidate.key];
    return {
      name: candidate.name,
      overallScore: candidate.overallScore,
      skills: candidate.skills || 0,
      experience: candidate.experience || 0,
      leadership: candidate.leadership || 0,
      culturalFit: candidate.culturalFit || 0,
      availability: candidate.availability || 0,
    };
  });

  // Create grouped bar chart data
  const groupedMetrics = ['Skills', 'Experience', 'Leadership', 'Culture Fit', 'Availability'].map(
    metric => {
      const dataKey = metric === 'Culture Fit' ? 'culturalFit' : metric.toLowerCase();
      return {
        metric,
        ...candidatesData.reduce(
          (acc, candidate) => ({
            ...acc,
            [candidate.name]: candidate[dataKey] || 0,
          }),
          {}
        ),
      };
    }
  );

  return (
    <div className="space-y-3">
      {/* Summary Statistics */}
      <motion.div
        initial={{ opacity: 0, y: 10 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3 }}
        className="grid grid-cols-2 md:grid-cols-4 gap-2"
      >
        <div className="bg-gradient-to-br from-purple-600/20 to-pink-600/10 rounded-lg p-2.5 border border-purple-400/20">
          <div className="flex items-center gap-2 mb-1">
            <Star className="w-4 h-4 text-purple-400" />
            <div className="text-lg font-bold text-white">{candidatesData[0]?.overallScore || 0}%</div>
          </div>
          <div className="text-xs text-white/70">Top: {candidatesData[0]?.name}</div>
        </div>

        <div className="bg-gradient-to-br from-blue-600/20 to-cyan-600/10 rounded-lg p-2.5 border border-blue-400/20">
          <div className="flex items-center gap-2 mb-1">
            <TrendingUp className="w-4 h-4 text-blue-400" />
            <div className="text-lg font-bold text-white">
              {Math.round(
                candidatesData.reduce((acc, c) => acc + c.overallScore, 0) / candidatesData.length
              )}%
            </div>
          </div>
          <div className="text-xs text-white/70">Average</div>
        </div>

        <div className="bg-gradient-to-br from-green-600/20 to-emerald-600/10 rounded-lg p-2.5 border border-green-400/20">
          <div className="flex items-center gap-2 mb-1">
            <Users className="w-4 h-4 text-green-400" />
            <div className="text-lg font-bold text-white">{candidatesData.length}</div>
          </div>
          <div className="text-xs text-white/70">Candidates</div>
        </div>

        <div className="bg-gradient-to-br from-orange-600/20 to-amber-600/10 rounded-lg p-2.5 border border-orange-400/20">
          <div className="flex items-center gap-2 mb-1">
            <BarChart3 className="w-4 h-4 text-orange-400" />
            <div className="text-lg font-bold text-white">
              {candidatesData[0]?.overallScore -
                candidatesData[candidatesData.length - 1]?.overallScore || 0}%
            </div>
          </div>
          <div className="text-xs text-white/70">Gap</div>
        </div>
      </motion.div>

      {/* Compact Visualizations Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
        {/* Overall Scores Comparison */}
        <motion.div
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.3 }}
          className="bg-white/5 backdrop-blur-sm rounded-lg border border-white/10 p-3"
        >
          <h4 className="text-xs font-semibold mb-2 text-white flex items-center gap-1.5">
            <BarChart3 className="w-3.5 h-3.5 text-purple-400" />
            Overall Scores
          </h4>
          <ResponsiveContainer width="100%" height={120}>
            <BarChart data={candidatesData} margin={{ top: 5, right: 10, left: 10, bottom: 20 }}>
              <XAxis dataKey="name" stroke="#fff" tick={{ fontSize: 9 }} angle={-20} textAnchor="end" />
              <YAxis hide domain={[0, 100]} />
              <Tooltip
                contentStyle={{
                  backgroundColor: 'rgba(30, 41, 59, 0.95)',
                  border: '1px solid rgba(255,255,255,0.2)',
                  borderRadius: '6px',
                  fontSize: '12px',
                  padding: '8px',
                }}
                formatter={(value: any) => [`${value}%`, 'Score']}
              />
              <Bar dataKey="overallScore" radius={[4, 4, 0, 0]}>
                {candidatesData.map((entry, index) => (
                  <Cell key={`cell-${index}`} fill={Object.values(COLORS)[index % Object.values(COLORS).length]} />
                ))}
              </Bar>
            </BarChart>
          </ResponsiveContainer>
        </motion.div>

        {/* Skills Radar */}
        <motion.div
          initial={{ opacity: 0, x: 20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.3 }}
          className="bg-white/5 backdrop-blur-sm rounded-lg border border-white/10 p-3"
        >
          <h4 className="text-xs font-semibold mb-2 text-white flex items-center gap-1.5">
            <TrendingUp className="w-3.5 h-3.5 text-purple-400" />
            Skills Comparison
          </h4>
          <ResponsiveContainer width="100%" height={120}>
            <RadarChart data={radarData}>
              <PolarGrid stroke="rgba(255,255,255,0.1)" />
              <PolarAngleAxis dataKey="metric" stroke="#fff" tick={{ fontSize: 8 }} />
              <PolarRadiusAxis stroke="#fff" domain={[0, 100]} tick={false} />
              {candidatesData.map((candidate, index) => (
                <Radar
                  key={candidate.name}
                  name={candidate.name}
                  dataKey={candidate.name}
                  stroke={Object.values(COLORS)[index % Object.values(COLORS).length]}
                  fill={Object.values(COLORS)[index % Object.values(COLORS).length]}
                  fillOpacity={0.2}
                  strokeWidth={1.5}
                />
              ))}
            </RadarChart>
          </ResponsiveContainer>
        </motion.div>
      </div>

      {/* Compact Legend */}
      <div className="flex flex-wrap justify-center gap-3 px-3">
        {candidatesData.map((candidate, index) => (
          <div key={candidate.name} className="flex items-center gap-1.5">
            <div
              className="w-2.5 h-2.5 rounded-full"
              style={{ backgroundColor: Object.values(COLORS)[index % Object.values(COLORS).length] }}
            />
            <span className="text-xs text-white/80">{candidate.name}</span>
          </div>
        ))}
      </div>
    </div>
  );
};
