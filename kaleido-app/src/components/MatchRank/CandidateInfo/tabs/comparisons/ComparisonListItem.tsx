'use client';

import React from 'react';
import { motion } from 'framer-motion';
import { GitCompare, Calendar, Users, ChevronRight, Clock } from 'lucide-react';
import { format } from 'date-fns';

interface ComparisonListItemProps {
  comparison: {
    id: string;
    comparisonTitle: string;
    comparisonType: string;
    status: 'pending' | 'completed' | 'failed';
    createdAt: string;
    candidateIds: string[];
    comparisonResults?: {
      executiveSummary?: string;
      summary?: string;
    };
  };
  onViewDetails: () => void;
}

const typeColors = {
  'quick_overview': 'text-blue-600 bg-blue-100',
  'skills_deep_dive': 'text-purple-600 bg-purple-100',
  'culture_fit': 'text-green-600 bg-green-100',
  'experience_comparison': 'text-orange-600 bg-orange-100',
  'leadership_potential': 'text-indigo-600 bg-indigo-100',
  'custom': 'text-gray-600 bg-gray-100'
};

export const ComparisonListItem: React.FC<ComparisonListItemProps> = ({ 
  comparison, 
  onViewDetails 
}) => {
  const typeColor = typeColors[comparison.comparisonType as keyof typeof typeColors] || typeColors.custom;

  return (
    <motion.div
      className="group relative bg-card rounded-lg border border-border hover:border-purple-300 transition-all duration-200 cursor-pointer"
      onClick={onViewDetails}
      whileHover={{ x: 4 }}
    >
      <div className="p-4">
        <div className="flex items-start gap-4">
          {/* Icon */}
          <div className="flex-shrink-0">
            <div className="p-2 rounded-lg bg-gradient-to-br from-purple-500/10 to-pink-500/10">
              <GitCompare className="w-5 h-5 text-purple-600" />
            </div>
          </div>

          {/* Content */}
          <div className="flex-1 min-w-0">
            <div className="flex items-start justify-between gap-4 mb-2">
              <div>
                <h4 className="font-semibold text-foreground group-hover:text-purple-600 transition-colors">
                  {comparison.comparisonTitle}
                </h4>
                <div className="flex items-center gap-3 mt-1 text-sm text-muted-foreground">
                  <span className="flex items-center gap-1">
                    <Calendar className="w-3.5 h-3.5" />
                    {format(new Date(comparison.createdAt), 'MMM dd, yyyy')}
                  </span>
                  <span className="flex items-center gap-1">
                    <Clock className="w-3.5 h-3.5" />
                    {format(new Date(comparison.createdAt), 'h:mm a')}
                  </span>
                  <span className="flex items-center gap-1">
                    <Users className="w-3.5 h-3.5" />
                    {comparison.candidateIds.length} candidates
                  </span>
                </div>
              </div>

              {/* Arrow */}
              <ChevronRight className="w-5 h-5 text-gray-400 group-hover:text-purple-600 transition-colors flex-shrink-0" />
            </div>

            {/* Summary */}
            {comparison.comparisonResults?.executiveSummary && (
              <p className="text-sm text-muted-foreground line-clamp-2 mb-2">
                {comparison.comparisonResults.executiveSummary}
              </p>
            )}

            {/* Footer */}
            <div className="flex items-center gap-3">
              {/* Type Badge */}
              <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${typeColor}`}>
                {comparison.comparisonType.replace(/_/g, ' ')}
              </span>

              {/* Status Badge */}
              <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                comparison.status === 'completed' 
                  ? 'text-green-700 bg-green-100' 
                  : comparison.status === 'pending' 
                  ? 'text-yellow-700 bg-yellow-100' 
                  : 'text-red-700 bg-red-100'
              }`}>
                {comparison.status === 'pending' && (
                  <span className="w-1.5 h-1.5 rounded-full bg-current animate-pulse mr-1" />
                )}
                {comparison.status}
              </span>
            </div>
          </div>
        </div>
      </div>

      {/* Hover effect gradient */}
      <div className="absolute inset-0 bg-gradient-to-r from-purple-500/0 via-purple-500/0 to-purple-500/5 opacity-0 group-hover:opacity-100 transition-opacity pointer-events-none rounded-lg" />
    </motion.div>
  );
};