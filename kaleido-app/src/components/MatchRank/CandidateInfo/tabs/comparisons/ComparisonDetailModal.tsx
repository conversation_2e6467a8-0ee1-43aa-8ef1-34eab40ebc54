'use client';

import { AnimatePresence, motion } from 'framer-motion';
import {
  AlertCircle,
  ArrowDownRight,
  ArrowUpRight,
  Award,
  BarChart3,
  Briefcase,
  Calendar,
  CheckCircle,
  FileText,
  GitCompare,
  Lightbulb,
  Quote,
  Shield,
  Target,
  TrendingUp,
  Users,
  X,
  Zap,
} from 'lucide-react';
import React, { useState } from 'react';
import { ComparisonStats } from './ComparisonStats';
import { DetailedAnalysis } from './DetailedAnalysis';

interface ComparisonDetailModalProps {
  isOpen: boolean;
  onClose: () => void;
  comparison: {
    id: string;
    comparisonTitle: string;
    comparisonType: string;
    status: string;
    createdAt: string;
    candidateIds: string[];
    comparisonResults?: {
      summary: string;
      executiveSummary?: string;
      detailedComparison?: {
        [candidateId: string]: any;
      };
      candidateAnalysis?: {
        [candidateId: string]: any;
      };
      recommendations?: any;
      tradeOffs?: Array<{
        candidateA: string;
        candidateB: string;
        comparison: string;
      }>;
      headToHeadComparisons?: Array<{
        candidate1: string;
        candidate2: string;
        keyDifference: string;
        recommendation: string;
      }>;
      criticalConsiderations?: string[];
    };
    visualizationData?: any;
  };
}

export const ComparisonDetailModal: React.FC<ComparisonDetailModalProps> = ({
  isOpen,
  onClose,
  comparison,
}) => {
  const [activeSection, setActiveSection] = useState('summary');

  const sections = [
    { id: 'summary', label: 'Executive Summary', icon: FileText },
    { id: 'analysis', label: 'Detailed Analysis', icon: Target },
    { id: 'candidates', label: 'Candidate Profiles', icon: Users },
    { id: 'visualizations', label: 'Visual Insights', icon: BarChart3 },
    { id: 'recommendations', label: 'Recommendations', icon: TrendingUp },
  ];

  return (
    <AnimatePresence>
      {isOpen && (
        <>
          {/* Full screen backdrop without blur */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black/80 z-50"
            onClick={onClose}
          />

          {/* Modal sliding from bottom */}
          <motion.div
            initial={{ y: '100%', opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            exit={{ y: '100%', opacity: 0 }}
            transition={{
              type: 'spring',
              damping: 25,
              stiffness: 300,
              opacity: { duration: 0.3 },
            }}
            className="fixed inset-x-0 bottom-0 max-h-[85vh] bg-gradient-to-r from-slate-900/95 via-purple-950/95 to-slate-900/95 backdrop-blur-xl rounded-t-3xl z-50 overflow-hidden flex flex-col border-t border-purple-400/20"
            style={{
              boxShadow:
                '0 -20px 50px -20px rgba(168, 85, 247, 0.5), 0 -10px 20px -10px rgba(168, 85, 247, 0.3)',
              minHeight: '70vh',
              height: 'auto',
              transition: 'height 0.3s ease-in-out',
            }}
          >
            {/* Enhanced Header with Image Background */}
            <div className="relative flex-shrink-0">
              {/* Gradient shadows emitting from edges */}
              <div className="absolute inset-0 bg-gradient-to-r from-purple-600/40 via-transparent to-purple-600/40 blur-3xl transform scale-110" />
              <div className="absolute inset-0 bg-gradient-to-r from-purple-500/30 via-transparent to-purple-500/30 blur-2xl" />

              {/* Background image with overlay */}
              <div className="relative h-64 overflow-hidden">
                <div
                  className="absolute inset-0 bg-cover bg-center"
                  style={{
                    backgroundImage: `url('/images/landing/exclusive-2.webp')`,
                  }}
                />
                <div className="absolute inset-0 bg-gradient-to-t from-slate-900 via-slate-900/80 to-transparent" />
                <div className="absolute inset-0 bg-gradient-to-r from-purple-600/20 via-transparent to-pink-600/20" />

                {/* Content over image */}
                <div className="relative z-10 h-full flex flex-col justify-between p-6">
                  <div className="flex justify-end">
                    <button
                      onClick={onClose}
                      className="p-2 rounded-full bg-white/10 backdrop-blur-md hover:bg-white/20 transition-all border border-white/20"
                    >
                      <X className="w-5 h-5 text-white" />
                    </button>
                  </div>

                  <div className="pb-20">
                    <h2 className="text-2xl sm:text-3xl font-bold text-white tracking-tight mb-2">
                      {comparison.comparisonTitle
                        .split(' ')
                        .map(word => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
                        .join(' ')}
                    </h2>
                    <div className="flex items-center gap-3 text-sm text-white/70">
                      <div className="flex items-center gap-1.5">
                        <Users className="w-4 h-4 text-purple-400" />
                        <span>{comparison.candidateIds.length} candidates</span>
                      </div>
                      <span className="text-white/40">•</span>
                      <div className="flex items-center gap-1.5">
                        <Calendar className="w-4 h-4 text-blue-400" />
                        <span>
                          {new Date(comparison.createdAt).toLocaleDateString('en-US', {
                            month: 'short',
                            day: 'numeric',
                            year: 'numeric',
                          })}
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Section Tabs - Similar to UnifiedJobView */}
              <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-slate-900 via-slate-900/80 to-transparent pt-8">
                <div className="px-6 pb-0 flex gap-0 overflow-x-auto [&::-webkit-scrollbar]:hidden [-ms-overflow-style:none] [scrollbar-width:none]">
                  {sections.map((section, index) => (
                    <React.Fragment key={section.id}>
                      {index > 0 && (
                        <div className="w-px bg-white/10 self-center h-6 hidden sm:block" />
                      )}
                      <button
                        onClick={() => setActiveSection(section.id)}
                        className={`
                          relative px-3 sm:px-4 h-12 flex items-center gap-1 sm:gap-2 text-xs sm:text-sm font-medium transition-all whitespace-nowrap flex-shrink-0
                          ${
                            activeSection === section.id
                              ? 'text-white'
                              : 'text-white/60 hover:text-white/90 hover:bg-white/5'
                          }
                        `}
                      >
                        {/* Gradient background for active tab */}
                        {activeSection === section.id && (
                          <div className="absolute inset-0 bg-gradient-to-b from-transparent via-purple-600/20 to-pink-600/30" />
                        )}

                        {/* Content */}
                        <span className="relative z-10 flex items-center gap-1 sm:gap-2">
                          <section.icon className="w-3.5 sm:w-4 h-3.5 sm:h-4" />
                          <span className="hidden sm:inline">{section.label}</span>
                          <span className="sm:hidden">
                            {section.label
                              .split(' ')
                              .map(word => word[0])
                              .join('')}
                          </span>
                        </span>

                        {/* Bottom border for active tab */}
                        {activeSection === section.id && (
                          <div className="absolute bottom-0 left-0 right-0 h-[2px] bg-gradient-to-r from-purple-600 to-pink-600" />
                        )}
                      </button>
                    </React.Fragment>
                  ))}
                </div>
              </div>
            </div>

            {/* Content */}
            <motion.div
              className="flex-1 overflow-y-auto p-6 bg-slate-900/50"
              animate={{ height: 'auto' }}
              transition={{ duration: 0.3, ease: 'easeInOut' }}
            >
              <AnimatePresence mode="wait">
                {activeSection === 'summary' && (
                  <motion.div
                    key="summary"
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    exit={{ opacity: 0, x: 20 }}
                    className="space-y-4"
                  >
                    {/* Executive Summary in Quote Style */}
                    <div className="relative bg-gradient-to-br from-purple-600/10 via-purple-500/5 to-pink-600/10 backdrop-blur-sm rounded-xl p-6 overflow-hidden">
                      {/* Background decoration */}
                      <div className="absolute top-0 right-0 w-32 h-32 bg-purple-500/10 rounded-full blur-3xl" />
                      <div className="absolute bottom-0 left-0 w-32 h-32 bg-pink-500/10 rounded-full blur-3xl" />

                      {/* Quote icon */}
                      <Quote className="absolute top-4 left-4 w-8 h-8 text-purple-400/20 rotate-180" />

                      <div className="relative z-10">
                        <h3 className="text-lg font-semibold mb-4 text-white">Executive Summary</h3>
                        <blockquote className="text-lg text-white/90 leading-relaxed">
                          "
                          {comparison.comparisonResults?.executiveSummary || 'No summary available'}
                          "
                        </blockquote>
                      </div>

                      {/* Bottom quote icon */}
                      <Quote className="absolute bottom-4 right-4 w-8 h-8 text-purple-400/20" />
                    </div>

                    {/* Key Findings - Card Style */}
                    {(comparison.comparisonResults?.criticalConsiderations ||
                      comparison.comparisonResults?.headToHeadComparisons) && (
                      <div className="grid grid-cols-1 lg:grid-cols-2 gap-3">
                        {/* Critical Considerations Card */}
                        {comparison.comparisonResults?.criticalConsiderations && (
                          <motion.div
                            initial={{ opacity: 0, y: 10 }}
                            animate={{ opacity: 1, y: 0 }}
                            transition={{ delay: 0.1 }}
                            className="relative bg-gradient-to-br from-white/5 to-white/[0.02] backdrop-blur-md rounded-xl border border-white/5 p-5 overflow-hidden"
                          >
                            {/* Subtle glow effect */}
                            <div className="absolute inset-0 bg-gradient-to-br from-blue-500/5 to-transparent" />
                            <div className="relative z-10">
                              <div className="flex items-center gap-3 mb-4">
                                <div className="w-10 h-10 rounded-xl bg-gradient-to-br from-blue-500/20 to-cyan-500/10 backdrop-blur-sm flex items-center justify-center border border-blue-400/20">
                                  <AlertCircle className="w-5 h-5 text-blue-400" />
                                </div>
                                <h4 className="text-base font-semibold text-white/90">
                                  Critical Considerations
                                </h4>
                              </div>
                              <div className="space-y-2.5">
                                {comparison.comparisonResults.criticalConsiderations
                                  .slice(0, 3)
                                  .map((item: any, idx: number) => (
                                    <div key={idx} className="flex items-start gap-2.5">
                                      <div className="w-1 h-1 rounded-full bg-blue-400 mt-1.5 flex-shrink-0" />
                                      <span className="text-xs text-white/70 leading-relaxed">
                                        {item}
                                      </span>
                                    </div>
                                  ))}
                              </div>
                            </div>
                          </motion.div>
                        )}

                        {/* Key Differences Card */}
                        {comparison.comparisonResults?.headToHeadComparisons &&
                          comparison.comparisonResults.headToHeadComparisons.length > 0 && (
                            <motion.div
                              initial={{ opacity: 0, y: 10 }}
                              animate={{ opacity: 1, y: 0 }}
                              transition={{ delay: 0.2 }}
                              className="relative bg-gradient-to-br from-white/5 to-white/[0.02] backdrop-blur-md rounded-xl border border-white/5 p-5 overflow-hidden"
                            >
                              {/* Subtle glow effect */}
                              <div className="absolute inset-0 bg-gradient-to-br from-purple-500/5 to-transparent" />
                              <div className="relative z-10">
                                <div className="flex items-center gap-3 mb-4">
                                  <div className="w-10 h-10 rounded-xl bg-gradient-to-br from-purple-500/20 to-pink-500/10 backdrop-blur-sm flex items-center justify-center border border-purple-400/20">
                                    <GitCompare className="w-5 h-5 text-purple-400" />
                                  </div>
                                  <h4 className="text-base font-semibold text-white/90">
                                    Key Differences
                                  </h4>
                                </div>
                                <div className="space-y-2.5">
                                  {comparison.comparisonResults.headToHeadComparisons
                                    .slice(0, 2)
                                    .map((item: any, idx: number) => (
                                      <div key={idx} className="flex items-start gap-2.5">
                                        <div className="w-1 h-1 rounded-full bg-purple-400 mt-1.5 flex-shrink-0" />
                                        <p className="text-xs text-white/70 leading-relaxed">
                                          {item.keyDifference}
                                        </p>
                                      </div>
                                    ))}
                                </div>
                              </div>
                            </motion.div>
                          )}
                      </div>
                    )}
                  </motion.div>
                )}

                {activeSection === 'analysis' && (
                  <motion.div
                    key="analysis"
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    exit={{ opacity: 0, x: 20 }}
                    className="space-y-6"
                  >
                    <DetailedAnalysis
                      candidateAnalysis={comparison.comparisonResults?.candidateAnalysis}
                      metadata={undefined}
                      headToHeadComparisons={comparison.comparisonResults?.headToHeadComparisons}
                      recommendations={comparison.comparisonResults?.recommendations}
                    />
                  </motion.div>
                )}

                {activeSection === 'candidates' && (
                  <motion.div
                    key="candidates"
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    exit={{ opacity: 0, x: 20 }}
                    className="space-y-6"
                  >
                    <h3 className="text-xl font-semibold mb-6 text-white flex items-center gap-3">
                      <Users className="w-6 h-6 text-purple-400" />
                      Detailed Comparison
                    </h3>

                    {comparison.comparisonResults?.candidateAnalysis ? (
                      <div className="grid grid-cols-1 lg:grid-cols-3 gap-4">
                        {(() => {
                          // Get candidate info and scores
                          const getCandidateInfo = (key: string) => {
                            const analysis =
                              comparison.comparisonResults.candidateAnalysis[key] || {};
                            const name = key;
                            
                            // Check if the key is a UUID (candidate ID)
                            const isUuid = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(key);
                            
                            // Skip metadata-based name resolution for now
                            // since metadata is not available
                            
                            // If key is already a name (not UUID or pattern), use it as is
                            return { name, analysis };
                          };

                          const candidatesData = Object.keys(
                            comparison.comparisonResults.candidateAnalysis
                          )
                            .map((key, index) => {
                              const { name, analysis } = getCandidateInfo(key);
                              const scores = analysis.scores || {};
                              const overallScore =
                                scores.skills &&
                                scores.experience &&
                                scores.leadership &&
                                scores.culturalFit &&
                                scores.availability
                                  ? Math.round(
                                      (scores.skills +
                                        scores.experience +
                                        scores.leadership +
                                        scores.culturalFit +
                                        scores.availability) /
                                        5
                                    )
                                  : 0;

                              return {
                                name,
                                key,
                                overallScore,
                                rank: analysis.overallRank || index + 1,
                                keyDifferentiator: analysis.keyDifferentiator || '',
                                strengths: analysis.strengths || [],
                                weaknesses: analysis.weaknesses || [],
                                riskFactors: analysis.riskFactors || [],
                              };
                            })
                            .sort((a, b) => a.rank - b.rank);

                          const COLORS = ['#3b82f6', '#06b6d4', '#8b5cf6', '#ec4899', '#f59e0b'];

                          return candidatesData.map((candidate, index) => (
                            <motion.div
                              key={candidate.name}
                              initial={{ opacity: 0, y: 10 }}
                              animate={{ opacity: 1, y: 0 }}
                              transition={{ delay: index * 0.1 }}
                              className="bg-gradient-to-br from-white/10 to-white/5 backdrop-blur-sm border border-white/10 rounded-xl p-5 space-y-3 flex flex-col h-full"
                            >
                              {/* Header */}
                              <div className="flex items-center justify-between">
                                <div className="flex items-center gap-4">
                                  <div
                                    className="w-12 h-12 rounded-full flex items-center justify-center text-white font-bold text-lg"
                                    style={{ backgroundColor: COLORS[index % COLORS.length] }}
                                  >
                                    {candidate.rank}
                                  </div>
                                  <div>
                                    <h4 className="text-lg font-bold text-white">
                                      {candidate.name}
                                    </h4>
                                    <p className="text-xs text-white/70 mt-0.5">
                                      Overall Score: {candidate.overallScore}%
                                    </p>
                                  </div>
                                </div>
                                {candidate.rank === 1 && (
                                  <Award className="w-6 h-6 text-yellow-400" />
                                )}
                              </div>

                              {/* Key Differentiator */}
                              {candidate.keyDifferentiator && (
                                <div className="flex items-start gap-3 bg-purple-500/10 rounded-lg p-3 border border-purple-400/20">
                                  <Zap className="w-5 h-5 text-purple-400 flex-shrink-0 mt-0.5" />
                                  <div>
                                    <p className="text-sm font-medium text-purple-300 mb-1">
                                      Key Differentiator
                                    </p>
                                    <p className="text-sm text-white/80">
                                      {candidate.keyDifferentiator}
                                    </p>
                                  </div>
                                </div>
                              )}

                              {/* Strengths & Weaknesses */}
                              <div className="flex-1 space-y-4">
                                {/* Strengths */}
                                <div className="space-y-2">
                                  <p className="text-sm font-semibold text-green-400 flex items-center gap-2">
                                    <CheckCircle className="w-4 h-4" />
                                    Strengths
                                  </p>
                                  <ul className="space-y-2">
                                    {candidate.strengths.map((strength: string, i: number) => (
                                      <li
                                        key={i}
                                        className="text-sm text-white/80 flex items-start gap-2"
                                      >
                                        <ArrowUpRight className="w-4 h-4 text-green-400 flex-shrink-0 mt-0.5" />
                                        <span>{strength}</span>
                                      </li>
                                    ))}
                                  </ul>
                                </div>

                                {/* Weaknesses */}
                                <div className="space-y-2">
                                  <p className="text-sm font-semibold text-orange-400 flex items-center gap-2">
                                    <AlertCircle className="w-4 h-4" />
                                    Areas to Improve
                                  </p>
                                  <ul className="space-y-2">
                                    {candidate.weaknesses.map((weakness: string, i: number) => (
                                      <li
                                        key={i}
                                        className="text-sm text-white/80 flex items-start gap-2"
                                      >
                                        <ArrowDownRight className="w-4 h-4 text-orange-400 flex-shrink-0 mt-0.5" />
                                        <span>{weakness}</span>
                                      </li>
                                    ))}
                                  </ul>
                                </div>
                              </div>

                              {/* Risk Factors */}
                              {candidate.riskFactors.length > 0 && (
                                <div className="pt-4 border-t border-white/10">
                                  <p className="text-sm font-semibold text-red-400 flex items-center gap-2 mb-2">
                                    <Shield className="w-4 h-4" />
                                    Risk Factors
                                  </p>
                                  <ul className="space-y-1">
                                    {candidate.riskFactors.map((risk: string, i: number) => (
                                      <li key={i} className="text-sm text-white/70">
                                        {risk}
                                      </li>
                                    ))}
                                  </ul>
                                </div>
                              )}
                            </motion.div>
                          ));
                        })()}
                      </div>
                    ) : (
                      <p className="text-white/60">No candidate profile data available</p>
                    )}
                  </motion.div>
                )}

                {activeSection === 'visualizations' && (
                  <motion.div
                    key="visualizations"
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    exit={{ opacity: 0, x: 20 }}
                    className="space-y-6"
                  >
                    <h3 className="text-xl font-semibold mb-6 text-white flex items-center gap-3">
                      <BarChart3 className="w-6 h-6 text-purple-400" />
                      Visual Insights
                    </h3>
                    <ComparisonStats
                      data={comparison.visualizationData}
                      candidateAnalysis={comparison.comparisonResults?.candidateAnalysis}
                      metadata={undefined}
                      headToHeadComparisons={comparison.comparisonResults?.headToHeadComparisons}
                    />
                  </motion.div>
                )}

                {activeSection === 'recommendations' && (
                  <motion.div
                    key="recommendations"
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    exit={{ opacity: 0, x: 20 }}
                    className="space-y-6"
                  >
                    <h3 className="text-xl font-semibold mb-6 text-white flex items-center gap-3">
                      <TrendingUp className="w-6 h-6 text-purple-400" />
                      Recommendations
                    </h3>

                    {comparison.comparisonResults?.recommendations ? (
                      <div className="grid grid-cols-1 lg:grid-cols-3 gap-4">
                        {/* Top Choice Card */}
                        {comparison.comparisonResults.recommendations.topChoice && (
                          <motion.div
                            initial={{ opacity: 0, y: 10 }}
                            animate={{ opacity: 1, y: 0 }}
                            transition={{ delay: 0.1 }}
                            className="bg-gradient-to-br from-white/10 to-white/5 backdrop-blur-sm border border-white/10 rounded-xl p-5 space-y-3 flex flex-col h-full"
                          >
                            {/* Header */}
                            <div className="flex items-center justify-between">
                              <div className="flex items-center gap-3">
                                <div className="w-12 h-12 rounded-full flex items-center justify-center bg-gradient-to-br from-purple-600 to-pink-600">
                                  <Award className="w-6 h-6 text-white" />
                                </div>
                                <div>
                                  <h4 className="text-lg font-bold text-white">
                                    Recommended Choice
                                  </h4>
                                  <p className="text-xs text-white/70">Top Candidate</p>
                                </div>
                              </div>
                            </div>

                            {/* Content */}
                            <div className="flex-1 space-y-3">
                              <div className="bg-purple-500/10 rounded-lg p-3 border border-purple-400/20">
                                <p className="text-sm font-semibold text-purple-300 mb-1">
                                  Selected Candidate
                                </p>
                                <p className="text-sm text-white">
                                  {
                                    comparison.comparisonResults.recommendations.topChoice
                                      .candidateId
                                  }
                                </p>
                              </div>

                              {comparison.comparisonResults.recommendations.topChoice.reasoning && (
                                <div>
                                  <p className="text-sm font-semibold text-white/90 mb-2">
                                    Reasoning
                                  </p>
                                  <p className="text-sm text-white/80 leading-relaxed">
                                    {
                                      comparison.comparisonResults.recommendations.topChoice
                                        .reasoning
                                    }
                                  </p>
                                </div>
                              )}
                            </div>
                          </motion.div>
                        )}

                        {/* Hiring Strategy Card */}
                        {comparison.comparisonResults.recommendations.hiringStrategy && (
                          <motion.div
                            initial={{ opacity: 0, y: 10 }}
                            animate={{ opacity: 1, y: 0 }}
                            transition={{ delay: 0.2 }}
                            className="bg-gradient-to-br from-white/10 to-white/5 backdrop-blur-sm border border-white/10 rounded-xl p-5 space-y-3 flex flex-col h-full"
                          >
                            {/* Header */}
                            <div className="flex items-center justify-between">
                              <div className="flex items-center gap-3">
                                <div className="w-12 h-12 rounded-full flex items-center justify-center bg-gradient-to-br from-blue-600 to-cyan-600">
                                  <Briefcase className="w-6 h-6 text-white" />
                                </div>
                                <div>
                                  <h4 className="text-lg font-bold text-white">Hiring Strategy</h4>
                                  <p className="text-xs text-white/70">Recommended Approach</p>
                                </div>
                              </div>
                            </div>

                            {/* Content */}
                            <div className="flex-1">
                              <p className="text-sm text-white/80 leading-relaxed">
                                {comparison.comparisonResults.recommendations.hiringStrategy}
                              </p>
                            </div>
                          </motion.div>
                        )}

                        {/* Alternative Scenarios Card */}
                        {comparison.comparisonResults.recommendations.alternativeScenarios &&
                          comparison.comparisonResults.recommendations.alternativeScenarios.length >
                            0 && (
                            <motion.div
                              initial={{ opacity: 0, y: 10 }}
                              animate={{ opacity: 1, y: 0 }}
                              transition={{ delay: 0.3 }}
                              className="bg-gradient-to-br from-white/10 to-white/5 backdrop-blur-sm border border-white/10 rounded-xl p-5 space-y-3 flex flex-col h-full"
                            >
                              {/* Header */}
                              <div className="flex items-center justify-between">
                                <div className="flex items-center gap-3">
                                  <div className="w-12 h-12 rounded-full flex items-center justify-center bg-gradient-to-br from-green-600 to-emerald-600">
                                    <Lightbulb className="w-6 h-6 text-white" />
                                  </div>
                                  <div>
                                    <h4 className="text-lg font-bold text-white">
                                      Alternative Options
                                    </h4>
                                    <p className="text-xs text-white/70">Other Scenarios</p>
                                  </div>
                                </div>
                              </div>

                              {/* Content */}
                              <div className="flex-1 space-y-3">
                                {comparison.comparisonResults.recommendations.alternativeScenarios.map(
                                  (scenario: any, idx: number) => (
                                    <div
                                      key={idx}
                                      className="bg-green-500/10 rounded-lg p-3 border border-green-400/20 space-y-2"
                                    >
                                      <p className="text-sm font-semibold text-green-300">
                                        {scenario.scenario}
                                      </p>
                                      <p className="text-xs text-white/70">{scenario.reasoning}</p>
                                      <p className="text-xs text-green-400 font-medium">
                                        Best fit: {scenario.recommendedCandidate}
                                      </p>
                                    </div>
                                  )
                                )}
                              </div>
                            </motion.div>
                          )}
                      </div>
                    ) : (
                      <p className="text-white/60">No recommendations available</p>
                    )}
                  </motion.div>
                )}
              </AnimatePresence>
            </motion.div>
          </motion.div>
        </>
      )}
    </AnimatePresence>
  );
};
