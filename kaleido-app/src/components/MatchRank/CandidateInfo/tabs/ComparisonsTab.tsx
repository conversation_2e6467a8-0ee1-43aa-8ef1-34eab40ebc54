'use client';

import apiHelper from '@/lib/apiHelper';
import { useQuery } from '@tanstack/react-query';
import { motion } from 'framer-motion';
import { Calendar, GitCompare, Loader2, Users, TrendingUp, Award, Clock, ChevronRight } from 'lucide-react';
import React, { useState } from 'react';
import { ComparisonDetailModal } from './comparisons/ComparisonDetailModal';

interface ComparisonsTabProps {
  candidateId: string;
  jobId: string;
}

interface Comparison {
  id: string;
  comparisonTitle: string;
  comparisonType: string;
  status: 'pending' | 'completed' | 'failed';
  createdAt: string;
  candidateIds: string[];
  comparisonResults?: {
    summary: string;
    executiveSummary?: string;
    detailedComparison?: {
      [candidateId: string]: any;
    };
    candidateAnalysis?: {
      [candidateId: string]: any;
    };
    recommendations?: any;
    tradeOffs?: Array<{
      candidateA: string;
      candidateB: string;
      comparison: string;
    }>;
    headToHeadComparisons?: Array<{
      candidate1: string;
      candidate2: string;
      keyDifference: string;
      recommendation: string;
    }>;
    criticalConsiderations?: string[];
  };
  visualizationData?: any;
  metadata?: any;
}

export const ComparisonsTab: React.FC<ComparisonsTabProps> = ({ candidateId, jobId }) => {
  const [selectedComparison, setSelectedComparison] = useState<Comparison | null>(null);
  const [showDetailModal, setShowDetailModal] = useState(false);

  // Fetch comparisons for this job
  const {
    data: comparisons,
    isLoading,
    error,
  } = useQuery({
    queryKey: ['comparisons', jobId],
    queryFn: async () => {
      const response = await apiHelper.get<Comparison[]>(`/comparisons/jobs/${jobId}`);
      return response;
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
  });

  // Filter comparisons that include this candidate
  const candidateComparisons =
    comparisons?.filter(comp => comp.candidateIds.includes(candidateId)) || [];

  // Get the latest comparison
  const latestComparison = candidateComparisons[0];

  // Calculate stats
  const totalComparisons = candidateComparisons.length;
  const completedComparisons = candidateComparisons.filter(c => c.status === 'completed').length;
  const avgCandidatesPerComparison = candidateComparisons.length > 0
    ? Math.round(candidateComparisons.reduce((acc, c) => acc + c.candidateIds.length, 0) / candidateComparisons.length)
    : 0;

  if (isLoading) {
    return (
      <div className="flex items-center justify-center py-12">
        <Loader2 className="w-8 h-8 animate-spin text-purple-600" />
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center py-12">
        <p className="text-red-500">Failed to load comparisons</p>
      </div>
    );
  }

  if (candidateComparisons.length === 0) {
    return (
      <div className="text-center py-12">
        <GitCompare className="w-16 h-16 mx-auto mb-4 text-gray-400" />
        <h3 className="text-lg font-semibold mb-2">No Comparisons Yet</h3>
        <p className="text-muted-foreground max-w-md mx-auto">
          This candidate hasn't been included in any comparisons yet. Select multiple candidates to
          create a comparison.
        </p>
      </div>
    );
  }

  const handleViewComparison = (comparison: Comparison) => {
    setSelectedComparison(comparison);
    setShowDetailModal(true);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'text-green-400 bg-green-400/10 border-green-400/20';
      case 'pending':
        return 'text-yellow-400 bg-yellow-400/10 border-yellow-400/20';
      case 'failed':
        return 'text-red-400 bg-red-400/10 border-red-400/20';
      default:
        return 'text-gray-400 bg-gray-400/10 border-gray-400/20';
    }
  };

  return (
    <div className="space-y-6">
      {/* Hero Section with Header Image */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3 }}
        className="relative h-48 rounded-xl overflow-hidden"
      >
        {/* Background image with gradient overlay */}
        <div 
          className="absolute inset-0 bg-cover bg-center"
          style={{ 
            backgroundImage: `url('/images/landing/exclusive-2.webp')`,
          }}
        />
        <div className="absolute inset-0 bg-gradient-to-r from-purple-900/90 via-purple-800/70 to-transparent" />
        <div className="absolute inset-0 bg-gradient-to-t from-slate-900 via-transparent to-transparent" />
        
        {/* Content over image */}
        <div className="relative z-10 h-full flex items-end justify-between p-6">
          <div>
            <h2 className="text-2xl font-bold text-white mb-2">Comparison Analysis</h2>
            <p className="text-white/80 text-sm">
              Track and review all candidate comparisons
            </p>
          </div>
          
          {/* Stats on the right */}
          <div className="flex items-center gap-6 text-xs">
            <div className="text-right">
              <div className="text-2xl font-bold text-white">{totalComparisons}</div>
              <div className="text-white/60">Total Comparisons</div>
            </div>
            <div className="text-right">
              <div className="text-2xl font-bold text-green-400">{completedComparisons}</div>
              <div className="text-white/60">Completed</div>
            </div>
            <div className="text-right">
              <div className="text-2xl font-bold text-purple-400">{avgCandidatesPerComparison}</div>
              <div className="text-white/60">Avg Candidates</div>
            </div>
          </div>
        </div>
      </motion.div>

      {/* Latest Comparison Card */}
      {latestComparison && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, delay: 0.1 }}
          className="relative bg-gradient-to-br from-purple-600/10 to-pink-600/5 backdrop-blur-sm rounded-xl border border-purple-400/20 p-6 overflow-hidden cursor-pointer hover:border-purple-400/40 transition-all"
          onClick={() => handleViewComparison(latestComparison)}
        >
          {/* Background decoration */}
          <div className="absolute top-0 right-0 w-32 h-32 bg-purple-500/10 rounded-full blur-3xl" />
          
          <div className="relative z-10">
            <div className="flex items-start justify-between mb-4">
              <div>
                <div className="flex items-center gap-3 mb-2">
                  <div className="w-10 h-10 rounded-full bg-gradient-to-br from-purple-600 to-pink-600 flex items-center justify-center">
                    <Award className="w-5 h-5 text-white" />
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold text-white">Latest Comparison</h3>
                    <p className="text-xs text-white/60">Most recent analysis</p>
                  </div>
                </div>
                <h4 className="text-base font-medium text-white/90 mb-1">
                  {latestComparison.comparisonTitle}
                </h4>
              </div>
              
              <span className={`px-2.5 py-1 rounded-full text-xs font-medium border ${getStatusColor(latestComparison.status)}`}>
                {latestComparison.status}
              </span>
            </div>

            <div className="flex items-center gap-4 text-sm">
              <div className="flex items-center gap-1.5 text-white/70">
                <Users className="w-4 h-4 text-purple-400" />
                <span>{latestComparison.candidateIds.length} candidates</span>
              </div>
              <div className="flex items-center gap-1.5 text-white/70">
                <Calendar className="w-4 h-4 text-blue-400" />
                <span>{new Date(latestComparison.createdAt).toLocaleDateString('en-US', { 
                  month: 'short', 
                  day: 'numeric',
                  year: 'numeric'
                })}</span>
              </div>
            </div>

            {latestComparison.comparisonResults?.executiveSummary && (
              <p className="mt-3 text-sm text-white/70 line-clamp-2">
                {latestComparison.comparisonResults.executiveSummary}
              </p>
            )}

            <div className="mt-4 flex items-center gap-2 text-sm font-medium text-purple-400">
              <span>View Full Analysis</span>
              <ChevronRight className="w-4 h-4" />
            </div>
          </div>
        </motion.div>
      )}

      {/* Comparison History */}
      {candidateComparisons.length > 1 && (
        <div className="space-y-3">
          <h3 className="text-base font-semibold text-white flex items-center gap-2">
            <Clock className="w-4 h-4 text-purple-400" />
            Previous Comparisons
          </h3>

          <div className="bg-white/[0.02] backdrop-blur-sm rounded-lg border border-white/5 overflow-hidden">
            {candidateComparisons.slice(1).map((comparison, index) => (
              <motion.div
                key={comparison.id}
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.3, delay: index * 0.05 }}
                className="px-5 py-4 hover:bg-white/[0.03] transition-all cursor-pointer border-b border-white/[0.03] last:border-b-0"
                onClick={() => handleViewComparison(comparison)}
              >
                <div className="flex items-center justify-between">
                  {/* Title Column */}
                  <div className="flex-1 min-w-0 pr-4">
                    <div className="flex items-center gap-2">
                      <GitCompare className="w-4 h-4 text-purple-400/70" />
                      <h4 className="text-sm font-medium text-white/90 truncate">
                        {comparison.comparisonTitle}
                      </h4>
                    </div>
                  </div>

                  {/* Status Column */}
                  <div className="flex items-center justify-center px-4">
                    <span className={`px-2.5 py-1 rounded-full text-xs font-medium border ${getStatusColor(comparison.status)}`}>
                      {comparison.status}
                    </span>
                  </div>

                  {/* Candidates Column */}
                  <div className="flex items-center gap-1.5 px-4 min-w-[120px]">
                    <Users className="w-3.5 h-3.5 text-purple-400" />
                    <span className="text-xs text-purple-300 font-medium">
                      {comparison.candidateIds.length} candidates
                    </span>
                  </div>

                  {/* Date Column */}
                  <div className="flex items-center gap-1.5 px-4 min-w-[100px]">
                    <Calendar className="w-3.5 h-3.5 text-blue-400" />
                    <span className="text-xs text-blue-300 font-medium">
                      {new Date(comparison.createdAt).toLocaleDateString('en-US', { 
                        month: 'short', 
                        day: 'numeric'
                      })}
                    </span>
                  </div>

                  {/* Action Column */}
                  <div className="pl-4">
                    <ChevronRight className="w-4 h-4 text-white/30 group-hover:text-white/50 transition-colors" />
                  </div>
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      )}

      {/* Detail Modal */}
      {selectedComparison && (
        <ComparisonDetailModal
          isOpen={showDetailModal}
          onClose={() => {
            setShowDetailModal(false);
            setSelectedComparison(null);
          }}
          comparison={selectedComparison}
        />
      )}
    </div>
  );
};