import { Building, Check, Cpu, Eye, Feather, Layout, Paintbrush, Rocket } from 'lucide-react';
import React from 'react';

interface IconBasedLayoutSettingsProps {
  currentLayout?: string;
  onLayoutChange?: (layout: string) => void;
}

const LAYOUT_OPTIONS = [
  {
    id: 'modern',
    name: 'Modern Corporate',
    icon: Layout,
    color: 'bg-blue-500/20 border border-blue-500/10',
    iconColor: 'text-blue-400',
    gradient: 'from-blue-500/20 to-blue-600/20',
  },
  {
    id: 'startup',
    name: 'Startup Vibe',
    icon: Rocket,
    color: 'bg-purple-500/20 border border-purple-500/10',
    iconColor: 'text-purple-400',
    gradient: 'from-purple-500/20 to-purple-600/20',
  },
  {
    id: 'minimal',
    name: 'Minimalist',
    icon: Feather,
    color: 'bg-gray-500/20 border border-gray-500/10',
    iconColor: 'text-gray-400',
    gradient: 'from-gray-500/20 to-gray-600/20',
  },
  {
    id: 'tech',
    name: 'Tech Forward',
    icon: Cpu,
    color: 'bg-green-500/20 border border-green-500/10',
    iconColor: 'text-green-400',
    gradient: 'from-green-500/20 to-green-600/20',
  },
  {
    id: 'creative',
    name: 'Creative Studio',
    icon: Paintbrush,
    color: 'bg-pink-500/20 border border-pink-500/10',
    iconColor: 'text-pink-400',
    gradient: 'from-pink-500/20 to-pink-600/20',
  },
  {
    id: 'enterprise',
    name: 'Enterprise',
    icon: Building,
    color: 'bg-indigo-500/20 border border-indigo-500/10',
    iconColor: 'text-indigo-400',
    gradient: 'from-indigo-500/20 to-indigo-600/20',
  },
];

export default function IconBasedLayoutSettings({
  currentLayout = 'modern',
  onLayoutChange,
}: IconBasedLayoutSettingsProps) {
  const handleLayoutSelect = (layoutId: string) => {
    if (onLayoutChange) {
      onLayoutChange(layoutId);
    }
  };

  return (
    <div className="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-6 gap-3">
      {LAYOUT_OPTIONS.map(option => {
        const IconComponent = option.icon;
        const isSelected = currentLayout === option.id;

        return (
          <button
            key={option.id}
            type="button"
            onClick={() => handleLayoutSelect(option.id)}
            className={`
              relative group flex flex-col items-center gap-3 p-4 rounded-xl transition-all duration-300 backdrop-blur-sm
              ${
                isSelected
                  ? 'border border-white/20 shadow-lg shadow-black/10 scale-105'
                  : 'border border-white/5 hover:border-white/10 hover:scale-102'
              }
            `}
          >
            {/* Selected indicator */}
            {isSelected && (
              <div className="absolute -top-2 -right-2 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full p-1 shadow-lg">
                <Check className="w-3 h-3 text-white" />
              </div>
            )}

            {/* Icon */}
            <div
              className={`p-3 rounded-xl ${option.color} transition-all duration-300 backdrop-blur-sm`}
            >
              <IconComponent className={`w-6 h-6 ${option.iconColor}`} />
            </div>

            {/* Label */}
            <span
              className={`text-xs text-center font-semibold line-clamp-1 transition-colors duration-300 ${
                isSelected ? 'text-white' : 'text-white/80'
              }`}
            >
              {option.name}
            </span>

            {/* Preview on hover */}
            <div className="absolute bottom-full mb-3 left-1/2 -translate-x-1/2 px-3 py-2 bg-black/80 backdrop-blur-sm text-white text-xs rounded-lg opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-300 whitespace-nowrap shadow-xl z-20 border border-white/10">
              <Eye className="w-3 h-3 inline mr-1" />
              Preview {option.name}
              <div className="absolute top-full left-1/2 -translate-x-1/2 -mt-1 w-2 h-2 bg-black/80 rotate-45 border-r border-b border-white/10" />
            </div>
          </button>
        );
      })}
    </div>
  );
}
