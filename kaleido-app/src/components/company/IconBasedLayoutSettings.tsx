import { Building, Check, Cpu, Eye, Feather, Layout, Paintbrush, Rocket } from 'lucide-react';
import React from 'react';

interface IconBasedLayoutSettingsProps {
  currentLayout?: string;
  onLayoutChange?: (layout: string) => void;
}

const LAYOUT_OPTIONS = [
  {
    id: 'modern',
    name: 'Modern Corporate',
    icon: Layout,
    color: 'bg-blue-500/10',
    iconColor: 'text-blue-500',
  },
  {
    id: 'startup',
    name: 'Startup Vibe',
    icon: Rocket,
    color: 'bg-purple-500/10',
    iconColor: 'text-purple-500',
  },
  {
    id: 'minimal',
    name: 'Minimalist',
    icon: Feather,
    color: 'bg-gray-500/10',
    iconColor: 'text-gray-500',
  },
  {
    id: 'tech',
    name: 'Tech Forward',
    icon: Cpu,
    color: 'bg-green-500/10',
    iconColor: 'text-green-500',
  },
  {
    id: 'creative',
    name: 'Creative Studio',
    icon: Paintbrush,
    color: 'bg-pink-500/10',
    iconColor: 'text-pink-500',
  },
  {
    id: 'enterprise',
    name: 'Enterprise',
    icon: Building,
    color: 'bg-indigo-500/10',
    iconColor: 'text-indigo-500',
  },
];

export default function IconBasedLayoutSettings({
  currentLayout = 'modern',
  onLayoutChange,
}: IconBasedLayoutSettingsProps) {
  const handleLayoutSelect = (layoutId: string) => {
    if (onLayoutChange) {
      onLayoutChange(layoutId);
    }
  };

  return (
    <div className="grid grid-cols-3 sm:grid-cols-6 gap-2">
      {LAYOUT_OPTIONS.map((option) => {
        const IconComponent = option.icon;
        const isSelected = currentLayout === option.id;
        
        return (
          <button
            key={option.id}
            onClick={() => handleLayoutSelect(option.id)}
            className={`
              relative group flex flex-col items-center gap-2 p-3 rounded-lg transition-all duration-200
              ${isSelected 
                ? 'bg-primary/10 border border-primary/30 shadow-sm' 
                : 'bg-card/50 hover:bg-card/70 border border-border/10 hover:border-border/20'
              }
            `}
          >
            {/* Selected indicator */}
            {isSelected && (
              <div className="absolute -top-1 -right-1 bg-primary rounded-full p-0.5">
                <Check className="w-3 h-3 text-primary-foreground" />
              </div>
            )}
            
            {/* Icon */}
            <div className={`p-2 rounded-md ${option.color} transition-colors`}>
              <IconComponent className={`w-5 h-5 ${option.iconColor}`} />
            </div>
            
            {/* Label */}
            <span className="text-xs text-center font-medium line-clamp-1">
              {option.name}
            </span>
            
            {/* Preview on hover */}
            <div className="absolute bottom-full mb-2 left-1/2 -translate-x-1/2 px-2 py-1 bg-popover text-popover-foreground text-xs rounded-md opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 whitespace-nowrap shadow-lg z-10">
              <Eye className="w-3 h-3 inline mr-1" />
              Preview
            </div>
          </button>
        );
      })}
    </div>
  );
}