/**
 * Unit tests for AuthStateHandler component
 * Tests graceful handling of unauthenticated users and authentication errors
 */

import { PUBLIC_ROUTES } from '@/types/publicRoutes';
import { act, waitFor } from '@testing-library/react';
import { render } from '@/test-utils';
import { useRouter } from 'next/navigation';
import AuthStateHandler from '../AuthStateHandler';
import { showToast } from '../Toaster';

// Mock dependencies
jest.mock('next/navigation', () => ({
  useRouter: jest.fn(),
  usePathname: jest.fn(),
}));

jest.mock('@/hooks/useUser', () => ({
  useUser: jest.fn(),
}));

jest.mock('../Toaster', () => ({
  showToast: jest.fn(),
}));

// Mock window.location
const mockLocation = {
  pathname: '/dashboard',
  search: '',
};
Object.defineProperty(window, 'location', {
  value: mockLocation,
  writable: true,
});

// Mock localStorage
const localStorageMock = {
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
  clear: jest.fn(),
};
Object.defineProperty(window, 'localStorage', {
  value: localStorageMock,
  configurable: true,
});

// Mock addEventListener and removeEventListener
const mockAddEventListener = jest.fn();
const mockRemoveEventListener = jest.fn();
Object.defineProperty(window, 'addEventListener', {
  value: mockAddEventListener,
});
Object.defineProperty(window, 'removeEventListener', {
  value: mockRemoveEventListener,
});

describe('AuthStateHandler', () => {
  const mockPush = jest.fn();
  const mockUseRouter = useRouter as jest.MockedFunction<typeof useRouter>;
  const mockUsePathname = require('next/navigation').usePathname as jest.MockedFunction<any>;
  const mockUseUser = require('@/hooks/useUser').useUser as jest.MockedFunction<any>;
  const mockShowToast = showToast as jest.MockedFunction<typeof showToast>;

  beforeEach(() => {
    jest.clearAllMocks();
    mockUseRouter.mockReturnValue({
      push: mockPush,
      back: jest.fn(),
      forward: jest.fn(),
      refresh: jest.fn(),
      replace: jest.fn(),
      prefetch: jest.fn(),
    });

    mockUsePathname.mockReturnValue('/dashboard');

    mockLocation.pathname = '/dashboard';
    mockLocation.search = '';

    // Clear localStorage mock
    localStorageMock.getItem.mockClear();
    localStorageMock.setItem.mockClear();
    localStorageMock.removeItem.mockClear();
    localStorageMock.clear.mockClear();
  });

  it('should render children when user is authenticated', () => {
    mockUseUser.mockReturnValue({
      user: { sub: 'user123', email: '<EMAIL>' },
      isLoading: false,
      error: undefined,
      checkSession: jest.fn(),
    });

    const { getByText } = render(
      <AuthStateHandler>
        <div>Protected Content</div>
      </AuthStateHandler>
    );

    expect(getByText('Protected Content')).toBeInTheDocument();
    expect(mockShowToast).not.toHaveBeenCalled();
    expect(mockPush).not.toHaveBeenCalled();
  });

  it('should not show warning while loading', () => {
    mockUseUser.mockReturnValue({
      user: undefined,
      isLoading: true,
      error: undefined,
      checkSession: jest.fn(),
    });

    render(
      <AuthStateHandler>
        <div>Protected Content</div>
      </AuthStateHandler>
    );

    expect(mockShowToast).not.toHaveBeenCalled();
    expect(mockPush).not.toHaveBeenCalled();
  });

  it('should handle authentication errors gracefully', async () => {
    const authError = new Error('Authentication failed');
    mockUseUser.mockReturnValue({
      user: undefined,
      isLoading: false,
      error: authError,
      checkSession: jest.fn(),
    });

    render(
      <AuthStateHandler>
        <div>Protected Content</div>
      </AuthStateHandler>
    );

    await waitFor(() => {
      expect(mockShowToast).toHaveBeenCalledWith({
        message: 'Authentication error occurred. Please try logging in again.',
        isSuccess: false,
      });
    });

    // Should redirect to login after delay
    await waitFor(
      () => {
        expect(mockPush).toHaveBeenCalledWith('/api/auth/login');
      },
      { timeout: 3000 }
    );
  });

  it('should handle unauthenticated users on protected routes', async () => {
    mockUseUser.mockReturnValue({
      user: undefined,
      isLoading: false,
      error: undefined,
      checkSession: jest.fn(),
    });

    mockLocation.pathname = '/dashboard';

    render(
      <AuthStateHandler>
        <div>Protected Content</div>
      </AuthStateHandler>
    );

    await waitFor(() => {
      expect(mockShowToast).toHaveBeenCalledWith({
        message: 'Please log in to access this page',
        isSuccess: false,
      });
    });

    await waitFor(
      () => {
        expect(mockPush).toHaveBeenCalledWith('/api/auth/login?returnTo=%2Fdashboard');
      },
      { timeout: 2000 }
    );
  });

  it('should not redirect on public routes', () => {
    mockUseUser.mockReturnValue({
      user: undefined,
      isLoading: false,
      error: undefined,
      checkSession: jest.fn(),
    });

    mockLocation.pathname = PUBLIC_ROUTES.ROOT;

    render(
      <AuthStateHandler>
        <div>Public Content</div>
      </AuthStateHandler>
    );

    expect(mockShowToast).not.toHaveBeenCalled();
    expect(mockPush).not.toHaveBeenCalled();
  });

  it('should not redirect on auth routes', () => {
    mockUseUser.mockReturnValue({
      user: undefined,
      isLoading: false,
      error: undefined,
      checkSession: jest.fn(),
    });

    mockLocation.pathname = '/api/auth/login';

    render(
      <AuthStateHandler>
        <div>Auth Content</div>
      </AuthStateHandler>
    );

    expect(mockShowToast).not.toHaveBeenCalled();
    expect(mockPush).not.toHaveBeenCalled();
  });

  it('should not redirect on company-profile routes', () => {
    mockUseUser.mockReturnValue({
      user: undefined,
      isLoading: false,
      error: undefined,
      checkSession: jest.fn(),
    });

    mockLocation.pathname = `${PUBLIC_ROUTES.COMPANY_PROFILE}/some-company`;

    render(
      <AuthStateHandler>
        <div>Company Profile Content</div>
      </AuthStateHandler>
    );

    expect(mockShowToast).not.toHaveBeenCalled();
    expect(mockPush).not.toHaveBeenCalled();
  });

  it('should not redirect on open-jobs routes', () => {
    mockUseUser.mockReturnValue({
      user: undefined,
      isLoading: false,
      error: undefined,
      checkSession: jest.fn(),
    });

    mockLocation.pathname = PUBLIC_ROUTES.OPEN_JOBS;

    render(
      <AuthStateHandler>
        <div>Open Jobs Content</div>
      </AuthStateHandler>
    );

    expect(mockShowToast).not.toHaveBeenCalled();
    expect(mockPush).not.toHaveBeenCalled();
  });

  it('should include return URL in login redirect', async () => {
    mockUseUser.mockReturnValue({
      user: undefined,
      isLoading: false,
      error: undefined,
      checkSession: jest.fn(),
    });

    mockLocation.pathname = '/match-rank';
    mockLocation.search = '?jobId=123';

    render(
      <AuthStateHandler>
        <div>Protected Content</div>
      </AuthStateHandler>
    );

    await waitFor(
      () => {
        expect(mockPush).toHaveBeenCalledWith(
          '/api/auth/login?returnTo=%2Fmatch-rank%3FjobId%3D123'
        );
      },
      { timeout: 2000 }
    );
  });

  it('should listen for storage changes', () => {
    mockUseUser.mockReturnValue({
      user: { sub: 'user123', email: '<EMAIL>' },
      isLoading: false,
      error: undefined,
      checkSession: jest.fn(),
    });

    render(
      <AuthStateHandler>
        <div>Protected Content</div>
      </AuthStateHandler>
    );

    expect(mockAddEventListener).toHaveBeenCalledWith('storage', expect.any(Function));
  });

  it('should handle auth session cleared event', () => {
    mockUseUser.mockReturnValue({
      user: { sub: 'user123', email: '<EMAIL>' },
      isLoading: false,
      error: undefined,
      checkSession: jest.fn(),
    });

    let storageHandler: (e: StorageEvent) => void;
    mockAddEventListener.mockImplementation((event, handler) => {
      if (event === 'storage') {
        storageHandler = handler;
      }
    });

    render(
      <AuthStateHandler>
        <div>Protected Content</div>
      </AuthStateHandler>
    );

    // Simulate auth session being cleared
    act(() => {
      storageHandler!({
        key: 'auth_session',
        newValue: null,
        oldValue: '{"accessToken":"token"}',
      } as StorageEvent);
    });

    expect(mockShowToast).toHaveBeenCalledWith({
      message: 'Your session has ended. Please log in again.',
      isSuccess: false,
    });
  });

  it('should reset warning flag when user authenticates', () => {
    // Start with no user
    const { rerender } = render(
      <AuthStateHandler>
        <div>Content</div>
      </AuthStateHandler>
    );

    // Mock user becoming authenticated
    mockUseUser.mockReturnValue({
      user: { sub: 'user123', email: '<EMAIL>' },
      isLoading: false,
      error: undefined,
      checkSession: jest.fn(),
    });

    rerender(
      <AuthStateHandler>
        <div>Content</div>
      </AuthStateHandler>
    );

    // Should not show duplicate warnings
    expect(mockShowToast).toHaveBeenCalledTimes(0);
  });

  it('should cleanup event listeners on unmount', () => {
    mockUseUser.mockReturnValue({
      user: { sub: 'user123', email: '<EMAIL>' },
      isLoading: false,
      error: undefined,
      checkSession: jest.fn(),
    });

    const { unmount } = render(
      <AuthStateHandler>
        <div>Protected Content</div>
      </AuthStateHandler>
    );

    unmount();

    expect(mockRemoveEventListener).toHaveBeenCalledWith('storage', expect.any(Function));
  });

  it('should not show multiple warnings for the same issue', async () => {
    mockUseUser.mockReturnValue({
      user: undefined,
      isLoading: false,
      error: new Error('Auth error'),
      checkSession: jest.fn(),
    });

    const { rerender } = render(
      <AuthStateHandler>
        <div>Content</div>
      </AuthStateHandler>
    );

    await waitFor(() => {
      expect(mockShowToast).toHaveBeenCalledTimes(1);
    });

    // Re-render with same error
    rerender(
      <AuthStateHandler>
        <div>Content</div>
      </AuthStateHandler>
    );

    // Should not show toast again
    expect(mockShowToast).toHaveBeenCalledTimes(1);
  });
});
