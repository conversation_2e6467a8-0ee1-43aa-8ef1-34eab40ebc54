{"compilerOptions": {"target": "es2017", "lib": ["dom", "dom.iterable", "es2015", "es2017", "esnext"], "allowJs": true, "skipLibCheck": true, "strict": false, "noImplicitAny": false, "strictNullChecks": false, "strictFunctionTypes": false, "strictBindCallApply": false, "strictPropertyInitialization": false, "noImplicitThis": true, "alwaysStrict": false, "useUnknownInCatchVariables": false, "noUnusedLocals": false, "noUnusedParameters": false, "noImplicitReturns": false, "noFallthroughCasesInSwitch": true, "noUncheckedIndexedAccess": false, "noPropertyAccessFromIndexSignature": false, "exactOptionalPropertyTypes": false, "noEmit": true, "esModuleInterop": true, "module": "esnext", "moduleResolution": "bundler", "resolveJsonModule": true, "isolatedModules": true, "jsx": "preserve", "incremental": true, "baseUrl": ".", "paths": {"@/*": ["src/*"]}, "experimentalDecorators": true, "emitDecoratorMetadata": true, "plugins": [{"name": "next"}], "allowArbitraryExtensions": true, "allowImportingTsExtensions": true}, "include": ["next-env.d.ts", "**/*.ts", "**/*.tsx", ".next/types/**/*.ts"], "exclude": ["node_modules", "kaleido-app/**/*", "headstart_backend/**/*", "kaleido-website/**/*", "**/__tests__/**/*", "**/*.test.ts", "**/*.test.tsx", "**/*.spec.ts", "**/*.spec.tsx", "**/*_backup.tsx", "**/*_backup.ts"], "typeRoots": ["./node_modules/@types"]}